"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7044],{57152:function(e,t,a){var o=a(57437),r=a(2265),i=a(33145),s=a(43886);t.Z=e=>{let{src:t,alt:a,width:n,height:d,fill:c=!1,sizes:l=c?"(max-width: 768px) 100vw, 50vw":void 0,priority:f=!1,className:u="",animate:m=!0,style:b={}}=e,[p,y]=(0,r.useState)(!0),[g,x]=(0,r.useState)(!1);return(0,o.jsxs)("div",{className:"relative overflow-hidden ".concat(u),style:{minHeight:c?"100%":void 0,height:c?"100%":void 0,...b},onMouseEnter:()=>x(!0),onMouseLeave:()=>x(!1),children:[p&&(0,o.jsx)(s.E.div,{className:"absolute inset-0 bg-[#f4f3f0]",initial:{opacity:1},animate:{opacity:[.5,.8,.5],backgroundPosition:["0% 0%","100% 100%"]},transition:{opacity:{duration:1.5,repeat:1/0,ease:"easeInOut"},backgroundPosition:{duration:1.5,repeat:1/0,ease:"easeInOut"}},style:{background:"linear-gradient(90deg, #f4f3f0, #e5e2d9, #f4f3f0)",backgroundSize:"200% 100%"}}),(0,o.jsx)(s.E.div,{className:"w-full h-full",animate:m&&g?{scale:1.05,filter:"brightness(1.1)"}:{scale:1,filter:"brightness(1)"},transition:{duration:.7,ease:"easeInOut"},children:(0,o.jsx)(i.default,{src:t,alt:a,width:n,height:d,fill:c,sizes:l,priority:f,className:"\n            ".concat(p?"opacity-0":"opacity-100"," \n            transition-opacity duration-500\n            ").concat(c?"object-cover":"","\n          "),onLoad:()=>y(!1)})})]})}},29456:function(e,t,a){a(57437),a(2265)},12381:function(e,t,a){a.d(t,{z:function(){return d}});var o=a(57437);a(2265);var r=a(37053),i=a(90535),s=a(93448);let n=(0,i.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-[#2c2c27] text-[#f4f3f0] shadow-xs hover:bg-[#3d3d35]",destructive:"bg-[#ff4d4f] text-white shadow-xs hover:bg-[#ff4d4f]/90 focus-visible:ring-[#ff4d4f]/20 dark:focus-visible:ring-[#ff4d4f]/40",outline:"border border-[#e5e2d9] bg-[#f8f8f5] shadow-xs hover:bg-[#f4f3f0] hover:text-[#2c2c27]",secondary:"bg-[#e5e2d9] text-[#2c2c27] shadow-xs hover:bg-[#e5e2d9]/80",ghost:"hover:bg-[#f4f3f0] hover:text-[#2c2c27]",link:"text-[#2c2c27] underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:a,size:i,asChild:d=!1,...c}=e,l=d?r.g7:"button";return(0,o.jsx)(l,{"data-slot":"button",className:(0,s.cn)(n({variant:a,size:i,className:t})),...c})}},40279:function(e,t,a){a.d(t,{I:function(){return s}});var o=a(57437),r=a(2265),i=a(93448);let s=r.forwardRef((e,t)=>{let{className:a,type:r,...s}=e;return(0,o.jsx)("input",{type:r,"data-slot":"input",className:(0,i.cn)("border-[#e5e2d9] file:text-[#2c2c27] placeholder:text-[#8a8778] selection:bg-[#2c2c27] selection:text-[#f4f3f0] flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-[#8a8778] focus-visible:ring-[#8a8778]/50 focus-visible:ring-[3px]","aria-invalid:ring-[#ff4d4f]/20 dark:aria-invalid:ring-[#ff4d4f]/40 aria-invalid:border-[#ff4d4f]",a),ref:t,...s})});s.displayName="Input"},29658:function(e,t,a){var o=a(57437),r=a(29),i=a.n(r);a(2265),t.Z=e=>{let{size:t="md",color:a="#2c2c27",className:r=""}=e,s={sm:{container:"w-6 h-6",dot:"w-1 h-1"},md:{container:"w-10 h-10",dot:"w-1.5 h-1.5"},lg:{container:"w-16 h-16",dot:"w-2 h-2"}};return(0,o.jsxs)("div",{className:"jsx-cba83ab4e8da42d9 "+"flex items-center justify-center ".concat(r),children:[(0,o.jsxs)("div",{className:"jsx-cba83ab4e8da42d9 "+"relative ".concat(s[t].container),children:[(0,o.jsx)("div",{style:{backgroundColor:a,animation:"loaderDot1 1.5s infinite"},className:"jsx-cba83ab4e8da42d9 "+"absolute top-0 left-1/2 -translate-x-1/2 ".concat(s[t].dot," rounded-full")}),(0,o.jsx)("div",{style:{backgroundColor:a,animation:"loaderDot2 1.5s infinite"},className:"jsx-cba83ab4e8da42d9 "+"absolute top-1/2 right-0 -translate-y-1/2 ".concat(s[t].dot," rounded-full")}),(0,o.jsx)("div",{style:{backgroundColor:a,animation:"loaderDot3 1.5s infinite"},className:"jsx-cba83ab4e8da42d9 "+"absolute bottom-0 left-1/2 -translate-x-1/2 ".concat(s[t].dot," rounded-full")}),(0,o.jsx)("div",{style:{backgroundColor:a,animation:"loaderDot4 1.5s infinite"},className:"jsx-cba83ab4e8da42d9 "+"absolute top-1/2 left-0 -translate-y-1/2 ".concat(s[t].dot," rounded-full")}),(0,o.jsx)("div",{style:{border:"2px solid ".concat(a),borderTopColor:"transparent",animation:"loaderRotate 1s linear infinite"},className:"jsx-cba83ab4e8da42d9 absolute inset-0 rounded-full"})]}),(0,o.jsx)(i(),{id:"cba83ab4e8da42d9",children:"@-webkit-keyframes loaderRotate{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-moz-keyframes loaderRotate{0%{-moz-transform:rotate(0deg);transform:rotate(0deg)}100%{-moz-transform:rotate(360deg);transform:rotate(360deg)}}@-o-keyframes loaderRotate{0%{-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-o-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes loaderRotate{0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}@-webkit-keyframes loaderDot1{0%,100%{opacity:.2}25%{opacity:1}}@-moz-keyframes loaderDot1{0%,100%{opacity:.2}25%{opacity:1}}@-o-keyframes loaderDot1{0%,100%{opacity:.2}25%{opacity:1}}@keyframes loaderDot1{0%,100%{opacity:.2}25%{opacity:1}}@-webkit-keyframes loaderDot2{0%,100%{opacity:.2}50%{opacity:1}}@-moz-keyframes loaderDot2{0%,100%{opacity:.2}50%{opacity:1}}@-o-keyframes loaderDot2{0%,100%{opacity:.2}50%{opacity:1}}@keyframes loaderDot2{0%,100%{opacity:.2}50%{opacity:1}}@-webkit-keyframes loaderDot3{0%,100%{opacity:.2}75%{opacity:1}}@-moz-keyframes loaderDot3{0%,100%{opacity:.2}75%{opacity:1}}@-o-keyframes loaderDot3{0%,100%{opacity:.2}75%{opacity:1}}@keyframes loaderDot3{0%,100%{opacity:.2}75%{opacity:1}}@-webkit-keyframes loaderDot4{0%,100%{opacity:1}50%{opacity:.2}}@-moz-keyframes loaderDot4{0%,100%{opacity:1}50%{opacity:.2}}@-o-keyframes loaderDot4{0%,100%{opacity:1}50%{opacity:.2}}@keyframes loaderDot4{0%,100%{opacity:1}50%{opacity:.2}}"})]})}},71917:function(e,t,a){a.d(t,{ToastProvider:function(){return u},p:function(){return m}});var o=a(57437),r=a(2265),i=a(43886),s=a(48131),n=a(65302),d=a(22252),c=a(33245),l=a(32489);let f=(0,r.createContext)(void 0);function u(e){let{children:t}=e,[a,i]=(0,r.useState)([]);return(0,o.jsxs)(f.Provider,{value:{toasts:a,addToast:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"info",a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3e3,o=Math.random().toString(36).substring(2,9);i(r=>[...r,{id:o,message:e,type:t,duration:a}])},removeToast:e=>{i(t=>t.filter(t=>t.id!==e))}},children:[t,(0,o.jsx)(p,{})]})}function m(){let e=(0,r.useContext)(f);if(void 0===e)throw Error("useToast must be used within a ToastProvider");return e}function b(e){let{toast:t,onRemove:a}=e;return(0,r.useEffect)(()=>{if(t.duration){let e=setTimeout(()=>{a()},t.duration);return()=>clearTimeout(e)}},[t.duration,a]),(0,o.jsxs)(i.E.div,{initial:{opacity:0,y:-50},animate:{opacity:1,y:0},exit:{opacity:0,x:300},className:"flex items-center p-4 rounded-lg border shadow-lg ".concat((()=>{switch(t.type){case"success":return"bg-[#f4f3f0] border-[#8a8778]";case"error":return"bg-red-50 border-red-200";default:return"bg-[#f8f8f5] border-[#e5e2d9]"}})()," max-w-md"),children:[(0,o.jsx)(()=>{switch(t.type){case"success":return(0,o.jsx)(n.Z,{className:"h-5 w-5"});case"error":return(0,o.jsx)(d.Z,{className:"h-5 w-5"});default:return(0,o.jsx)(c.Z,{className:"h-5 w-5"})}},{}),(0,o.jsx)("span",{className:"ml-3 text-sm font-medium flex-1",children:t.message}),(0,o.jsx)("button",{onClick:a,className:"ml-4 text-gray-400 hover:text-gray-600",children:(0,o.jsx)(l.Z,{className:"h-4 w-4"})})]})}function p(){let{toasts:e,removeToast:t}=m();return(0,o.jsx)("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:(0,o.jsx)(s.M,{children:e.map(e=>(0,o.jsx)(b,{toast:e,onRemove:()=>t(e.id)},e.id))})})}},3697:function(e,t,a){var o=a(2265),r=a(6658);t.Z=function(e,t){let{setLoading:a,setVariant:i}=(0,r.r)();(0,o.useEffect)(()=>{a(e),t&&i(t)},[e,t,a,i])}}}]);