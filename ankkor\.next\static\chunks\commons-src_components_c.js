"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["commons-src_components_c"],{

/***/ "(app-pages-browser)/./src/components/cart/CartProvider.tsx":
/*!**********************************************!*\
  !*** ./src/components/cart/CartProvider.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CartProvider: function() { return /* binding */ CartProvider; },\n/* harmony export */   useCart: function() { return /* binding */ useCart; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_localCartStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/localCartStore */ \"(app-pages-browser)/./src/lib/localCartStore.ts\");\n/* __next_internal_client_entry_do_not_use__ useCart,CartProvider,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n// Create context with default values\nconst CartContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Custom hook to use cart context\nconst useCart = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(CartContext);\n    if (context === undefined) {\n        throw new Error(\"useCart must be used within a CartProvider\");\n    }\n    return context;\n};\n_s(useCart, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst CartProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const cartStore = (0,_lib_localCartStore__WEBPACK_IMPORTED_MODULE_2__.useLocalCartStore)();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const openCart = ()=>setIsOpen(true);\n    const closeCart = ()=>setIsOpen(false);\n    const toggleCart = ()=>setIsOpen((prevState)=>!prevState);\n    const value = {\n        openCart,\n        closeCart,\n        toggleCart,\n        isOpen,\n        itemCount: cartStore.itemCount\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CartContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\cart\\\\CartProvider.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(CartProvider, \"qtYp7aTllpMo11bnbmOX8RspG7w=\", false, function() {\n    return [\n        _lib_localCartStore__WEBPACK_IMPORTED_MODULE_2__.useLocalCartStore\n    ];\n});\n_c = CartProvider;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CartProvider);\nvar _c;\n$RefreshReg$(_c, \"CartProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/cart/CartProvider.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/providers/CustomerProvider.tsx":
/*!*******************************************************!*\
  !*** ./src/components/providers/CustomerProvider.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomerProvider: function() { return /* binding */ CustomerProvider; },\n/* harmony export */   useCustomer: function() { return /* binding */ useCustomer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_clientAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/clientAuth */ \"(app-pages-browser)/./src/lib/clientAuth.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/auth */ \"(app-pages-browser)/./src/lib/auth.ts\");\n/* harmony import */ var _components_ui_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/toast */ \"(app-pages-browser)/./src/components/ui/toast.tsx\");\n/* harmony import */ var _lib_wooStore__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/wooStore */ \"(app-pages-browser)/./src/lib/wooStore.ts\");\n/* harmony import */ var _hooks_useAuthCartSync__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useAuthCartSync */ \"(app-pages-browser)/./src/hooks/useAuthCartSync.ts\");\n/* __next_internal_client_entry_do_not_use__ useCustomer,CustomerProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n// Create the context\nconst CustomerContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    customer: null,\n    isLoading: true,\n    isAuthenticated: false,\n    token: null,\n    login: async ()=>{},\n    register: async ()=>{},\n    logout: ()=>{},\n    updateProfile: async ()=>{},\n    error: null,\n    refreshCustomer: async ()=>{}\n});\n// Custom hook to use the customer context\nconst useCustomer = ()=>{\n    _s();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(CustomerContext);\n};\n_s(useCustomer, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\n// Provider component\nfunction CustomerProvider(param) {\n    let { children } = param;\n    _s1();\n    const [customer, setCustomer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { addToast } = (0,_components_ui_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    // Initialize auth cart sync\n    (0,_hooks_useAuthCartSync__WEBPACK_IMPORTED_MODULE_7__.useAuthCartSync)();\n    // Transform customer data to ensure it has all required fields\n    const transformCustomerData = (customerData)=>{\n        if (!customerData) return null;\n        return {\n            ...customerData,\n            displayName: customerData.displayName || customerData.username || \"\".concat(customerData.firstName || \"\", \" \").concat(customerData.lastName || \"\").trim() || \"User\"\n        };\n    };\n    // Check authentication and get customer data from API\n    const checkAuthAndGetCustomer = async ()=>{\n        try {\n            console.log(\"CustomerProvider: Checking authentication via /api/auth/me\");\n            const response = await fetch(\"/api/auth/me\", {\n                method: \"GET\",\n                credentials: \"include\"\n            });\n            console.log(\"CustomerProvider: Auth API response status:\", response.status);\n            const result = await response.json();\n            console.log(\"CustomerProvider: Auth API result:\", result);\n            if (result.success && result.customer) {\n                // Get token from API response (more secure than client-side cookies)\n                const apiToken = result.token;\n                console.log(\"CustomerProvider: Token from API response:\", !!apiToken);\n                setToken(apiToken || null);\n                return {\n                    success: true,\n                    customer: result.customer,\n                    token: apiToken\n                };\n            } else {\n                // Clear token if authentication failed\n                setToken(null);\n                return {\n                    success: false,\n                    message: result.message || \"Not authenticated\"\n                };\n            }\n        } catch (error) {\n            console.error(\"CustomerProvider: Error checking authentication:\", error);\n            // Clear token on error\n            setToken(null);\n            return {\n                success: false,\n                message: \"Network error\"\n            };\n        }\n    };\n    // Refresh customer data\n    const refreshCustomer = async ()=>{\n        try {\n            const result = await checkAuthAndGetCustomer();\n            if (result.success) {\n                // Add token to customer object\n                const customerWithToken = {\n                    ...result.customer,\n                    token: result.token\n                };\n                setCustomer(transformCustomerData(customerWithToken));\n                console.log(\"Customer data refreshed successfully\");\n                console.log(\"Token available after refresh:\", !!result.token);\n            } else {\n                console.log(\"Failed to refresh customer data:\", result.message);\n                setCustomer(null);\n                setToken(null);\n            }\n        } catch (err) {\n            console.error(\"Error refreshing customer data:\", err);\n            setCustomer(null);\n            setToken(null);\n        }\n    };\n    // Check if the customer is logged in on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkCustomerSession = async ()=>{\n            try {\n                setIsLoading(true);\n                // Check authentication and get customer data from API\n                const result = await checkAuthAndGetCustomer();\n                if (result.success) {\n                    console.log(\"Found valid authentication, customer data loaded\");\n                    console.log(\"Token available on mount:\", !!result.token);\n                    // Add token to customer object\n                    const customerWithToken = {\n                        ...result.customer,\n                        token: result.token\n                    };\n                    setCustomer(transformCustomerData(customerWithToken));\n                } else {\n                    console.log(\"No valid authentication found:\", result.message);\n                    setCustomer(null);\n                    setToken(null);\n                }\n            } catch (err) {\n                console.error(\"Error checking customer session:\", err);\n                // On error, clear any potentially corrupted session data\n                (0,_lib_clientAuth__WEBPACK_IMPORTED_MODULE_3__.logout)();\n                setCustomer(null);\n                setToken(null);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        checkCustomerSession();\n    }, []);\n    // Logout function\n    const logout = ()=>{\n        (0,_lib_clientAuth__WEBPACK_IMPORTED_MODULE_3__.logout)();\n        setCustomer(null);\n        setToken(null);\n        console.log(\"Logout completed, token cleared\");\n        // Clear the cart data when the user logs out\n        const cartStore = _lib_wooStore__WEBPACK_IMPORTED_MODULE_6__.useCartStore.getState();\n        // Clear the cart store to ensure all cart data is reset\n        cartStore.clearCart().catch((error)=>{\n            console.error(\"Error clearing cart during logout:\", error);\n        // Even if clearing cart fails, we should still proceed with local cleanup\n        });\n        // Reset cart initialization indicator in sessionStorage\n        if (true) {\n            sessionStorage.removeItem(\"cartInitialized\");\n            // Ensure we clean up any error states\n            sessionStorage.removeItem(\"cartInitializationAttempts\");\n        }\n        // Show info toast notification\n        addToast(\"You have been signed out successfully\", \"info\");\n        router.push(\"/\");\n        router.refresh(); // Refresh to update UI based on auth state\n    };\n    // Parse and handle Shopify authentication errors with more specific messages\n    const parseAuthError = (error)=>{\n        if (!error) return \"An unknown error occurred\";\n        const errorMessage = typeof error === \"string\" ? error : error.message || JSON.stringify(error);\n        // Common Shopify customer auth errors\n        if (errorMessage.includes(\"Unidentified customer\")) {\n            return \"The email or password you entered is incorrect. Please try again.\";\n        }\n        if (errorMessage.includes(\"already associated\")) {\n            return \"An account with this email already exists. Please sign in instead.\";\n        }\n        if (errorMessage.includes(\"password\") && errorMessage.includes(\"too short\")) {\n            return \"Your password must be at least 8 characters. Please try again.\";\n        }\n        if (errorMessage.includes(\"token\") && (errorMessage.includes(\"expired\") || errorMessage.includes(\"invalid\"))) {\n            return \"Your login session has expired. Please sign in again.\";\n        }\n        if (errorMessage.includes(\"network\") || errorMessage.includes(\"failed to fetch\")) {\n            return \"Network connection issue. Please check your internet connection and try again.\";\n        }\n        // Return the original error if no specific handling\n        return errorMessage;\n    };\n    // Enhanced login function with better error handling\n    const login = async (credentials)=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            const result = await (0,_lib_clientAuth__WEBPACK_IMPORTED_MODULE_3__.login)(credentials.email, credentials.password);\n            if (!result || !result.success || !result.user) {\n                throw new Error(\"Login failed: No user data returned\");\n            }\n            // Convert user to customer format and include the token\n            const customer = {\n                id: result.user.id,\n                databaseId: result.user.databaseId,\n                email: result.user.email,\n                firstName: result.user.firstName,\n                lastName: result.user.lastName,\n                token: result.token // Include token in customer object\n            };\n            setCustomer(transformCustomerData(customer));\n            // Also set token in separate state for backward compatibility\n            const loginToken = result.token;\n            console.log(\"Login successful, token from API:\", !!loginToken);\n            setToken(loginToken || null);\n            // Initialize a fresh cart after login to ensure it's associated with the customer\n            const cartStore = _lib_wooStore__WEBPACK_IMPORTED_MODULE_6__.useCartStore.getState();\n            try {\n                await cartStore.clearCart();\n                // Initialize cart with the correct method\n                await cartStore.initializeCart();\n            } catch (cartError) {\n                console.error(\"Error initializing cart after login:\", cartError);\n            // Don't fail the login process if cart initialization fails\n            }\n            // Show success toast notification\n            addToast(\"Welcome back, \".concat((customer === null || customer === void 0 ? void 0 : customer.firstName) || \"there\", \"!\"), \"success\");\n            // Redirect to homepage instead of account page\n            router.push(\"/\");\n        } catch (err) {\n            const errorMessage = parseAuthError(err);\n            setError(errorMessage);\n            // Show error toast notification\n            addToast(errorMessage, \"error\");\n            throw err;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Enhanced register function with better error handling\n    const register = async (registration)=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            var _result_customer;\n            const result = await (0,_lib_clientAuth__WEBPACK_IMPORTED_MODULE_3__.register)(registration.email, registration.firstName, registration.lastName, registration.password);\n            if (!result || !result.success || !result.customer) {\n                throw new Error(\"Registration failed: No customer data returned\");\n            }\n            // Add token to customer object\n            const customerWithToken = {\n                ...result.customer,\n                token: result.token\n            };\n            setCustomer(transformCustomerData(customerWithToken));\n            // Also set token in separate state for backward compatibility\n            const registrationToken = result.token;\n            console.log(\"Registration successful, token from API:\", !!registrationToken);\n            setToken(registrationToken || null);\n            // Initialize a fresh cart after registration to ensure it's associated with the customer\n            const cartStore = _lib_wooStore__WEBPACK_IMPORTED_MODULE_6__.useCartStore.getState();\n            try {\n                await cartStore.clearCart();\n                // Initialize cart with the correct method\n                await cartStore.initializeCart();\n            } catch (cartError) {\n                console.error(\"Error initializing cart after registration:\", cartError);\n            // Don't fail the registration process if cart initialization fails\n            }\n            // Show success toast notification\n            addToast(\"Welcome to Ankkor, \".concat((_result_customer = result.customer) === null || _result_customer === void 0 ? void 0 : _result_customer.firstName, \"!\"), \"success\");\n            // Redirect to homepage instead of account page\n            router.push(\"/\");\n        } catch (err) {\n            const errorMessage = parseAuthError(err);\n            setError(errorMessage);\n            // Show error toast notification\n            addToast(errorMessage, \"error\");\n            throw err;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Function to update customer profile\n    const updateProfile = async (data)=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            const result = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_4__.updateCustomerProfile)(data);\n            if (!result || !result.customer) {\n                throw new Error(\"Profile update failed: No customer data returned\");\n            }\n            // Update the customer state with the new data\n            setCustomer(transformCustomerData(result.customer));\n            // Show success toast notification\n            addToast(\"Your profile has been updated successfully\", \"success\");\n            return result;\n        } catch (err) {\n            const errorMessage = parseAuthError(err);\n            setError(errorMessage);\n            // Show error toast notification\n            addToast(errorMessage, \"error\");\n            throw err;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Calculate isAuthenticated from customer data\n    const isAuthenticated = Boolean(customer);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomerContext.Provider, {\n        value: {\n            customer,\n            isLoading,\n            isAuthenticated,\n            token,\n            login,\n            register,\n            logout,\n            updateProfile,\n            error,\n            refreshCustomer\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\providers\\\\CustomerProvider.tsx\",\n        lineNumber: 390,\n        columnNumber: 5\n    }, this);\n}\n_s1(CustomerProvider, \"d8osDVg46zesMvhrb3jJMZtqux4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _components_ui_toast__WEBPACK_IMPORTED_MODULE_5__.useToast,\n        _hooks_useAuthCartSync__WEBPACK_IMPORTED_MODULE_7__.useAuthCartSync\n    ];\n});\n_c = CustomerProvider;\nvar _c;\n$RefreshReg$(_c, \"CustomerProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/providers/CustomerProvider.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/providers/LaunchingSoonProvider.tsx":
/*!************************************************************!*\
  !*** ./src/components/providers/LaunchingSoonProvider.tsx ***!
  \************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LaunchingSoonProvider: function() { return /* binding */ LaunchingSoonProvider; },\n/* harmony export */   useLaunchingSoon: function() { return /* binding */ useLaunchingSoon; },\n/* harmony export */   useLaunchingSoonStore: function() { return /* binding */ useLaunchingSoonStore; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n/* __next_internal_client_entry_do_not_use__ useLaunchingSoonStore,useLaunchingSoon,LaunchingSoonProvider,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Create a Zustand store with persistence and proper SSR handling\nconst useLaunchingSoonStore = (0,zustand__WEBPACK_IMPORTED_MODULE_2__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_3__.persist)((set)=>({\n        // In production, use the NEXT_PUBLIC_LAUNCHING_SOON env var; in development, default to true\n        isLaunchingSoon:  false ? 0 : true,\n        setIsLaunchingSoon: (isLaunchingSoon)=>{\n            // In production, only allow changes if explicitly configured\n            if (false) {}\n            set({\n                isLaunchingSoon\n            });\n        }\n    }), {\n    name: \"ankkor-launch-state\",\n    // Add proper SSR handling\n    storage: {\n        getItem: (name)=>{\n            if (false) {}\n            try {\n                return localStorage.getItem(name);\n            } catch (error) {\n                console.error(\"localStorage.getItem error:\", error);\n                return null;\n            }\n        },\n        setItem: (name, value)=>{\n            if (false) {}\n            try {\n                localStorage.setItem(name, value);\n            } catch (error) {\n                console.error(\"localStorage.setItem error:\", error);\n            }\n        },\n        removeItem: (name)=>{\n            if (false) {}\n            try {\n                localStorage.removeItem(name);\n            } catch (error) {\n                console.error(\"localStorage.removeItem error:\", error);\n            }\n        }\n    }\n}));\nconst LaunchingSoonContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useLaunchingSoon = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LaunchingSoonContext);\n    if (context === undefined) {\n        throw new Error(\"useLaunchingSoon must be used within a LaunchingSoonProvider\");\n    }\n    return context;\n};\n_s(useLaunchingSoon, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst LaunchingSoonProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    // Use the Zustand store to provide the context\n    const store = useLaunchingSoonStore();\n    // We need to handle hydration mismatches in Next.js\n    const [isHydrated, setIsHydrated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsHydrated(true);\n        // In production, ensure the state matches the environment variable\n        if (false) {}\n    }, [\n        store\n    ]);\n    // Always render children to prevent hydration mismatches\n    // The individual components will handle their own hydration state\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LaunchingSoonContext.Provider, {\n        value: store,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\providers\\\\LaunchingSoonProvider.tsx\",\n        lineNumber: 107,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(LaunchingSoonProvider, \"fECSBiy19/Gb+BXn4xcYA7BciS8=\", false, function() {\n    return [\n        useLaunchingSoonStore\n    ];\n});\n_c = LaunchingSoonProvider;\n/* harmony default export */ __webpack_exports__[\"default\"] = (LaunchingSoonProvider);\nvar _c;\n$RefreshReg$(_c, \"LaunchingSoonProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/providers/LaunchingSoonProvider.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/providers/LoadingProvider.tsx":
/*!******************************************************!*\
  !*** ./src/components/providers/LoadingProvider.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoadingProvider: function() { return /* binding */ LoadingProvider; },\n/* harmony export */   useLoading: function() { return /* binding */ useLoading; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_PageLoading__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/PageLoading */ \"(app-pages-browser)/./src/components/ui/PageLoading.tsx\");\n/* __next_internal_client_entry_do_not_use__ useLoading,LoadingProvider,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\nconst LoadingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    isLoading: false,\n    setLoading: ()=>{},\n    variant: \"thread\",\n    setVariant: ()=>{}\n});\nconst useLoading = ()=>{\n    _s();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LoadingContext);\n};\n_s(useLoading, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\n// Map paths to specific loader variants for a more tailored experience\nconst pathVariantMap = {\n    \"/collection\": \"fabric\",\n    \"/collection/shirts\": \"fabric\",\n    \"/collection/polos\": \"fabric\",\n    \"/product\": \"thread\",\n    \"/about\": \"button\",\n    \"/customer-service\": \"button\",\n    \"/account\": \"thread\",\n    \"/wishlist\": \"thread\"\n};\n// Separate component that uses useSearchParams\nconst RouteChangeHandler = (param)=>{\n    let { setIsLoading, setVariant } = param;\n    _s1();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    // Set loading state and variant when route changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Start loading\n        setIsLoading(true);\n        // Determine the appropriate variant based on the path\n        const basePathname = \"/\" + pathname.split(\"/\")[1];\n        const newVariant = pathVariantMap[basePathname] || pathVariantMap[pathname] || \"thread\";\n        setVariant(newVariant);\n        // Simulate loading delay (remove in production and rely on actual loading time)\n        const timer = setTimeout(()=>{\n            setIsLoading(false);\n        }, 1200);\n        return ()=>clearTimeout(timer);\n    }, [\n        pathname,\n        searchParams,\n        setIsLoading,\n        setVariant\n    ]);\n    return null;\n};\n_s1(RouteChangeHandler, \"h6p6PpCFmP4Mu5bIMduBzSZThBE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = RouteChangeHandler;\n// Loading fallback component\nconst LoadingFallback = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"hidden\",\n        children: \"Loading route...\"\n    }, void 0, false, {\n        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\providers\\\\LoadingProvider.tsx\",\n        lineNumber: 71,\n        columnNumber: 31\n    }, undefined);\n_c1 = LoadingFallback;\nconst LoadingProvider = (param)=>{\n    let { children } = param;\n    _s2();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [variant, setVariant] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"thread\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingContext.Provider, {\n        value: {\n            isLoading,\n            setLoading: setIsLoading,\n            variant,\n            setVariant\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingFallback, {}, void 0, false, {\n                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\providers\\\\LoadingProvider.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 27\n                }, void 0),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RouteChangeHandler, {\n                    setIsLoading: setIsLoading,\n                    setVariant: setVariant\n                }, void 0, false, {\n                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\providers\\\\LoadingProvider.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\providers\\\\LoadingProvider.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, undefined),\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageLoading__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isLoading: isLoading,\n                variant: variant\n            }, void 0, false, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\providers\\\\LoadingProvider.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\providers\\\\LoadingProvider.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, undefined);\n};\n_s2(LoadingProvider, \"oWTpoJhdp4nnyJ9KhdVnjoT8a/4=\");\n_c2 = LoadingProvider;\n/* harmony default export */ __webpack_exports__[\"default\"] = (LoadingProvider);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"RouteChangeHandler\");\n$RefreshReg$(_c1, \"LoadingFallback\");\n$RefreshReg$(_c2, \"LoadingProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/providers/LoadingProvider.tsx\n"));

/***/ })

}]);