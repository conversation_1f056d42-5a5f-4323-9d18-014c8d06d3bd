(()=>{var e={};e.id=3696,e.ids=[3696],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},43247:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>l}),s(14636),s(51806),s(12523);var r=s(23191),o=s(88716),a=s(37922),n=s.n(a),i=s(95231),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);s.d(t,c);let l=["",{children:["woocommerce-test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,14636)),"E:\\ankkorwoo\\ankkor\\src\\app\\woocommerce-test\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,51806)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],d=["E:\\ankkorwoo\\ankkor\\src\\app\\woocommerce-test\\page.tsx"],m="/woocommerce-test/page",u={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/woocommerce-test/page",pathname:"/woocommerce-test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},9332:(e,t,s)=>{Promise.resolve().then(s.bind(s,32457))},14636:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(19510);s(71159);var o=s(55361);function a(){return(0,r.jsxs)("div",{className:"container mx-auto py-12",children:[r.jsx("h1",{className:"text-3xl font-bold mb-8",children:"WooCommerce Authentication Test"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-12",children:[(0,r.jsxs)("div",{className:"bg-white p-6 shadow-md",children:[r.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Login Form"}),r.jsx(o.Z,{mode:"login",redirectUrl:"/woocommerce-test/success"})]}),(0,r.jsxs)("div",{className:"bg-white p-6 shadow-md",children:[r.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Register Form"}),r.jsx(o.Z,{mode:"register",redirectUrl:"/woocommerce-test/success"})]})]}),(0,r.jsxs)("div",{className:"mt-12 bg-gray-50 p-6 border border-gray-200",children:[r.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Note"}),r.jsx("p",{children:"This page is used to test the authentication with WooCommerce using client-safe auth functions."}),r.jsx("p",{className:"mt-2",children:"After successful login or registration, you will be redirected to the success page."})]})]})}},55361:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\auth\AuthForm.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[8948,2344,5010,4154],()=>s(43247));module.exports=r})();