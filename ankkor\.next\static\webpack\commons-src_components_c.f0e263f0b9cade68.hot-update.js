"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("commons-src_components_c",{

/***/ "(app-pages-browser)/./src/components/cart/CartProvider.tsx":
/*!**********************************************!*\
  !*** ./src/components/cart/CartProvider.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CartProvider: function() { return /* binding */ CartProvider; },\n/* harmony export */   useCart: function() { return /* binding */ useCart; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_localCartStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/localCartStore */ \"(app-pages-browser)/./src/lib/localCartStore.ts\");\n/* harmony import */ var _Cart__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Cart */ \"(app-pages-browser)/./src/components/cart/Cart.tsx\");\n/* __next_internal_client_entry_do_not_use__ useCart,CartProvider,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Create context with default values\nconst CartContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Custom hook to use cart context\nconst useCart = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(CartContext);\n    if (context === undefined) {\n        throw new Error(\"useCart must be used within a CartProvider\");\n    }\n    return context;\n};\n_s(useCart, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst CartProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const cartStore = (0,_lib_localCartStore__WEBPACK_IMPORTED_MODULE_2__.useLocalCartStore)();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const openCart = ()=>setIsOpen(true);\n    const closeCart = ()=>setIsOpen(false);\n    const toggleCart = ()=>setIsOpen((prevState)=>!prevState);\n    const value = {\n        openCart,\n        closeCart,\n        toggleCart,\n        isOpen,\n        itemCount: cartStore.itemCount\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CartContext.Provider, {\n        value: value,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Cart__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: value.isOpen,\n                toggleCart: value.toggleCart\n            }, void 0, false, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\cart\\\\CartProvider.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\cart\\\\CartProvider.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(CartProvider, \"qtYp7aTllpMo11bnbmOX8RspG7w=\", false, function() {\n    return [\n        _lib_localCartStore__WEBPACK_IMPORTED_MODULE_2__.useLocalCartStore\n    ];\n});\n_c = CartProvider;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CartProvider);\nvar _c;\n$RefreshReg$(_c, \"CartProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/cart/CartProvider.tsx\n"));

/***/ })

});