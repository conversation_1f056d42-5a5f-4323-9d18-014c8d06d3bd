"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_components_LaunchingStateServer_tsx";
exports.ids = ["_rsc_src_components_LaunchingStateServer_tsx"];
exports.modules = {

/***/ "(rsc)/./src/components/LaunchingStateInitializer.tsx":
/*!******************************************************!*\
  !*** ./src/components/LaunchingStateInitializer.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\LaunchingStateInitializer.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/LaunchingStateServer.tsx":
/*!*************************************************!*\
  !*** ./src/components/LaunchingStateServer.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LaunchingStateServer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _LaunchingStateInitializer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./LaunchingStateInitializer */ \"(rsc)/./src/components/LaunchingStateInitializer.tsx\");\n\n\n\n/**\r\n * Server component to get the launching state from environment variables\r\n * This component will always read the most current value from the server environment\r\n */ function LaunchingStateServer() {\n    // Read the environment variable from the server side\n    // Convert string 'true'/'false' to boolean\n    const launchingState = \"false\" === \"true\";\n    // Pass the value to the client component\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LaunchingStateInitializer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        launchingState: launchingState\n    }, void 0, false, {\n        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\LaunchingStateServer.tsx\",\n        lineNumber: 14,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9MYXVuY2hpbmdTdGF0ZVNlcnZlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUEwQjtBQUMwQztBQUVwRTs7O0NBR0MsR0FDYyxTQUFTRTtJQUN0QixxREFBcUQ7SUFDckQsMkNBQTJDO0lBQzNDLE1BQU1DLGlCQUFpQkMsT0FBc0MsS0FBSztJQUVsRSx5Q0FBeUM7SUFDekMscUJBQU8sOERBQUNILGtFQUF5QkE7UUFBQ0UsZ0JBQWdCQTs7Ozs7O0FBQ3BEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW5ra29yLy4vc3JjL2NvbXBvbmVudHMvTGF1bmNoaW5nU3RhdGVTZXJ2ZXIudHN4P2Y5MDEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IExhdW5jaGluZ1N0YXRlSW5pdGlhbGl6ZXIgZnJvbSAnLi9MYXVuY2hpbmdTdGF0ZUluaXRpYWxpemVyJztcclxuXHJcbi8qKlxyXG4gKiBTZXJ2ZXIgY29tcG9uZW50IHRvIGdldCB0aGUgbGF1bmNoaW5nIHN0YXRlIGZyb20gZW52aXJvbm1lbnQgdmFyaWFibGVzXHJcbiAqIFRoaXMgY29tcG9uZW50IHdpbGwgYWx3YXlzIHJlYWQgdGhlIG1vc3QgY3VycmVudCB2YWx1ZSBmcm9tIHRoZSBzZXJ2ZXIgZW52aXJvbm1lbnRcclxuICovXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExhdW5jaGluZ1N0YXRlU2VydmVyKCkge1xyXG4gIC8vIFJlYWQgdGhlIGVudmlyb25tZW50IHZhcmlhYmxlIGZyb20gdGhlIHNlcnZlciBzaWRlXHJcbiAgLy8gQ29udmVydCBzdHJpbmcgJ3RydWUnLydmYWxzZScgdG8gYm9vbGVhblxyXG4gIGNvbnN0IGxhdW5jaGluZ1N0YXRlID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfTEFVTkNISU5HX1NPT04gPT09ICd0cnVlJztcclxuXHJcbiAgLy8gUGFzcyB0aGUgdmFsdWUgdG8gdGhlIGNsaWVudCBjb21wb25lbnRcclxuICByZXR1cm4gPExhdW5jaGluZ1N0YXRlSW5pdGlhbGl6ZXIgbGF1bmNoaW5nU3RhdGU9e2xhdW5jaGluZ1N0YXRlfSAvPjtcclxufSJdLCJuYW1lcyI6WyJSZWFjdCIsIkxhdW5jaGluZ1N0YXRlSW5pdGlhbGl6ZXIiLCJMYXVuY2hpbmdTdGF0ZVNlcnZlciIsImxhdW5jaGluZ1N0YXRlIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0xBVU5DSElOR19TT09OIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/components/LaunchingStateServer.tsx\n");

/***/ })

};
;