(()=>{var e={};e.id=4457,e.ids=[4457],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},45188:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>h,originalPathname:()=>g,pages:()=>f,routeModule:()=>p,tree:()=>m});var o=r(86514);r(51806),r(12523);var i=r(23191),a=r(88716),n=r(37922),l=r.n(n),c=r(95231),d={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>c[e]);r.d(t,d);var u=e([o]);o=(u.then?(await u)():u)[0];let m=["",{children:["categories",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,86514)),"E:\\ankkorwoo\\ankkor\\src\\app\\categories\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,51806)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],f=["E:\\ankkorwoo\\ankkor\\src\\app\\categories\\page.tsx"],g="/categories/page",h={require:r,loadChunk:()=>Promise.resolve()},p=new i.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/categories/page",pathname:"/categories",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:m}});s()}catch(e){s(e)}})},13417:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,12994,23)),Promise.resolve().then(r.t.bind(r,96114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,79671,23)),Promise.resolve().then(r.t.bind(r,41868,23)),Promise.resolve().then(r.t.bind(r,84759,23))},14504:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,92481,23)),Promise.resolve().then(r.t.bind(r,79404,23))},96799:(e,t,r)=>{Promise.resolve().then(r.bind(r,68897)),Promise.resolve().then(r.bind(r,75367))},54039:(e,t,r)=>{Promise.resolve().then(r.bind(r,83846))},83846:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(10326);r(17577);var o=r(33265);let i=()=>s.jsx("div",{className:"animate-pulse flex space-x-4",children:(0,s.jsxs)("div",{className:"flex-1 space-y-6 py-1",children:[s.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[s.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-2"}),s.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-1"})]}),s.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"})]})]})}),a=(0,o.default)(async()=>{},{loadableGenerated:{modules:["app\\not-found.tsx -> ./not-found-content"]},ssr:!1,loading:()=>s.jsx(i,{})});function n(){return s.jsx("div",{className:"container mx-auto py-20",children:s.jsx(a,{})})}},68897:(e,t,r)=>{"use strict";r.d(t,{CustomerProvider:()=>c,O:()=>l});var s=r(10326),o=r(17577),i=r(35047),a=r(75367);let n=(0,o.createContext)({customer:null,isLoading:!0,isAuthenticated:!1,token:null,login:async()=>{},register:async()=>{},logout:()=>{},updateProfile:async()=>{},error:null,refreshCustomer:async()=>{}}),l=()=>(0,o.useContext)(n);function c({children:e}){let[t,r]=(0,o.useState)(null),[l,c]=(0,o.useState)(!0),[d,u]=(0,o.useState)(null),[m,f]=(0,o.useState)(null),g=(0,i.useRouter)(),{addToast:h}=(0,a.p)(),p=e=>e?{...e,displayName:e.displayName||e.username||`${e.firstName||""} ${e.lastName||""}`.trim()||"User"}:null,x=async()=>{try{console.log("CustomerProvider: Checking authentication via /api/auth/me");let e=await fetch("/api/auth/me",{method:"GET",credentials:"include"});console.log("CustomerProvider: Auth API response status:",e.status);let t=await e.json();if(console.log("CustomerProvider: Auth API result:",t),!t.success||!t.customer)return f(null),{success:!1,message:t.message||"Not authenticated"};{let e=t.token;return console.log("CustomerProvider: Token from API response:",!!e),f(e||null),{success:!0,customer:t.customer,token:e}}}catch(e){return console.error("CustomerProvider: Error checking authentication:",e),f(null),{success:!1,message:"Network error"}}},v=async()=>{try{let e=await x();if(e.success){let t={...e.customer,token:e.token};r(p(t)),console.log("Customer data refreshed successfully"),console.log("Token available after refresh:",!!e.token)}else console.log("Failed to refresh customer data:",e.message),r(null),f(null)}catch(e){console.error("Error refreshing customer data:",e),r(null),f(null)}},b=async e=>{c(!0),u(null);try{throw Error("Login temporarily disabled for build fix")}catch(t){let e="Login temporarily disabled for build fix";throw u(e),h(e,"error"),t}finally{c(!1)}},y=async e=>{c(!0),u(null);try{throw Error("Register temporarily disabled for build fix")}catch(t){let e="Register temporarily disabled for build fix";throw u(e),h(e,"error"),t}finally{c(!1)}},w=async e=>{c(!0),u(null);try{throw Error("Profile update temporarily disabled for build fix")}catch(t){let e="Profile update temporarily disabled for build fix";throw u(e),h(e,"error"),t}finally{c(!1)}};return s.jsx(n.Provider,{value:{customer:t,isLoading:l,isAuthenticated:!!t,token:m,login:b,register:y,logout:()=>{r(null),f(null),console.log("Logout completed, token cleared"),h("You have been signed out successfully","info"),g.push("/"),g.refresh()},updateProfile:w,error:d,refreshCustomer:v},children:e})}},75367:(e,t,r)=>{"use strict";r.d(t,{ToastProvider:()=>m,p:()=>f});var s=r(10326),o=r(17577),i=r(92148),a=r(86462),n=r(54659),l=r(87888),c=r(18019),d=r(94019);let u=(0,o.createContext)(void 0);function m({children:e}){let[t,r]=(0,o.useState)([]);return(0,s.jsxs)(u.Provider,{value:{toasts:t,addToast:(e,t="info",s=3e3)=>{let o=Math.random().toString(36).substring(2,9);r(r=>[...r,{id:o,message:e,type:t,duration:s}])},removeToast:e=>{r(t=>t.filter(t=>t.id!==e))}},children:[e,s.jsx(h,{})]})}function f(){let e=(0,o.useContext)(u);if(void 0===e)throw Error("useToast must be used within a ToastProvider");return e}function g({toast:e,onRemove:t}){return(0,s.jsxs)(i.E.div,{initial:{opacity:0,y:-50},animate:{opacity:1,y:0},exit:{opacity:0,x:300},className:`flex items-center p-4 rounded-lg border shadow-lg ${(()=>{switch(e.type){case"success":return"bg-[#f4f3f0] border-[#8a8778]";case"error":return"bg-red-50 border-red-200";default:return"bg-[#f8f8f5] border-[#e5e2d9]"}})()} max-w-md`,children:[s.jsx(()=>{switch(e.type){case"success":return s.jsx(n.Z,{className:"h-5 w-5"});case"error":return s.jsx(l.Z,{className:"h-5 w-5"});default:return s.jsx(c.Z,{className:"h-5 w-5"})}},{}),s.jsx("span",{className:"ml-3 text-sm font-medium flex-1",children:e.message}),s.jsx("button",{onClick:t,className:"ml-4 text-gray-400 hover:text-gray-600",children:s.jsx(d.Z,{className:"h-4 w-4"})})]})}function h(){let{toasts:e,removeToast:t}=f();return s.jsx("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:s.jsx(a.M,{children:e.map(e=>s.jsx(g,{toast:e,onRemove:()=>t(e.id)},e.id))})})}},17710:(e,t,r)=>{"use strict";r.d(t,{default:()=>o.a});var s=r(66794),o=r.n(s)},57371:(e,t,r)=>{"use strict";r.d(t,{default:()=>o.a});var s=r(670),o=r.n(s)},10221:(e,t,r)=>{"use strict";let{createProxy:s}=r(68570);e.exports=s("E:\\ankkorwoo\\ankkor\\node_modules\\next\\dist\\client\\image-component.js")},670:(e,t,r)=>{"use strict";let{createProxy:s}=r(68570);e.exports=s("E:\\ankkorwoo\\ankkor\\node_modules\\next\\dist\\client\\link.js")},79241:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return n}}),r(96501);let s=r(95728),o=r(29472);function i(e){return void 0!==e.default}function a(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function n(e,t){var r,n;let l,c,d,{src:u,sizes:m,unoptimized:f=!1,priority:g=!1,loading:h,className:p,quality:x,width:v,height:b,fill:y=!1,style:w,overrideSrc:j,onLoad:P,onLoadingComplete:k,placeholder:N="empty",blurDataURL:_,fetchPriority:C,decoding:E="async",layout:S,objectFit:M,objectPosition:z,lazyBoundary:A,lazyRoot:O,...I}=e,{imgConf:G,showAltText:q,blurComplete:R,defaultLoader:T}=t,L=G||o.imageConfigDefault;if("allSizes"in L)l=L;else{let e=[...L.deviceSizes,...L.imageSizes].sort((e,t)=>e-t),t=L.deviceSizes.sort((e,t)=>e-t),s=null==(r=L.qualities)?void 0:r.sort((e,t)=>e-t);l={...L,allSizes:e,deviceSizes:t,qualities:s}}if(void 0===T)throw Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config");let D=I.loader||T;delete I.loader,delete I.srcSet;let F="__next_img_default"in D;if(F){if("custom"===l.loader)throw Error('Image with src "'+u+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader')}else{let e=D;D=t=>{let{config:r,...s}=t;return e(s)}}if(S){"fill"===S&&(y=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[S];e&&(w={...w,...e});let t={responsive:"100vw",fill:"100vw"}[S];t&&!m&&(m=t)}let B="",$=a(v),U=a(b);if("object"==typeof(n=u)&&(i(n)||void 0!==n.src)){let e=i(u)?u.default:u;if(!e.src)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e));if(!e.height||!e.width)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e));if(c=e.blurWidth,d=e.blurHeight,_=_||e.blurDataURL,B=e.src,!y){if($||U){if($&&!U){let t=$/e.width;U=Math.round(e.height*t)}else if(!$&&U){let t=U/e.height;$=Math.round(e.width*t)}}else $=e.width,U=e.height}}let W=!g&&("lazy"===h||void 0===h);(!(u="string"==typeof u?u:B)||u.startsWith("data:")||u.startsWith("blob:"))&&(f=!0,W=!1),l.unoptimized&&(f=!0),F&&u.endsWith(".svg")&&!l.dangerouslyAllowSVG&&(f=!0),g&&(C="high");let V=a(x),Z=Object.assign(y?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:M,objectPosition:z}:{},q?{}:{color:"transparent"},w),Y=R||"empty"===N?null:"blur"===N?'url("data:image/svg+xml;charset=utf-8,'+(0,s.getImageBlurSvg)({widthInt:$,heightInt:U,blurWidth:c,blurHeight:d,blurDataURL:_||"",objectFit:Z.objectFit})+'")':'url("'+N+'")',J=Y?{backgroundSize:Z.objectFit||"cover",backgroundPosition:Z.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:Y}:{},H=function(e){let{config:t,src:r,unoptimized:s,width:o,quality:i,sizes:a,loader:n}=e;if(s)return{src:r,srcSet:void 0,sizes:void 0};let{widths:l,kind:c}=function(e,t,r){let{deviceSizes:s,allSizes:o}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let s;s=e.exec(r);s)t.push(parseInt(s[2]));if(t.length){let e=.01*Math.min(...t);return{widths:o.filter(t=>t>=s[0]*e),kind:"w"}}return{widths:o,kind:"w"}}return"number"!=typeof t?{widths:s,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>o.find(t=>t>=e)||o[o.length-1]))],kind:"x"}}(t,o,a),d=l.length-1;return{sizes:a||"w"!==c?a:"100vw",srcSet:l.map((e,s)=>n({config:t,src:r,quality:i,width:e})+" "+("w"===c?e:s+1)+c).join(", "),src:n({config:t,src:r,quality:i,width:l[d]})}}({config:l,src:u,unoptimized:f,width:$,quality:V,sizes:m,loader:D});return{props:{...I,loading:W?"lazy":h,fetchPriority:C,width:$,height:U,decoding:E,className:p,style:{...Z,...J},sizes:H.sizes,srcSet:H.srcSet,src:j||H.src},meta:{unoptimized:f,priority:g,placeholder:N,fill:y}}}},95728:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:s,blurHeight:o,blurDataURL:i,objectFit:a}=e,n=s?40*s:t,l=o?40*o:r,c=n&&l?"viewBox='0 0 "+n+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+c+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(c?"none":"contain"===a?"xMidYMid":"cover"===a?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+i+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},29472:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return s}});let r=["default","imgix","cloudinary","akamai","custom"],s={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},66794:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return l},getImageProps:function(){return n}});let s=r(53370),o=r(79241),i=r(10221),a=s._(r(52049));function n(e){let{props:t}=(0,o.getImgProps)(e,{defaultLoader:a.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!0,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let l=i.Image},52049:(e,t)=>{"use strict";function r(e){var t;let{config:r,src:s,width:o,quality:i}=e,a=i||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(s)+"&w="+o+"&q="+a}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}}),r.__next_img_default=!0;let s=r},96501:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},86514:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>c,metadata:()=>d});var o=r(19510);r(71159);var i=r(57371),a=r(17710),n=r(19910),l=e([n]);n=(l.then?(await l)():l)[0];let d={title:"Product Categories | Ankkor",description:"Browse our collection of luxury menswear categories"};async function c(){let e=((await (0,n.CP)()).nodes||[]).filter(e=>e.count>0);return(0,o.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[o.jsx("h1",{className:"text-3xl font-serif text-center mb-12",children:"Product Categories"}),0===e.length?o.jsx("div",{className:"text-center py-12",children:o.jsx("p",{className:"text-gray-500",children:"No categories found."})}):o.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:e.map(e=>(0,o.jsxs)(i.default,{href:`/category/${e.slug}`,className:"group block",children:[o.jsx("div",{className:"relative aspect-[4/3] bg-[#f4f3f0] overflow-hidden mb-4",children:e.image?o.jsx(a.default,{src:e.image.sourceUrl,alt:e.name,fill:!0,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",className:"object-cover transition-transform group-hover:scale-105"}):o.jsx("div",{className:"w-full h-full flex items-center justify-center bg-[#f4f3f0]",children:o.jsx("span",{className:"text-[#8a8778] text-lg",children:e.name})})}),(0,o.jsxs)("div",{className:"text-center",children:[o.jsx("h2",{className:"text-xl font-medium text-[#2c2c27]",children:e.name}),(0,o.jsxs)("p",{className:"text-[#8a8778] mt-1",children:[e.count," products"]}),e.description&&o.jsx("p",{className:"text-sm text-[#5c5c52] mt-2 line-clamp-2",children:e.description})]})]},e.id))})]})}s()}catch(e){s(e)}})},51806:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m,metadata:()=>u});var s=r(19510),o=r(10527),i=r.n(o),a=r(36822),n=r.n(a);r(5023);var l=r(68570);(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#useCustomer`);let c=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#CustomerProvider`),d=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#ToastProvider`);(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#useToast`);let u={title:"Ankkor | Timeless Menswear",description:"Elevated essentials for the discerning gentleman. Impeccably tailored garments crafted from the finest materials.",keywords:["menswear","luxury clothing","tailored","shirts","accessories"],icons:{icon:[{url:"/logo.PNG",sizes:"32x32",type:"image/png"},{url:"/logo.PNG",sizes:"16x16",type:"image/png"}],shortcut:"/logo.PNG",apple:"/logo.PNG"}};function m({children:e}){return s.jsx("html",{lang:"en",children:s.jsx("body",{className:`${i().variable} ${n().variable} font-sans antialiased min-h-screen bg-[#f8f8f5]`,children:s.jsx(d,{children:s.jsx(c,{children:s.jsx("main",{style:{paddingTop:0},className:"transition-all duration-300",children:e})})})})})}},12523:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\not-found.tsx#default`)},5023:()=>{},53370:(e,t,r)=>{"use strict";function s(e){return e&&e.__esModule?e:{default:e}}r.r(t),r.d(t,{_:()=>s,_interop_require_default:()=>s})}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,2344,9404,4766,4868,2481,9910],()=>r(45188));module.exports=s})();