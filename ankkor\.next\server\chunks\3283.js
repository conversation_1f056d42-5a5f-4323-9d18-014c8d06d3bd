exports.id=3283,exports.ids=[3283],exports.modules={13417:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,12994,23)),Promise.resolve().then(r.t.bind(r,96114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,79671,23)),Promise.resolve().then(r.t.bind(r,41868,23)),Promise.resolve().then(r.t.bind(r,84759,23))},96799:(e,t,r)=>{Promise.resolve().then(r.bind(r,68897)),Promise.resolve().then(r.bind(r,75367))},54039:(e,t,r)=>{Promise.resolve().then(r.bind(r,83846))},83846:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var a=r(10326);r(17577);var o=r(33265);let s=()=>a.jsx("div",{className:"animate-pulse flex space-x-4",children:(0,a.jsxs)("div",{className:"flex-1 space-y-6 py-1",children:[a.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[a.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-2"}),a.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-1"})]}),a.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"})]})]})}),n=(0,o.default)(async()=>{},{loadableGenerated:{modules:["app\\not-found.tsx -> ./not-found-content"]},ssr:!1,loading:()=>a.jsx(s,{})});function i(){return a.jsx("div",{className:"container mx-auto py-20",children:a.jsx(n,{})})}},68897:(e,t,r)=>{"use strict";r.d(t,{CustomerProvider:()=>d,O:()=>l});var a=r(10326),o=r(17577),s=r(35047),n=r(75367);let i=(0,o.createContext)({customer:null,isLoading:!0,isAuthenticated:!1,token:null,login:async()=>{},register:async()=>{},logout:()=>{},updateProfile:async()=>{},error:null,refreshCustomer:async()=>{}}),l=()=>(0,o.useContext)(i);function d({children:e}){let[t,r]=(0,o.useState)(null),[l,d]=(0,o.useState)(!0),[u,c]=(0,o.useState)(null),[m,p]=(0,o.useState)(null),g=(0,s.useRouter)(),{addToast:y}=(0,n.p)(),h=e=>e?{...e,displayName:e.displayName||e.username||`${e.firstName||""} ${e.lastName||""}`.trim()||"User"}:null,f=async()=>{try{console.log("CustomerProvider: Checking authentication via /api/auth/me");let e=await fetch("/api/auth/me",{method:"GET",credentials:"include"});console.log("CustomerProvider: Auth API response status:",e.status);let t=await e.json();if(console.log("CustomerProvider: Auth API result:",t),!t.success||!t.customer)return p(null),{success:!1,message:t.message||"Not authenticated"};{let e=t.token;return console.log("CustomerProvider: Token from API response:",!!e),p(e||null),{success:!0,customer:t.customer,token:e}}}catch(e){return console.error("CustomerProvider: Error checking authentication:",e),p(null),{success:!1,message:"Network error"}}},b=async()=>{try{let e=await f();if(e.success){let t={...e.customer,token:e.token};r(h(t)),console.log("Customer data refreshed successfully"),console.log("Token available after refresh:",!!e.token)}else console.log("Failed to refresh customer data:",e.message),r(null),p(null)}catch(e){console.error("Error refreshing customer data:",e),r(null),p(null)}},x=async e=>{d(!0),c(null);try{throw Error("Login temporarily disabled for build fix")}catch(t){let e="Login temporarily disabled for build fix";throw c(e),y(e,"error"),t}finally{d(!1)}},v=async e=>{d(!0),c(null);try{throw Error("Register temporarily disabled for build fix")}catch(t){let e="Register temporarily disabled for build fix";throw c(e),y(e,"error"),t}finally{d(!1)}},P=async e=>{d(!0),c(null);try{throw Error("Profile update temporarily disabled for build fix")}catch(t){let e="Profile update temporarily disabled for build fix";throw c(e),y(e,"error"),t}finally{d(!1)}};return a.jsx(i.Provider,{value:{customer:t,isLoading:l,isAuthenticated:!!t,token:m,login:x,register:v,logout:()=>{r(null),p(null),console.log("Logout completed, token cleared"),y("You have been signed out successfully","info"),g.push("/"),g.refresh()},updateProfile:P,error:u,refreshCustomer:b},children:e})}},75367:(e,t,r)=>{"use strict";r.d(t,{ToastProvider:()=>m,p:()=>p});var a=r(10326),o=r(17577),s=r(92148),n=r(86462),i=r(54659),l=r(87888),d=r(18019),u=r(94019);let c=(0,o.createContext)(void 0);function m({children:e}){let[t,r]=(0,o.useState)([]);return(0,a.jsxs)(c.Provider,{value:{toasts:t,addToast:(e,t="info",a=3e3)=>{let o=Math.random().toString(36).substring(2,9);r(r=>[...r,{id:o,message:e,type:t,duration:a}])},removeToast:e=>{r(t=>t.filter(t=>t.id!==e))}},children:[e,a.jsx(y,{})]})}function p(){let e=(0,o.useContext)(c);if(void 0===e)throw Error("useToast must be used within a ToastProvider");return e}function g({toast:e,onRemove:t}){return(0,a.jsxs)(s.E.div,{initial:{opacity:0,y:-50},animate:{opacity:1,y:0},exit:{opacity:0,x:300},className:`flex items-center p-4 rounded-lg border shadow-lg ${(()=>{switch(e.type){case"success":return"bg-[#f4f3f0] border-[#8a8778]";case"error":return"bg-red-50 border-red-200";default:return"bg-[#f8f8f5] border-[#e5e2d9]"}})()} max-w-md`,children:[a.jsx(()=>{switch(e.type){case"success":return a.jsx(i.Z,{className:"h-5 w-5"});case"error":return a.jsx(l.Z,{className:"h-5 w-5"});default:return a.jsx(d.Z,{className:"h-5 w-5"})}},{}),a.jsx("span",{className:"ml-3 text-sm font-medium flex-1",children:e.message}),a.jsx("button",{onClick:t,className:"ml-4 text-gray-400 hover:text-gray-600",children:a.jsx(u.Z,{className:"h-4 w-4"})})]})}function y(){let{toasts:e,removeToast:t}=p();return a.jsx("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:a.jsx(n.M,{children:e.map(e=>a.jsx(g,{toast:e,onRemove:()=>t(e.id)},e.id))})})}},53248:(e,t,r)=>{"use strict";new(r(78578)).s({url:process.env.UPSTASH_REDIS_REST_URL||process.env.NEXT_PUBLIC_KV_REST_API_URL||"",token:process.env.UPSTASH_REDIS_REST_TOKEN||process.env.NEXT_PUBLIC_KV_REST_API_TOKEN||""})},15725:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{Bk:()=>c,CP:()=>u,Dg:()=>d,Id:()=>h,ML:()=>f,Op:()=>y,Xp:()=>n,Xq:()=>p,dv:()=>m,h2:()=>g,mJ:()=>E,wv:()=>b,xu:()=>x});var o=r(93690);r(53248);var s=e([o]);o=(s.then?(await s)():s)[0];let v={storeUrl:"https://maroon-lapwing-781450.hostingersite.com",graphqlUrl:process.env.WOOCOMMERCE_GRAPHQL_URL||"https://your-wordpress-site.com/graphql",apiVersion:"v1"},P=process.env.WOOCOMMERCE_GRAPHQL_URL||"https://your-wordpress-site.com/graphql",I=new o.GraphQLClient(P,{headers:{"Content-Type":"application/json",Accept:"application/json"}}),C=(0,o.gql)`
  fragment ProductFields on Product {
    id
    databaseId
    name
    slug
    description
    shortDescription
    type
    image {
      sourceUrl
      altText
    }
    galleryImages {
      nodes {
        sourceUrl
        altText
      }
    }
    ... on SimpleProduct {
      price
      regularPrice
      salePrice
      onSale
      stockStatus
      stockQuantity
    }
    ... on VariableProduct {
      price
      regularPrice
      salePrice
      onSale
      stockStatus
      stockQuantity
      attributes {
        nodes {
          name
          options
        }
      }
    }
  }
`,w=(0,o.gql)`
  fragment VariableProductWithVariations on VariableProduct {
    attributes {
      nodes {
        name
        options
      }
    }
    variations {
      nodes {
        id
        databaseId
        name
        price
        regularPrice
        salePrice
        stockStatus
        stockQuantity
        attributes {
          nodes {
            name
            value
          }
        }
      }
    }
  }
`,N=(0,o.gql)`
  query GetProducts(
    $first: Int
    $after: String
    $where: RootQueryToProductConnectionWhereArgs
  ) {
    products(first: $first, after: $after, where: $where) {
      pageInfo {
        hasNextPage
        endCursor
      }
      nodes {
        ...ProductFields
        ... on VariableProduct {
          ...VariableProductWithVariations
        }
      }
    }
  }
  ${C}
  ${w}
`;async function n(e={}){try{return(await i(N,{first:e.first||12,after:e.after||null,where:e.where||{}},["products"],60)).products}catch(e){return console.error("Error fetching products:",e),{nodes:[],pageInfo:{hasNextPage:!1,endCursor:null}}}}async function i(e,t={},r=[],a=60){try{{let o={method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:e,variables:t}),next:{}};r&&r.length>0&&(o.next.tags=r),void 0!==a&&(o.next.revalidate=a);let s=await fetch(v.graphqlUrl,o);if(!s.ok)throw Error(`WooCommerce GraphQL API responded with status ${s.status}`);let{data:n,errors:i}=await s.json();if(i)throw console.error("GraphQL Errors:",i),Error(i[0].message);return n}}catch(e){throw console.error("Error fetching from WooCommerce:",e),e}}async function l({query:e,variables:t},r=3,a=1e3){let o=0,s=null;for(;o<r;)try{return await i(e,t,[],0)}catch(e){s=e,++o<r&&(console.log(`Retrying request (${o}/${r}) after ${a}ms`),await new Promise(e=>setTimeout(e,a)),a*=2)}throw console.error(`Failed after ${r} attempts:`,s),s}(0,o.gql)`
  query GetProductBySlug($slug: ID!) {
    product(id: $slug, idType: SLUG) {
      ...ProductFields
      ... on VariableProduct {
        ...VariableProductWithVariations
      }
    }
  }
  ${C}
  ${w}
`,(0,o.gql)`
  query GetProductBySlugWithTags($slug: ID!) {
    product(id: $slug, idType: SLUG) {
      ...ProductFields
      ... on VariableProduct {
        ...VariableProductWithVariations
      }
      productTags {
        nodes {
          id
          name
          slug
        }
      }
      productCategories {
        nodes {
          id
          name
          slug
        }
      }
    }
  }
  ${C}
  ${w}
`,(0,o.gql)`
  query GetCategories(
    $first: Int
    $after: String
    $where: RootQueryToProductCategoryConnectionWhereArgs
  ) {
    productCategories(first: $first, after: $after, where: $where) {
      pageInfo {
        hasNextPage
        endCursor
      }
      nodes {
        id
        databaseId
        name
        slug
        description
        count
        image {
          sourceUrl
          altText
        }
      }
    }
  }
`;let k=(0,o.gql)`
  query GetAllProducts($first: Int = 20) {
    products(first: $first) {
      nodes {
        id
        databaseId
        name
        slug
        description
        shortDescription
        productCategories {
          nodes {
            id
            name
            slug
          }
        }
        ... on SimpleProduct {
          price
          regularPrice
          salePrice
          onSale
          stockStatus
          stockQuantity
        }
        ... on VariableProduct {
          price
          regularPrice
          salePrice
          onSale
          stockStatus
          variations {
            nodes {
              stockStatus
              stockQuantity
            }
          }
        }
        image {
          id
          sourceUrl
          altText
        }
        galleryImages {
          nodes {
            id
            sourceUrl
            altText
          }
        }
        ... on VariableProduct {
          attributes {
            nodes {
              name
              options
            }
          }
        }
        ... on SimpleProduct {
          attributes {
            nodes {
              name
              options
            }
          }
        }
      }
    }
  }
`,T=((0,o.gql)`
  query GetProductsByCategory($slug: ID!, $first: Int = 20) {
    productCategory(id: $slug, idType: SLUG) {
      id
      name
      slug
      description
      products(first: $first) {
        nodes {
          id
          databaseId
          name
          slug
          ... on SimpleProduct {
            price
            regularPrice
            salePrice
            onSale
            stockStatus
          }
          ... on VariableProduct {
            price
            regularPrice
            salePrice
            onSale
            stockStatus
          }
          image {
            id
            sourceUrl
            altText
          }
        }
      }
    }
  }
`,(0,o.gql)`
  query GetAllCategories($first: Int = 20) {
    productCategories(first: $first) {
      nodes {
        id
        databaseId
        name
        slug
        description
        count
        image {
          sourceUrl
          altText
        }
        children {
          nodes {
            id
            name
            slug
          }
        }
      }
    }
  }
`);(0,o.gql)`
  query GetCart {
    cart {
      contents {
        nodes {
          key
          product {
            node {
              id
              databaseId
              name
              slug
              type
              image {
                sourceUrl
                altText
              }
            }
          }
          variation {
            node {
              id
              databaseId
              name
              attributes {
                nodes {
                  name
                  value
                }
              }
            }
          }
          quantity
          total
        }
      }
      subtotal
      total
      totalTax
      isEmpty
    }
  }
`,(0,o.gql)`
  mutation LoginUser($username: String!, $password: String!) {
    login(input: {
      clientMutationId: "login"
      username: $username
      password: $password
    }) {
      authToken
      refreshToken
      user {
        id
        databaseId
        email
        firstName
        lastName
        nicename
        nickname
        username
      }
    }
  }
`;let $=(0,o.gql)`
  query GetCart {
    cart {
      contents {
        nodes {
          key
          product {
            node {
              id
              databaseId
              name
              slug
              type
              image {
                sourceUrl
                altText
              }
            }
          }
          variation {
            node {
              id
              databaseId
              name
              attributes {
                nodes {
                  name
                  value
                }
              }
            }
          }
          quantity
          total
        }
      }
      subtotal
      total
      totalTax
      isEmpty
      contentsCount
    }
  }
`,S=(0,o.gql)`
  mutation AddToCart($productId: Int!, $variationId: Int, $quantity: Int, $extraData: String) {
    addToCart(
      input: {
        clientMutationId: "addToCart"
        productId: $productId
        variationId: $variationId
        quantity: $quantity
        extraData: $extraData
      }
    ) {
      cart {
        contents {
          nodes {
            key
            product {
              node {
                id
                databaseId
                name
                slug
                type
                image {
                  sourceUrl
                  altText
                }
              }
            }
            variation {
              node {
                id
                databaseId
                name
                attributes {
                  nodes {
                    name
                    value
                  }
                }
              }
            }
            quantity
            total
          }
        }
        subtotal
        total
        totalTax
        isEmpty
        contentsCount
      }
    }
  }
`,q=(0,o.gql)`
  mutation RemoveItemsFromCart($keys: [ID]!, $all: Boolean) {
    removeItemsFromCart(input: { keys: $keys, all: $all }) {
      cart {
        contents {
          nodes {
            key
            product {
              node {
                id
                databaseId
                name
                slug
                type
                image {
                  sourceUrl
                  altText
                }
              }
            }
            variation {
              node {
                id
                databaseId
                name
                attributes {
                  nodes {
                    name
                    value
                  }
                }
              }
            }
            quantity
            total
          }
        }
        subtotal
        total
        totalTax
        isEmpty
      }
    }
  }
`;async function d(e=20){try{let t=await l({query:k,variables:{first:e}});return t?.products?.nodes||[]}catch(e){return console.error("Error fetching all products:",e),[]}}async function u(e={}){try{let t=await l({query:T,variables:{first:e.first||20,after:e.after||null,where:e.where||{}}});return{nodes:t.productCategories.nodes,pageInfo:t.productCategories.pageInfo}}catch(e){return console.error("Error fetching categories:",e),{nodes:[],pageInfo:{hasNextPage:!1,endCursor:null}}}}async function c(e=[]){try{if(0===e.length)return{contents:{nodes:[]},subtotal:"0",total:"0",totalTax:"0",isEmpty:!0,contentsCount:0};let t=e[0],r=await p("",[t]);if(e.length>1){for(let t=1;t<e.length;t++)await p("",[e[t]]);return await m()}return r}catch(e){throw console.error("Error creating cart:",e),e}}async function m(){try{let e=await l({query:$,variables:{}});return e?.cart||null}catch(e){return console.error("Error fetching cart:",e),null}}async function p(e,t){try{if(0===t.length)throw Error("No items provided to add to cart");let e=t[0],r={productId:parseInt(e.productId),quantity:e.quantity||1,variationId:e.variationId?parseInt(e.variationId):null,extraData:null};console.log("Adding to cart with variables:",r);let a=await l({query:S,variables:r});return console.log("Add to cart response:",a),a.addToCart.cart}catch(e){throw console.error("Error adding items to cart:",e),e}}async function g(e,t){try{let e=await l({query:q,variables:{keys:t,all:!1}});return e?.removeItemsFromCart?.cart||null}catch(e){throw console.error("Error removing items from cart:",e),e}}function y(e){if(!e)return null;let t=!!e.variations?.nodes?.length,r={minVariantPrice:{amount:e.price||"0",currencyCode:"INR"},maxVariantPrice:{amount:e.price||"0",currencyCode:"INR"}};if(t&&e.variations?.nodes?.length>0){let t=e.variations.nodes.map(e=>parseFloat(e.price||"0")).filter(e=>!isNaN(e));t.length>0&&(r={minVariantPrice:{amount:String(Math.min(...t)),currencyCode:"INR"},maxVariantPrice:{amount:String(Math.max(...t)),currencyCode:"INR"}})}let a=function(e){let t=[];return e.image&&t.push({url:e.image.sourceUrl,altText:e.image.altText||e.name||""}),e.galleryImages?.nodes?.length&&e.galleryImages.nodes.forEach(r=>{e.image&&r.sourceUrl===e.image.sourceUrl||t.push({url:r.sourceUrl,altText:r.altText||e.name||""})}),t}(e),o=e.variations?.nodes?.map(e=>({id:e.id,title:e.name,price:{amount:e.price||"0",currencyCode:"USD"},availableForSale:"IN_STOCK"===e.stockStatus,selectedOptions:e.attributes?.nodes?.map(e=>({name:e.name,value:e.value}))||[],sku:e.sku||"",image:e.image?{url:e.image.sourceUrl,altText:e.image.altText||""}:null}))||[],s=e.attributes?.nodes?.map(e=>({name:e.name,values:e.options||[]}))||[],n=e.productCategories?.nodes?.map(e=>({handle:e.slug,title:e.name}))||[],i={};return e.metafields&&e.metafields.forEach(e=>{i[e.key]=e.value}),{id:e.id,handle:e.slug,title:e.name,description:e.description||"",descriptionHtml:e.description||"",priceRange:r,options:s,variants:o,images:a,collections:n,availableForSale:"OUT_OF_STOCK"!==e.stockStatus,metafields:i,_originalWooProduct:e}}(0,o.gql)`
  query GetShippingMethods {
    shippingMethods {
      nodes {
        id
        title
        description
        cost
      }
    }
  }
`,(0,o.gql)`
  query GetPaymentGateways {
    paymentGateways {
      nodes {
        id
        title
        description
        enabled
      }
    }
  }
`;let E=(e,t,r,a="")=>{if(!e||!e.metafields)return a;if(r){let o=`${r}:${t}`;return e.metafields[o]||a}return e.metafields[t]||a};function h(e){if(!e)return null;let t=e.contents?.nodes?.map(e=>{let t=e.product?.node,r=e.variation?.node;return{id:e.key,quantity:e.quantity,merchandise:{id:r?.id||t?.id,title:r?.name||t?.name,product:{id:t?.id,handle:t?.slug,title:t?.name,image:t?.image?{url:t?.image.sourceUrl,altText:t?.image.altText||""}:null},selectedOptions:r?.attributes?.nodes?.map(e=>({name:e.name,value:e.value}))||[]},cost:{totalAmount:{amount:e.total||"0",currencyCode:"USD"}}}})||[],r=e.appliedCoupons?.nodes?.map(e=>({code:e.code,amount:e.discountAmount||"0"}))||[],a=t.reduce((e,t)=>e+t.quantity,0);return{id:e.id,checkoutUrl:"",totalQuantity:a,cost:{subtotalAmount:{amount:e.subtotal||"0",currencyCode:"USD"},totalAmount:{amount:e.total||"0",currencyCode:"USD"}},lines:t,discountCodes:r}}function f(e,t=!1){let r=`${v.storeUrl}/checkout`,a=e?`?cart=${e}`:"",o="";return t||(o=`${a?"&":"?"}guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0&create_account=0&skip_login=1&force_guest_checkout=1`),`${r}${a}${o}`}async function b(e){let t=(0,o.gql)`
    query GetProduct($id: ID!) {
      product(id: $id, idType: DATABASE_ID) {
        id
        databaseId
        name
        slug
        description
        shortDescription
        price
        regularPrice
        salePrice
        onSale
        stockStatus
        stockQuantity
        image {
          sourceUrl
          altText
        }
        galleryImages {
          nodes {
            sourceUrl
            altText
          }
        }
        ... on SimpleProduct {
          attributes {
            nodes {
              name
              options
            }
          }
          price
          regularPrice
          salePrice
        }
        ... on VariableProduct {
          price
          regularPrice
          salePrice
          attributes {
            nodes {
              name
              options
            }
          }
          variations {
            nodes {
              id
              databaseId
              name
              price
              regularPrice
              salePrice
              stockStatus
              attributes {
                nodes {
                  name
                  value
                }
              }
            }
          }
        }
      }
    }
  `;try{return(await I.request(t,{id:e})).product}catch(e){throw console.error("Error fetching product:",e),Error("Failed to fetch product")}}(0,o.gql)`
  mutation CreateCustomer($input: RegisterCustomerInput!) {
    registerCustomer(input: $input) {
      customer {
        id
        databaseId
        email
        firstName
        lastName
        displayName
      }
      authToken
      refreshToken
    }
  }
`,(0,o.gql)`
  mutation UpdateCustomer($input: UpdateCustomerInput!) {
    updateCustomer(input: $input) {
      clientMutationId
      customer {
        id
        databaseId
        email
        firstName
        lastName
        displayName
        billing {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
          email
          phone
        }
        shipping {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
        }
      }
      customerUserErrors {
        field
        message
      }
    }
  }
`,(0,o.gql)`
  query GetCustomer {
    customer {
      id
      databaseId
      email
      firstName
      lastName
      displayName
      username
      role
      date
      modified
      isPayingCustomer
      orderCount
      totalSpent
      billing {
        firstName
        lastName
        company
        address1
        address2
        city
        state
        postcode
        country
        email
        phone
      }
      shipping {
        firstName
        lastName
        company
        address1
        address2
        city
        state
        postcode
        country
      }
      orders(first: 50) {
        nodes {
          id
          databaseId
          date
          status
          total
          subtotal
          totalTax
          shippingTotal
          discountTotal
          paymentMethodTitle
          customerNote
          billing {
            firstName
            lastName
            company
            address1
            address2
            city
            state
            postcode
            country
            email
            phone
          }
          shipping {
            firstName
            lastName
            company
            address1
            address2
            city
            state
            postcode
            country
          }
          lineItems {
            nodes {
              product {
                node {
                  id
                  name
                  slug
                  image {
                    sourceUrl
                    altText
                  }
                }
              }
              variation {
                node {
                  id
                  name
                  attributes {
                    nodes {
                      name
                      value
                    }
                  }
                }
              }
              quantity
              total
              subtotal
              totalTax
            }
          }
          shippingLines {
            nodes {
              methodTitle
              total
            }
          }
          feeLines {
            nodes {
              name
              total
            }
          }
          couponLines {
            nodes {
              code
              discount
            }
          }
        }
      }
      downloadableItems {
        nodes {
          name
          downloadId
          downloadsRemaining
          accessExpires
          product {
            node {
              id
              name
            }
          }
        }
      }
      metaData {
        key
        value
      }
    }
  }
`,(0,o.gql)`
  mutation CreateAddress($input: UpdateCustomerInput!) {
    updateCustomer(input: $input) {
      customer {
        id
        billing {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
          email
          phone
        }
        shipping {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
        }
      }
    }
  }
`,(0,o.gql)`
  mutation UpdateAddress($input: UpdateCustomerInput!) {
    updateCustomer(input: $input) {
      customer {
        id
        billing {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
          email
          phone
        }
        shipping {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
        }
      }
    }
  }
`,(0,o.gql)`
  mutation DeleteAddress($input: UpdateCustomerInput!) {
    updateCustomer(input: $input) {
      customer {
        id
        billing {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
          email
          phone
        }
        shipping {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
        }
      }
    }
  }
`,(0,o.gql)`
  mutation SetDefaultAddress($input: UpdateCustomerInput!) {
    updateCustomer(input: $input) {
      customer {
        id
        billing {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
          email
          phone
        }
        shipping {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
        }
      }
    }
  }
`;let U=(0,o.gql)`
  mutation UpdateCart($input: UpdateItemQuantitiesInput!) {
    updateItemQuantities(input: $input) {
      cart {
        contents {
          nodes {
            key
            product {
              node {
                id
                name
                price
              }
            }
            quantity
            total
          }
        }
        subtotal
        total
        totalTax
        isEmpty
      }
    }
  }
`;async function x(e){try{return(await l({query:U,variables:{input:{items:e}}})).updateItemQuantities.cart}catch(e){throw console.error("Error updating cart:",e),e}}a()}catch(e){a(e)}})},51806:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m,metadata:()=>c});var a=r(19510),o=r(10527),s=r.n(o),n=r(36822),i=r.n(n);r(5023);var l=r(68570);(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#useCustomer`);let d=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#CustomerProvider`),u=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#ToastProvider`);(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#useToast`);let c={title:"Ankkor | Timeless Menswear",description:"Elevated essentials for the discerning gentleman. Impeccably tailored garments crafted from the finest materials.",keywords:["menswear","luxury clothing","tailored","shirts","accessories"],icons:{icon:[{url:"/logo.PNG",sizes:"32x32",type:"image/png"},{url:"/logo.PNG",sizes:"16x16",type:"image/png"}],shortcut:"/logo.PNG",apple:"/logo.PNG"}};function m({children:e}){return a.jsx("html",{lang:"en",children:a.jsx("body",{className:`${s().variable} ${i().variable} font-sans antialiased min-h-screen bg-[#f8f8f5]`,children:a.jsx(u,{children:a.jsx(d,{children:a.jsx("main",{style:{paddingTop:0},className:"transition-all duration-300",children:e})})})})})}},12523:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\not-found.tsx#default`)},5023:()=>{}};