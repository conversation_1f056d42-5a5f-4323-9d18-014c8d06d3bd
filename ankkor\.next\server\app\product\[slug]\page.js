(()=>{var e={};e.id=1599,e.ids=[1599],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},37523:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>h,originalPathname:()=>m,pages:()=>p,routeModule:()=>g,tree:()=>f});var o=r(8179);r(51806),r(12523);var n=r(23191),a=r(88716),i=r(37922),l=r.n(i),c=r(95231),d={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>c[e]);r.d(t,d);var u=e([o]);o=(u.then?(await u)():u)[0];let f=["",{children:["product",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,8179)),"E:\\ankkorwoo\\ankkor\\src\\app\\product\\[slug]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,51806)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],p=["E:\\ankkorwoo\\ankkor\\src\\app\\product\\[slug]\\page.tsx"],m="/product/[slug]/page",h={require:r,loadChunk:()=>Promise.resolve()},g=new n.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/product/[slug]/page",pathname:"/product/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:f}});s()}catch(e){s(e)}})},13417:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,12994,23)),Promise.resolve().then(r.t.bind(r,96114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,79671,23)),Promise.resolve().then(r.t.bind(r,41868,23)),Promise.resolve().then(r.t.bind(r,84759,23))},96799:(e,t,r)=>{Promise.resolve().then(r.bind(r,68897)),Promise.resolve().then(r.bind(r,75367))},54039:(e,t,r)=>{Promise.resolve().then(r.bind(r,83846))},72996:(e,t,r)=>{Promise.resolve().then(r.bind(r,387))},34565:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(76557).Z)("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])},46226:(e,t,r)=>{"use strict";r.d(t,{default:()=>o.a});var s=r(69029),o=r.n(s)},50131:(e,t,r)=>{"use strict";e.exports=r(81616).vendored.contexts.RouterContext},69029:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return l},getImageProps:function(){return i}});let s=r(91174),o=r(23078),n=r(92481),a=s._(r(86820));function i(e){let{props:t}=(0,o.getImgProps)(e,{defaultLoader:a.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!0,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let l=n.Image},83846:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(10326);r(17577);var o=r(33265);let n=()=>s.jsx("div",{className:"animate-pulse flex space-x-4",children:(0,s.jsxs)("div",{className:"flex-1 space-y-6 py-1",children:[s.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[s.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-2"}),s.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-1"})]}),s.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"})]})]})}),a=(0,o.default)(async()=>{},{loadableGenerated:{modules:["app\\not-found.tsx -> ./not-found-content"]},ssr:!1,loading:()=>s.jsx(n,{})});function i(){return s.jsx("div",{className:"container mx-auto py-20",children:s.jsx(a,{})})}},77321:(e,t,r)=>{"use strict";r.d(t,{j:()=>n}),r(10326);var s=r(17577);r(86806);let o=(0,s.createContext)(void 0),n=()=>{let e=(0,s.useContext)(o);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e}},387:(e,t,r)=>{"use strict";r.d(t,{default:()=>m});var s=r(10326),o=r(17577),n=r(46226),a=r(92148),i=r(86806),l=r(91664),c=r(77321),d=r(76557);let u=(0,d.Z)("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]),f=(0,d.Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);var p=r(34565);let m=({product:e})=>{let[t,r]=(0,o.useState)(0),[d,m]=(0,o.useState)(1),[h,g]=(0,o.useState)(null),[x,v]=(0,o.useState)({}),[y,b]=(0,o.useState)(!1),j=(0,i.rY)(),{openCart:k}=(0,c.j)(),{id:w,databaseId:P,name:N,description:S,shortDescription:_,price:C,regularPrice:E,onSale:R,stockStatus:T,image:M,galleryImages:O,attributes:A,type:F,variations:q}=e,I="VARIABLE"===F,z=[M?.sourceUrl?{sourceUrl:M.sourceUrl,altText:M.altText||N}:null,...O?.nodes||[]].filter(Boolean),L=(e,t)=>{if(v(r=>({...r,[e]:t})),I&&q?.nodes){let r={...x,[e]:t};if(A?.nodes?.every(e=>r[e.name])){let e=q.nodes.find(e=>e.attributes.nodes.every(e=>{let t=r[e.name];return e.value===t}));e?g(e):g(null)}}},U=async()=>{b(!0);try{let e={productId:P.toString(),quantity:d,name:N,price:h?.price||C,image:{url:z[0]?.sourceUrl||"",altText:z[0]?.altText||N}};await j.addToCart(e),k()}catch(e){console.error("Error adding product to cart:",e)}finally{b(!1)}},$="IN_STOCK"!==T,G=!I||I&&h;return s.jsx("div",{className:"container mx-auto px-4 py-12",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,s.jsxs)("div",{className:"space-y-6",children:[s.jsx("div",{className:"relative aspect-square bg-[#f4f3f0] overflow-hidden",children:z[t]?.sourceUrl&&s.jsx(n.default,{src:z[t].sourceUrl,alt:z[t].altText||N,fill:!0,sizes:"(max-width: 768px) 100vw, 50vw",priority:!0,className:"object-cover"})}),z.length>1&&s.jsx("div",{className:"grid grid-cols-5 gap-2",children:z.map((e,o)=>s.jsx("button",{onClick:()=>r(o),className:`relative aspect-square bg-[#f4f3f0] ${t===o?"ring-2 ring-[#2c2c27]":""}`,children:s.jsx(n.default,{src:e.sourceUrl,alt:e.altText||`${N} - Image ${o+1}`,fill:!0,sizes:"(max-width: 768px) 20vw, 10vw",className:"object-cover"})},o))})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[s.jsx("h1",{className:"text-3xl font-serif text-[#2c2c27]",children:N}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[s.jsx("span",{className:"text-xl font-medium text-[#2c2c27]",children:(h?.price||C).toString().includes("₹")||(h?.price||C).toString().includes("$")||(h?.price||C).toString().includes("€")||(h?.price||C).toString().includes("\xa3")?h?.price||C:`₹${h?.price||C}`}),R&&E&&s.jsx("span",{className:"text-sm line-through text-[#8a8778]",children:E.toString().includes("₹")||E.toString().includes("$")||E.toString().includes("€")||E.toString().includes("\xa3")?E:`₹${E}`})]}),_&&s.jsx("div",{className:"prose prose-sm text-[#5c5c52]",dangerouslySetInnerHTML:{__html:_}}),I&&A?.nodes&&s.jsx("div",{className:"space-y-4",children:A.nodes.map(e=>(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx("h3",{className:"font-medium text-[#2c2c27]",children:e.name}),s.jsx("div",{className:"flex flex-wrap gap-2",children:e.options.map(t=>s.jsx("button",{onClick:()=>L(e.name,t),className:`px-4 py-2 border ${x[e.name]===t?"border-[#2c2c27] bg-[#2c2c27] text-white":"border-gray-300 hover:border-[#8a8778]"}`,children:t},t))})]},e.name))}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[s.jsx("span",{className:"text-[#5c5c52]",children:"Quantity:"}),(0,s.jsxs)("div",{className:"flex items-center border border-gray-300",children:[s.jsx("button",{onClick:()=>m(e=>e>1?e-1:1),disabled:d<=1,className:"px-3 py-2 hover:bg-gray-100","aria-label":"Decrease quantity",children:s.jsx(u,{className:"h-4 w-4"})}),s.jsx("span",{className:"px-4 py-2 border-x border-gray-300",children:d}),s.jsx("button",{onClick:()=>m(e=>e+1),className:"px-3 py-2 hover:bg-gray-100","aria-label":"Increase quantity",children:s.jsx(f,{className:"h-4 w-4"})})]})]}),(0,s.jsxs)("div",{className:"text-sm",children:[s.jsx("span",{className:"font-medium",children:"Availability: "}),s.jsx("span",{className:$?"text-red-600":"text-green-600",children:$?"Out of Stock":"In Stock"})]}),(0,s.jsxs)(a.E.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.2},children:[(0,s.jsxs)(l.z,{onClick:U,disabled:$||y||!G,className:"w-full py-6 bg-[#2c2c27] text-white hover:bg-[#3c3c37] flex items-center justify-center gap-2",children:[s.jsx(p.Z,{className:"h-5 w-5"}),y?"Adding...":"Add to Cart"]}),I&&!G&&!$&&s.jsx("p",{className:"mt-2 text-sm text-red-600",children:"Please select all options to add this product to your cart"})]}),S&&(0,s.jsxs)("div",{className:"mt-12 border-t border-gray-200 pt-8",children:[s.jsx("h2",{className:"text-xl font-serif mb-4 text-[#2c2c27]",children:"Description"}),s.jsx("div",{className:"prose prose-sm text-[#5c5c52]",dangerouslySetInnerHTML:{__html:S}})]})]})]})})}},68897:(e,t,r)=>{"use strict";r.d(t,{CustomerProvider:()=>c,O:()=>l});var s=r(10326),o=r(17577),n=r(35047),a=r(75367);let i=(0,o.createContext)({customer:null,isLoading:!0,isAuthenticated:!1,token:null,login:async()=>{},register:async()=>{},logout:()=>{},updateProfile:async()=>{},error:null,refreshCustomer:async()=>{}}),l=()=>(0,o.useContext)(i);function c({children:e}){let[t,r]=(0,o.useState)(null),[l,c]=(0,o.useState)(!0),[d,u]=(0,o.useState)(null),[f,p]=(0,o.useState)(null),m=(0,n.useRouter)(),{addToast:h}=(0,a.p)(),g=e=>e?{...e,displayName:e.displayName||e.username||`${e.firstName||""} ${e.lastName||""}`.trim()||"User"}:null,x=async()=>{try{console.log("CustomerProvider: Checking authentication via /api/auth/me");let e=await fetch("/api/auth/me",{method:"GET",credentials:"include"});console.log("CustomerProvider: Auth API response status:",e.status);let t=await e.json();if(console.log("CustomerProvider: Auth API result:",t),!t.success||!t.customer)return p(null),{success:!1,message:t.message||"Not authenticated"};{let e=t.token;return console.log("CustomerProvider: Token from API response:",!!e),p(e||null),{success:!0,customer:t.customer,token:e}}}catch(e){return console.error("CustomerProvider: Error checking authentication:",e),p(null),{success:!1,message:"Network error"}}},v=async()=>{try{let e=await x();if(e.success){let t={...e.customer,token:e.token};r(g(t)),console.log("Customer data refreshed successfully"),console.log("Token available after refresh:",!!e.token)}else console.log("Failed to refresh customer data:",e.message),r(null),p(null)}catch(e){console.error("Error refreshing customer data:",e),r(null),p(null)}},y=async e=>{c(!0),u(null);try{throw Error("Login temporarily disabled for build fix")}catch(t){let e="Login temporarily disabled for build fix";throw u(e),h(e,"error"),t}finally{c(!1)}},b=async e=>{c(!0),u(null);try{throw Error("Register temporarily disabled for build fix")}catch(t){let e="Register temporarily disabled for build fix";throw u(e),h(e,"error"),t}finally{c(!1)}},j=async e=>{c(!0),u(null);try{throw Error("Profile update temporarily disabled for build fix")}catch(t){let e="Profile update temporarily disabled for build fix";throw u(e),h(e,"error"),t}finally{c(!1)}};return s.jsx(i.Provider,{value:{customer:t,isLoading:l,isAuthenticated:!!t,token:f,login:y,register:b,logout:()=>{r(null),p(null),console.log("Logout completed, token cleared"),h("You have been signed out successfully","info"),m.push("/"),m.refresh()},updateProfile:j,error:d,refreshCustomer:v},children:e})}},91664:(e,t,r)=>{"use strict";r.d(t,{z:()=>l});var s=r(10326);r(17577);var o=r(34214),n=r(79360),a=r(51223);let i=(0,n.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-[#2c2c27] text-[#f4f3f0] shadow-xs hover:bg-[#3d3d35]",destructive:"bg-[#ff4d4f] text-white shadow-xs hover:bg-[#ff4d4f]/90 focus-visible:ring-[#ff4d4f]/20 dark:focus-visible:ring-[#ff4d4f]/40",outline:"border border-[#e5e2d9] bg-[#f8f8f5] shadow-xs hover:bg-[#f4f3f0] hover:text-[#2c2c27]",secondary:"bg-[#e5e2d9] text-[#2c2c27] shadow-xs hover:bg-[#e5e2d9]/80",ghost:"hover:bg-[#f4f3f0] hover:text-[#2c2c27]",link:"text-[#2c2c27] underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:r,asChild:n=!1,...l}){let c=n?o.g7:"button";return s.jsx(c,{"data-slot":"button",className:(0,a.cn)(i({variant:t,size:r,className:e})),...l})}},75367:(e,t,r)=>{"use strict";r.d(t,{ToastProvider:()=>f,p:()=>p});var s=r(10326),o=r(17577),n=r(92148),a=r(86462),i=r(54659),l=r(87888),c=r(18019),d=r(94019);let u=(0,o.createContext)(void 0);function f({children:e}){let[t,r]=(0,o.useState)([]);return(0,s.jsxs)(u.Provider,{value:{toasts:t,addToast:(e,t="info",s=3e3)=>{let o=Math.random().toString(36).substring(2,9);r(r=>[...r,{id:o,message:e,type:t,duration:s}])},removeToast:e=>{r(t=>t.filter(t=>t.id!==e))}},children:[e,s.jsx(h,{})]})}function p(){let e=(0,o.useContext)(u);if(void 0===e)throw Error("useToast must be used within a ToastProvider");return e}function m({toast:e,onRemove:t}){return(0,s.jsxs)(n.E.div,{initial:{opacity:0,y:-50},animate:{opacity:1,y:0},exit:{opacity:0,x:300},className:`flex items-center p-4 rounded-lg border shadow-lg ${(()=>{switch(e.type){case"success":return"bg-[#f4f3f0] border-[#8a8778]";case"error":return"bg-red-50 border-red-200";default:return"bg-[#f8f8f5] border-[#e5e2d9]"}})()} max-w-md`,children:[s.jsx(()=>{switch(e.type){case"success":return s.jsx(i.Z,{className:"h-5 w-5"});case"error":return s.jsx(l.Z,{className:"h-5 w-5"});default:return s.jsx(c.Z,{className:"h-5 w-5"})}},{}),s.jsx("span",{className:"ml-3 text-sm font-medium flex-1",children:e.message}),s.jsx("button",{onClick:t,className:"ml-4 text-gray-400 hover:text-gray-600",children:s.jsx(d.Z,{className:"h-4 w-4"})})]})}function h(){let{toasts:e,removeToast:t}=p();return s.jsx("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:s.jsx(a.M,{children:e.map(e=>s.jsx(m,{toast:e,onRemove:()=>t(e.id)},e.id))})})}},51223:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(41135),o=r(31009);function n(...e){return(0,o.m6)((0,s.W)(e))}},58585:(e,t,r)=>{"use strict";var s=r(61085);r.o(s,"notFound")&&r.d(t,{notFound:function(){return s.notFound}}),r.o(s,"redirect")&&r.d(t,{redirect:function(){return s.redirect}})},61085:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return a},RedirectType:function(){return s.RedirectType},notFound:function(){return o.notFound},permanentRedirect:function(){return s.permanentRedirect},redirect:function(){return s.redirect}});let s=r(83953),o=r(16399);class n extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class a extends URLSearchParams{append(){throw new n}delete(){throw new n}set(){throw new n}sort(){throw new n}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},16399:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isNotFoundError:function(){return o},notFound:function(){return s}});let r="NEXT_NOT_FOUND";function s(){let e=Error(r);throw e.digest=r,e}function o(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8586:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}}),function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(r||(r={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},83953:(e,t,r)=>{"use strict";var s;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectType:function(){return s},getRedirectError:function(){return l},getRedirectStatusCodeFromError:function(){return m},getRedirectTypeFromError:function(){return p},getURLFromRedirectError:function(){return f},isRedirectError:function(){return u},permanentRedirect:function(){return d},redirect:function(){return c}});let o=r(54580),n=r(72934),a=r(8586),i="NEXT_REDIRECT";function l(e,t,r){void 0===r&&(r=a.RedirectStatusCode.TemporaryRedirect);let s=Error(i);s.digest=i+";"+t+";"+e+";"+r+";";let n=o.requestAsyncStorage.getStore();return n&&(s.mutableCookies=n.mutableCookies),s}function c(e,t){void 0===t&&(t="replace");let r=n.actionAsyncStorage.getStore();throw l(e,t,(null==r?void 0:r.isAction)?a.RedirectStatusCode.SeeOther:a.RedirectStatusCode.TemporaryRedirect)}function d(e,t){void 0===t&&(t="replace");let r=n.actionAsyncStorage.getStore();throw l(e,t,(null==r?void 0:r.isAction)?a.RedirectStatusCode.SeeOther:a.RedirectStatusCode.PermanentRedirect)}function u(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r,s,o]=e.digest.split(";",4),n=Number(o);return t===i&&("replace"===r||"push"===r)&&"string"==typeof s&&!isNaN(n)&&n in a.RedirectStatusCode}function f(e){return u(e)?e.digest.split(";",3)[2]:null}function p(e){if(!u(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}function m(e){if(!u(e))throw Error("Not a redirect error");return Number(e.digest.split(";",4)[3])}(function(e){e.push="push",e.replace="replace"})(s||(s={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},51806:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f,metadata:()=>u});var s=r(19510),o=r(10527),n=r.n(o),a=r(36822),i=r.n(a);r(5023);var l=r(68570);(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#useCustomer`);let c=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#CustomerProvider`),d=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#ToastProvider`);(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#useToast`);let u={title:"Ankkor | Timeless Menswear",description:"Elevated essentials for the discerning gentleman. Impeccably tailored garments crafted from the finest materials.",keywords:["menswear","luxury clothing","tailored","shirts","accessories"],icons:{icon:[{url:"/logo.PNG",sizes:"32x32",type:"image/png"},{url:"/logo.PNG",sizes:"16x16",type:"image/png"}],shortcut:"/logo.PNG",apple:"/logo.PNG"}};function f({children:e}){return s.jsx("html",{lang:"en",children:s.jsx("body",{className:`${n().variable} ${i().variable} font-sans antialiased min-h-screen bg-[#f8f8f5]`,children:s.jsx(d,{children:s.jsx(c,{children:s.jsx("main",{style:{paddingTop:0},className:"transition-all duration-300",children:e})})})})})}},12523:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\not-found.tsx#default`)},8179:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>d,generateMetadata:()=>c});var o=r(19510),n=r(58585),a=r(19910),i=r(80151),l=e([a]);async function c({params:e}){let{slug:t}=e;try{let e=await (0,a.gF)(t);if(!e)return{title:"Product Not Found | Ankkor",description:"The requested product could not be found."};return{title:`${e.name} | Ankkor`,description:e.shortDescription||e.description||"Luxury menswear from Ankkor.",openGraph:{images:e.image?[{url:e.image.sourceUrl,alt:e.name}]:[]}}}catch(e){return console.error("Error generating product metadata:",e),{title:"Product | Ankkor",description:"Luxury menswear from Ankkor."}}}async function d({params:e}){let{slug:t}=e;try{let e=await (0,a.gF)(t);return e||(0,n.notFound)(),o.jsx(i.Z,{product:e})}catch(e){console.error("Error fetching product:",e),(0,n.notFound)()}}a=(l.then?(await l)():l)[0],s()}catch(e){s(e)}})},80151:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\product\ProductDetail.tsx#default`)},5023:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,2344,4766,4868,2481,7207,2325,9910,6806],()=>r(37523));module.exports=s})();