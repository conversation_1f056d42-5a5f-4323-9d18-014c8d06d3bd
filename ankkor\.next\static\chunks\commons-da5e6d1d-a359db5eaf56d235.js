"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7158],{50749:function(e,t,s){var a=s(57437),r=s(2265),l=s(99376),o=s(29501),i=s(15863),n=s(3371);t.default=e=>{let{mode:t,redirectUrl:s="/"}=e,c=(0,l.useRouter)(),{refreshCustomer:d}=(0,n.O)(),[u,m]=(0,r.useState)(!1),[x,f]=(0,r.useState)(null),[h,p]=(0,r.useState)(null),[g,b]=(0,r.useState)(null),N="login"===t,{register:w,handleSubmit:y,watch:j,formState:{errors:v}}=(0,o.cI)({mode:"onBlur"}),S=j("password",""),k=async e=>{m(!0),f(null),p(null),b(null);try{if(N){console.log("Attempting login with:",e.email);let t=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"login",username:e.email,password:e.password})}),a=await t.json();a.success?(p("Login successful! Redirecting..."),setTimeout(async()=>{await d(),c.push(s),c.refresh()},500)):f(a.message||"Login failed. Please check your credentials.")}else{console.log("Attempting registration for:",e.email);let t=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"register",email:e.email,firstName:e.firstName,lastName:e.lastName,password:e.password})}),a=await t.json();a.success?(p("Registration successful! Redirecting..."),await d(),setTimeout(()=>{c.push(s),c.refresh()},1e3)):f(a.message||"Registration failed. Please try again.")}}catch(e){console.error("Authentication error:",e),f(e.message||"An error occurred during authentication"),p(null)}finally{m(!1)}};return(0,a.jsxs)("div",{className:"max-w-md mx-auto bg-white p-8 border border-gray-200",children:[(0,a.jsx)("h2",{className:"text-2xl font-serif mb-6 text-center",children:N?"Sign In to Your Account":"Create an Account"}),x&&(0,a.jsx)("div",{className:"mb-4 p-3 bg-red-50 text-red-700 text-sm border border-red-200",children:x}),h&&(0,a.jsx)("div",{className:"mb-4 p-3 bg-green-50 text-green-700 text-sm border border-green-200",children:h}),g&&!1,(0,a.jsxs)("form",{onSubmit:y(k),className:"space-y-4",children:[!N&&(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"firstName",className:"block text-sm font-medium text-gray-700 mb-1",children:"First Name"}),(0,a.jsx)("input",{id:"firstName",type:"text",className:"w-full p-2 border ".concat(v.firstName?"border-red-500":"border-gray-300"),...w("firstName",{required:"First name is required"})}),v.firstName&&(0,a.jsx)("p",{className:"mt-1 text-xs text-red-600",children:v.firstName.message})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"lastName",className:"block text-sm font-medium text-gray-700 mb-1",children:"Last Name"}),(0,a.jsx)("input",{id:"lastName",type:"text",className:"w-full p-2 border ".concat(v.lastName?"border-red-500":"border-gray-300"),...w("lastName",{required:"Last name is required"})}),v.lastName&&(0,a.jsx)("p",{className:"mt-1 text-xs text-red-600",children:v.lastName.message})]})]})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email Address"}),(0,a.jsx)("input",{id:"email",type:"email",className:"w-full p-2 border ".concat(v.email?"border-red-500":"border-gray-300"),...w("email",{required:"Email is required",pattern:{value:/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,message:"Invalid email address"}})}),v.email&&(0,a.jsx)("p",{className:"mt-1 text-xs text-red-600",children:v.email.message})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),(0,a.jsx)("input",{id:"password",type:"password",className:"w-full p-2 border ".concat(v.password?"border-red-500":"border-gray-300"),...w("password",{required:"Password is required",minLength:{value:8,message:"Password must be at least 8 characters"}})}),v.password&&(0,a.jsx)("p",{className:"mt-1 text-xs text-red-600",children:v.password.message})]}),!N&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:"Confirm Password"}),(0,a.jsx)("input",{id:"confirmPassword",type:"password",className:"w-full p-2 border ".concat(v.confirmPassword?"border-red-500":"border-gray-300"),...w("confirmPassword",{required:"Please confirm your password",validate:e=>e===S||"Passwords do not match"})}),v.confirmPassword&&(0,a.jsx)("p",{className:"mt-1 text-xs text-red-600",children:v.confirmPassword.message})]}),(0,a.jsx)("button",{type:"submit",disabled:u,className:"w-full bg-[#2c2c27] text-white py-2 px-4 hover:bg-[#4c4c47] transition-colors duration-300 disabled:bg-gray-400 disabled:cursor-not-allowed",children:u?(0,a.jsxs)("span",{className:"flex items-center justify-center",children:[(0,a.jsx)(i.Z,{className:"animate-spin mr-2 h-4 w-4"}),N?"Signing in...":"Creating account..."]}):N?"Sign In":"Create Account"})]}),N?(0,a.jsx)("div",{className:"mt-4 text-center",children:(0,a.jsx)("a",{href:"/forgot-password",className:"text-sm text-[#2c2c27] hover:text-[#8a8778] underline",children:"Forgot your password?"})}):null]})}},18686:function(e,t,s){s.d(t,{j:function(){return l}}),s(57437);var a=s(2265);s(87758);let r=(0,a.createContext)(void 0),l=()=>{let e=(0,a.useContext)(r);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e}},62670:function(e,t,s){var a=s(57437),r=s(2265),l=s(27648),o=s(43886),i=s(88997),n=s(15863),c=s(42449),d=s(87758),u=s(92371),m=s(3371),x=s(18686),f=s(57152),h=s(70597),p=s(11738);let g=e=>{if("number"==typeof e)return e.toString();if(!e)return"0";let t=parseFloat(e.toString().replace(/[^\d.-]/g,""));return isNaN(t)?"0":t.toString()};t.Z=e=>{let{id:t,name:s,price:b,image:N,slug:w,material:y,isNew:j=!1,stockStatus:v="IN_STOCK",compareAtPrice:S=null,regularPrice:k=null,salePrice:C=null,onSale:A=!1,currencySymbol:P=h.J6,currencyCode:F=h.EJ,shortDescription:T,type:O}=e,[E,I]=(0,r.useState)(!1),_=(0,d.rY)(),{openCart:L}=(0,x.j)(),{addToWishlist:R,isInWishlist:Z,removeFromWishlist:q}=(0,u.Y)(),{isAuthenticated:$}=(0,m.O)(),H=Z(t),D=async e=>{if(e.preventDefault(),e.stopPropagation(),!t||""===t){console.error("Cannot add to cart: Missing product ID for product",s),p.Am.error("Cannot add to cart: Invalid product");return}if(!E){I(!0),console.log("Adding product to cart: ".concat(s," (ID: ").concat(t,")"));try{await _.addToCart({productId:t,quantity:1,name:s,price:b,image:{url:N,altText:s}}),p.Am.success("".concat(s," added to cart!")),L()}catch(e){console.error("Failed to add ".concat(s," to cart:"),e),p.Am.error("Failed to add item to cart. Please try again.")}finally{I(!1)}}},K=e=>{e.preventDefault(),e.stopPropagation(),H?(q(t),p.Am.success("Removed from wishlist")):(R({id:t,name:s,price:g(b),image:N,handle:w,material:y||"Material not specified",variantId:t}),$?p.Am.success("Added to your wishlist"):p.Am.success("Added to wishlist (saved locally)"))},J=S&&parseFloat(S)>parseFloat(b)?Math.round((parseFloat(S)-parseFloat(b))/parseFloat(S)*100):null,M="IN_STOCK"!==v;return(0,a.jsxs)(o.E.div,{className:"group relative",whileHover:{y:-5},transition:{duration:.3},children:[(0,a.jsxs)(l.default,{href:"/product/".concat(w),className:"block",children:[(0,a.jsxs)("div",{className:"relative overflow-hidden mb-4",children:[(0,a.jsx)("div",{className:"aspect-[3/4] relative bg-[#f4f3f0] overflow-hidden",children:(0,a.jsx)(f.Z,{src:N,alt:s,fill:!0,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",animate:!0,className:"h-full"})}),(0,a.jsxs)("div",{className:"absolute bottom-0 left-0 right-0 p-4 flex justify-between opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:[(0,a.jsx)(o.E.button,{onClick:K,className:"p-2 rounded-none ".concat(H?"bg-[#2c2c27]":"bg-[#f8f8f5]"),whileHover:{scale:1.05},whileTap:{scale:.95},"aria-label":H?"Remove from wishlist":"Add to wishlist",children:(0,a.jsx)(i.Z,{className:"h-5 w-5 ".concat(H?"text-[#f4f3f0] fill-current":"text-[#2c2c27]")})}),(0,a.jsx)(o.E.button,{onClick:D,className:"p-2 rounded-none ".concat(M||E?"bg-gray-400 cursor-not-allowed":"bg-[#2c2c27]"," text-[#f4f3f0]"),whileHover:M||E?{}:{scale:1.05},whileTap:M||E?{}:{scale:.95},"aria-label":M?"Out of stock":E?"Adding to cart...":"Add to cart",disabled:M||E,children:E?(0,a.jsx)(n.Z,{className:"h-5 w-5 animate-spin"}):(0,a.jsx)(c.Z,{className:"h-5 w-5"})})]}),j&&(0,a.jsx)("div",{className:"absolute top-0 left-0 bg-[#2c2c27] text-[#f4f3f0] py-1 px-3 text-xs uppercase tracking-wider",children:"New"}),M&&(0,a.jsx)("div",{className:"absolute top-0 right-0 bg-red-600 text-[#f4f3f0] py-1 px-3 text-xs uppercase tracking-wider",children:"Out of Stock"}),!M&&J&&(0,a.jsxs)("div",{className:"absolute top-0 right-0 bg-[#8a8778] text-[#f4f3f0] py-1 px-3 text-xs uppercase tracking-wider",children:[J,"% Off"]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h3",{className:"font-serif text-lg text-[#2c2c27] mb-1 line-clamp-2",children:s}),y&&(0,a.jsx)("p",{className:"text-[#8a8778] text-xs",children:y}),O&&(0,a.jsx)("p",{className:"text-[#8a8778] text-xs capitalize",children:O.toLowerCase().replace("_"," ")}),T&&(0,a.jsx)("p",{className:"text-[#5c5c52] text-xs line-clamp-2",dangerouslySetInnerHTML:{__html:T.replace(/<[^>]*>/g,"")}}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 product-card-price",children:[(0,a.jsx)("p",{className:"text-[#2c2c27] font-medium",children:A&&C?C.toString().includes("₹")||C.toString().includes("$")||C.toString().includes("€")||C.toString().includes("\xa3")?C:"".concat(P).concat(C):b.toString().includes("₹")||b.toString().includes("$")||b.toString().includes("€")||b.toString().includes("\xa3")?b:"".concat(P).concat(b)}),A&&k&&(0,a.jsx)("p",{className:"text-[#8a8778] text-xs line-through product-card-compare-price",children:k.toString().includes("₹")||k.toString().includes("$")||k.toString().includes("€")||k.toString().includes("\xa3")?k:"".concat(P).concat(k)}),!A&&S&&parseFloat(S.toString().replace(/[₹$€£]/g,""))>parseFloat(b.toString().replace(/[₹$€£]/g,""))&&(0,a.jsx)("p",{className:"text-[#8a8778] text-xs line-through product-card-compare-price",children:S.toString().includes("₹")||S.toString().includes("$")||S.toString().includes("€")||S.toString().includes("\xa3")?S:"".concat(P).concat(S)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"flex items-center gap-2",children:"IN_STOCK"===v?(0,a.jsx)("span",{className:"text-green-600 text-xs font-medium",children:"✓ In Stock"}):"OUT_OF_STOCK"===v?(0,a.jsx)("span",{className:"text-red-600 text-xs font-medium",children:"✗ Out of Stock"}):"ON_BACKORDER"===v?(0,a.jsx)("span",{className:"text-orange-600 text-xs font-medium",children:"⏳ Backorder"}):(0,a.jsx)("span",{className:"text-gray-600 text-xs font-medium",children:"? Unknown"})}),A&&(0,a.jsx)("span",{className:"bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full font-medium",children:"Sale"})]})]})]})]}),(0,a.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,a.jsx)(o.E.button,{onClick:D,className:"w-full py-3 px-4 transition-all duration-200 ".concat(M||E?"bg-gray-400 text-gray-600 cursor-not-allowed":"bg-[#2c2c27] text-[#f4f3f0] hover:bg-[#1a1a17]"),whileHover:M||E?{}:{scale:1.02},whileTap:M||E?{}:{scale:.98},"aria-label":M?"Out of stock":E?"Adding to cart...":"Add to cart",disabled:M||E,children:(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[E?(0,a.jsx)(n.Z,{className:"h-4 w-4 animate-spin"}):(0,a.jsx)(c.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:M?"Out of Stock":E?"Adding...":"Add to Cart"})]})}),(0,a.jsx)(o.E.button,{onClick:K,className:"w-full py-3 px-4 border transition-all duration-200 ".concat(H?"bg-[#2c2c27] text-[#f4f3f0] border-[#2c2c27]":"bg-transparent text-[#2c2c27] border-[#2c2c27] hover:bg-[#2c2c27] hover:text-[#f4f3f0]"),whileHover:{scale:1.02},whileTap:{scale:.98},"aria-label":H?"Remove from wishlist":"Add to wishlist",children:(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,a.jsx)(i.Z,{className:"h-4 w-4 ".concat(H?"fill-current":"")}),(0,a.jsx)("span",{className:"text-sm font-medium",children:H?"In Wishlist":"Add to Wishlist"})]})})]})]})}},3371:function(e,t,s){s.d(t,{CustomerProvider:function(){return c},O:function(){return n}});var a=s(57437),r=s(2265),l=s(99376),o=s(71917);let i=(0,r.createContext)({customer:null,isLoading:!0,isAuthenticated:!1,token:null,login:async()=>{},register:async()=>{},logout:()=>{},updateProfile:async()=>{},error:null,refreshCustomer:async()=>{}}),n=()=>(0,r.useContext)(i);function c(e){let{children:t}=e,[s,n]=(0,r.useState)(null),[c,d]=(0,r.useState)(!0),[u,m]=(0,r.useState)(null),[x,f]=(0,r.useState)(null),h=(0,l.useRouter)(),{addToast:p}=(0,o.p)(),g=e=>e?{...e,displayName:e.displayName||e.username||"".concat(e.firstName||""," ").concat(e.lastName||"").trim()||"User"}:null,b=async()=>{try{console.log("CustomerProvider: Checking authentication via /api/auth/me");let e=await fetch("/api/auth/me",{method:"GET",credentials:"include"});console.log("CustomerProvider: Auth API response status:",e.status);let t=await e.json();if(console.log("CustomerProvider: Auth API result:",t),!t.success||!t.customer)return f(null),{success:!1,message:t.message||"Not authenticated"};{let e=t.token;return console.log("CustomerProvider: Token from API response:",!!e),f(e||null),{success:!0,customer:t.customer,token:e}}}catch(e){return console.error("CustomerProvider: Error checking authentication:",e),f(null),{success:!1,message:"Network error"}}},N=async()=>{try{let e=await b();if(e.success){let t={...e.customer,token:e.token};n(g(t)),console.log("Customer data refreshed successfully"),console.log("Token available after refresh:",!!e.token)}else console.log("Failed to refresh customer data:",e.message),n(null),f(null)}catch(e){console.error("Error refreshing customer data:",e),n(null),f(null)}};(0,r.useEffect)(()=>{(async()=>{try{d(!0);let e=await b();if(e.success){console.log("Found valid authentication, customer data loaded"),console.log("Token available on mount:",!!e.token);let t={...e.customer,token:e.token};n(g(t))}else console.log("No valid authentication found:",e.message),n(null),f(null)}catch(e){console.error("Error checking customer session:",e),n(null),f(null)}finally{d(!1)}})()},[]);let w=async e=>{d(!0),m(null);try{throw Error("Login temporarily disabled for build fix")}catch(t){let e="Login temporarily disabled for build fix";throw m(e),p(e,"error"),t}finally{d(!1)}},y=async e=>{d(!0),m(null);try{throw Error("Register temporarily disabled for build fix")}catch(t){let e="Register temporarily disabled for build fix";throw m(e),p(e,"error"),t}finally{d(!1)}},j=async e=>{d(!0),m(null);try{throw Error("Profile update temporarily disabled for build fix")}catch(t){let e="Profile update temporarily disabled for build fix";throw m(e),p(e,"error"),t}finally{d(!1)}};return(0,a.jsx)(i.Provider,{value:{customer:s,isLoading:c,isAuthenticated:!!s,token:x,login:w,register:y,logout:()=>{n(null),f(null),console.log("Logout completed, token cleared"),p("You have been signed out successfully","info"),h.push("/"),h.refresh()},updateProfile:j,error:u,refreshCustomer:N},children:t})}},6658:function(e,t,s){s.d(t,{r:function(){return l}}),s(57437);var a=s(2265);s(99376),s(29456);let r=(0,a.createContext)({isLoading:!1,setLoading:()=>{},variant:"thread",setVariant:()=>{}}),l=()=>(0,a.useContext)(r)}}]);