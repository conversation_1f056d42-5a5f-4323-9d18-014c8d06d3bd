(()=>{var e={};e.id=1978,e.ids=[1978],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},24541:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>o.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d}),s(76469),s(51806),s(12523);var t=s(23191),a=s(88716),n=s(37922),o=s.n(n),i=s(95231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(r,l);let d=["",{children:["order-confirmed",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,76469)),"E:\\ankkorwoo\\ankkor\\src\\app\\order-confirmed\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,51806)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],c=["E:\\ankkorwoo\\ankkor\\src\\app\\order-confirmed\\page.tsx"],u="/order-confirmed/page",m={require:s,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/order-confirmed/page",pathname:"/order-confirmed",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},13417:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,12994,23)),Promise.resolve().then(s.t.bind(s,96114,23)),Promise.resolve().then(s.t.bind(s,9727,23)),Promise.resolve().then(s.t.bind(s,79671,23)),Promise.resolve().then(s.t.bind(s,41868,23)),Promise.resolve().then(s.t.bind(s,84759,23))},96799:(e,r,s)=>{Promise.resolve().then(s.bind(s,68897)),Promise.resolve().then(s.bind(s,75367))},54039:(e,r,s)=>{Promise.resolve().then(s.bind(s,83846))},64305:(e,r,s)=>{Promise.resolve().then(s.bind(s,40927))},28916:(e,r,s)=>{"use strict";s.d(r,{Z:()=>t});let t=(0,s(76557).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},48705:(e,r,s)=>{"use strict";s.d(r,{Z:()=>t});let t=(0,s(76557).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},14228:(e,r,s)=>{"use strict";s.d(r,{Z:()=>t});let t=(0,s(76557).Z)("Truck",[["path",{d:"M5 18H3c-.6 0-1-.4-1-1V7c0-.6.4-1 1-1h10c.6 0 1 .4 1 1v11",key:"hs4xqm"}],["path",{d:"M14 9h4l4 4v4c0 .6-.4 1-1 1h-2",key:"11fp61"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}]])},83846:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>i});var t=s(10326);s(17577);var a=s(33265);let n=()=>t.jsx("div",{className:"animate-pulse flex space-x-4",children:(0,t.jsxs)("div",{className:"flex-1 space-y-6 py-1",children:[t.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[t.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-2"}),t.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-1"})]}),t.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"})]})]})}),o=(0,a.default)(async()=>{},{loadableGenerated:{modules:["app\\not-found.tsx -> ./not-found-content"]},ssr:!1,loading:()=>t.jsx(n,{})});function i(){return t.jsx("div",{className:"container mx-auto py-20",children:t.jsx(o,{})})}},40927:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>u});var t=s(10326),a=s(17577),n=s(35047),o=s(54659),i=s(28916),l=s(48705),d=s(14228),c=s(91664);function u(){let e=(0,n.useRouter)();(0,n.useSearchParams)();let[r,s]=(0,a.useState)(null);return r?t.jsx("div",{className:"container mx-auto px-4 py-8",children:(0,t.jsxs)("div",{className:"max-w-2xl mx-auto text-center",children:[t.jsx("div",{className:"mb-8",children:t.jsx("div",{className:"mx-auto w-24 h-24 bg-green-100 rounded-full flex items-center justify-center",children:t.jsx(o.Z,{className:"w-12 h-12 text-green-600"})})}),(0,t.jsxs)("div",{className:"mb-8",children:[t.jsx("h1",{className:"text-3xl font-serif mb-4 text-gray-900",children:"Thank You for Your Order!"}),t.jsx("p",{className:"text-lg text-gray-600 mb-6",children:"Your order has been successfully placed and is being processed."}),(0,t.jsxs)("div",{className:"bg-gray-50 border rounded-lg p-6 mb-6",children:[t.jsx("h2",{className:"text-lg font-medium mb-2",children:"Order Details"}),(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[t.jsx("span",{className:"text-gray-600",children:"Order ID:"}),(0,t.jsxs)("span",{className:"font-mono text-lg font-medium text-[#2c2c27]",children:["#",r]})]})]})]}),(0,t.jsxs)("div",{className:"mb-8",children:[t.jsx("h3",{className:"text-xl font-medium mb-6",children:"What happens next?"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("div",{className:"mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-3",children:t.jsx(i.Z,{className:"w-6 h-6 text-blue-600"})}),t.jsx("h4",{className:"font-medium mb-2",children:"Payment Confirmed"}),t.jsx("p",{className:"text-sm text-gray-600",children:"Your payment has been successfully processed"})]}),(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("div",{className:"mx-auto w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mb-3",children:t.jsx(l.Z,{className:"w-6 h-6 text-yellow-600"})}),t.jsx("h4",{className:"font-medium mb-2",children:"Order Processing"}),t.jsx("p",{className:"text-sm text-gray-600",children:"We're preparing your items for shipment"})]}),(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("div",{className:"mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-3",children:t.jsx(d.Z,{className:"w-6 h-6 text-green-600"})}),t.jsx("h4",{className:"font-medium mb-2",children:"On the Way"}),t.jsx("p",{className:"text-sm text-gray-600",children:"Your order will be shipped soon"})]})]})]}),(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8",children:[t.jsx("h3",{className:"font-medium mb-2",children:"Order Confirmation Email"}),t.jsx("p",{className:"text-sm text-gray-600",children:"We've sent an order confirmation email with your order details and tracking information. Please check your inbox and spam folder."})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[t.jsx(c.z,{onClick:()=>e.push("/"),className:"w-full md:w-auto bg-[#2c2c27] hover:bg-[#3c3c37] text-white px-8 py-3",children:"Continue Shopping"}),t.jsx("div",{className:"text-center",children:t.jsx("button",{onClick:()=>e.push("/account"),className:"text-[#2c2c27] hover:underline text-sm",children:"View Order History"})})]}),(0,t.jsxs)("div",{className:"mt-12 pt-8 border-t border-gray-200",children:[t.jsx("h3",{className:"font-medium mb-4",children:"Need Help?"}),t.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"If you have any questions about your order, please don't hesitate to contact us."}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("p",{children:[t.jsx("span",{className:"font-medium",children:"Email:"})," ",t.jsx("a",{href:"mailto:<EMAIL>",className:"text-[#2c2c27] hover:underline",children:"<EMAIL>"})]}),(0,t.jsxs)("p",{children:[t.jsx("span",{className:"font-medium",children:"Phone:"})," ",t.jsx("a",{href:"tel:+**********",className:"text-[#2c2c27] hover:underline",children:"+91 12345 67890"})]})]})]})]})}):null}},68897:(e,r,s)=>{"use strict";s.d(r,{CustomerProvider:()=>d,O:()=>l});var t=s(10326),a=s(17577),n=s(35047),o=s(75367);let i=(0,a.createContext)({customer:null,isLoading:!0,isAuthenticated:!1,token:null,login:async()=>{},register:async()=>{},logout:()=>{},updateProfile:async()=>{},error:null,refreshCustomer:async()=>{}}),l=()=>(0,a.useContext)(i);function d({children:e}){let[r,s]=(0,a.useState)(null),[l,d]=(0,a.useState)(!0),[c,u]=(0,a.useState)(null),[m,x]=(0,a.useState)(null),h=(0,n.useRouter)(),{addToast:f}=(0,o.p)(),p=e=>e?{...e,displayName:e.displayName||e.username||`${e.firstName||""} ${e.lastName||""}`.trim()||"User"}:null,g=async()=>{try{console.log("CustomerProvider: Checking authentication via /api/auth/me");let e=await fetch("/api/auth/me",{method:"GET",credentials:"include"});console.log("CustomerProvider: Auth API response status:",e.status);let r=await e.json();if(console.log("CustomerProvider: Auth API result:",r),!r.success||!r.customer)return x(null),{success:!1,message:r.message||"Not authenticated"};{let e=r.token;return console.log("CustomerProvider: Token from API response:",!!e),x(e||null),{success:!0,customer:r.customer,token:e}}}catch(e){return console.error("CustomerProvider: Error checking authentication:",e),x(null),{success:!1,message:"Network error"}}},v=async()=>{try{let e=await g();if(e.success){let r={...e.customer,token:e.token};s(p(r)),console.log("Customer data refreshed successfully"),console.log("Token available after refresh:",!!e.token)}else console.log("Failed to refresh customer data:",e.message),s(null),x(null)}catch(e){console.error("Error refreshing customer data:",e),s(null),x(null)}},b=async e=>{d(!0),u(null);try{throw Error("Login temporarily disabled for build fix")}catch(r){let e="Login temporarily disabled for build fix";throw u(e),f(e,"error"),r}finally{d(!1)}},y=async e=>{d(!0),u(null);try{throw Error("Register temporarily disabled for build fix")}catch(r){let e="Register temporarily disabled for build fix";throw u(e),f(e,"error"),r}finally{d(!1)}},j=async e=>{d(!0),u(null);try{throw Error("Profile update temporarily disabled for build fix")}catch(r){let e="Profile update temporarily disabled for build fix";throw u(e),f(e,"error"),r}finally{d(!1)}};return t.jsx(i.Provider,{value:{customer:r,isLoading:l,isAuthenticated:!!r,token:m,login:b,register:y,logout:()=>{s(null),x(null),console.log("Logout completed, token cleared"),f("You have been signed out successfully","info"),h.push("/"),h.refresh()},updateProfile:j,error:c,refreshCustomer:v},children:e})}},91664:(e,r,s)=>{"use strict";s.d(r,{z:()=>l});var t=s(10326);s(17577);var a=s(34214),n=s(79360),o=s(51223);let i=(0,n.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-[#2c2c27] text-[#f4f3f0] shadow-xs hover:bg-[#3d3d35]",destructive:"bg-[#ff4d4f] text-white shadow-xs hover:bg-[#ff4d4f]/90 focus-visible:ring-[#ff4d4f]/20 dark:focus-visible:ring-[#ff4d4f]/40",outline:"border border-[#e5e2d9] bg-[#f8f8f5] shadow-xs hover:bg-[#f4f3f0] hover:text-[#2c2c27]",secondary:"bg-[#e5e2d9] text-[#2c2c27] shadow-xs hover:bg-[#e5e2d9]/80",ghost:"hover:bg-[#f4f3f0] hover:text-[#2c2c27]",link:"text-[#2c2c27] underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:r,size:s,asChild:n=!1,...l}){let d=n?a.g7:"button";return t.jsx(d,{"data-slot":"button",className:(0,o.cn)(i({variant:r,size:s,className:e})),...l})}},75367:(e,r,s)=>{"use strict";s.d(r,{ToastProvider:()=>m,p:()=>x});var t=s(10326),a=s(17577),n=s(92148),o=s(86462),i=s(54659),l=s(87888),d=s(18019),c=s(94019);let u=(0,a.createContext)(void 0);function m({children:e}){let[r,s]=(0,a.useState)([]);return(0,t.jsxs)(u.Provider,{value:{toasts:r,addToast:(e,r="info",t=3e3)=>{let a=Math.random().toString(36).substring(2,9);s(s=>[...s,{id:a,message:e,type:r,duration:t}])},removeToast:e=>{s(r=>r.filter(r=>r.id!==e))}},children:[e,t.jsx(f,{})]})}function x(){let e=(0,a.useContext)(u);if(void 0===e)throw Error("useToast must be used within a ToastProvider");return e}function h({toast:e,onRemove:r}){return(0,t.jsxs)(n.E.div,{initial:{opacity:0,y:-50},animate:{opacity:1,y:0},exit:{opacity:0,x:300},className:`flex items-center p-4 rounded-lg border shadow-lg ${(()=>{switch(e.type){case"success":return"bg-[#f4f3f0] border-[#8a8778]";case"error":return"bg-red-50 border-red-200";default:return"bg-[#f8f8f5] border-[#e5e2d9]"}})()} max-w-md`,children:[t.jsx(()=>{switch(e.type){case"success":return t.jsx(i.Z,{className:"h-5 w-5"});case"error":return t.jsx(l.Z,{className:"h-5 w-5"});default:return t.jsx(d.Z,{className:"h-5 w-5"})}},{}),t.jsx("span",{className:"ml-3 text-sm font-medium flex-1",children:e.message}),t.jsx("button",{onClick:r,className:"ml-4 text-gray-400 hover:text-gray-600",children:t.jsx(c.Z,{className:"h-4 w-4"})})]})}function f(){let{toasts:e,removeToast:r}=x();return t.jsx("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:t.jsx(o.M,{children:e.map(e=>t.jsx(h,{toast:e,onRemove:()=>r(e.id)},e.id))})})}},51223:(e,r,s)=>{"use strict";s.d(r,{cn:()=>n});var t=s(41135),a=s(31009);function n(...e){return(0,a.m6)((0,t.W)(e))}},51806:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>m,metadata:()=>u});var t=s(19510),a=s(10527),n=s.n(a),o=s(36822),i=s.n(o);s(5023);var l=s(68570);(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#useCustomer`);let d=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#CustomerProvider`),c=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#ToastProvider`);(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#useToast`);let u={title:"Ankkor | Timeless Menswear",description:"Elevated essentials for the discerning gentleman. Impeccably tailored garments crafted from the finest materials.",keywords:["menswear","luxury clothing","tailored","shirts","accessories"],icons:{icon:[{url:"/logo.PNG",sizes:"32x32",type:"image/png"},{url:"/logo.PNG",sizes:"16x16",type:"image/png"}],shortcut:"/logo.PNG",apple:"/logo.PNG"}};function m({children:e}){return t.jsx("html",{lang:"en",children:t.jsx("body",{className:`${n().variable} ${i().variable} font-sans antialiased min-h-screen bg-[#f8f8f5]`,children:t.jsx(c,{children:t.jsx(d,{children:t.jsx("main",{style:{paddingTop:0},className:"transition-all duration-300",children:e})})})})})}},12523:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\not-found.tsx#default`)},76469:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\order-confirmed\page.tsx#default`)},5023:()=>{}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[8948,2344,2325],()=>s(24541));module.exports=t})();