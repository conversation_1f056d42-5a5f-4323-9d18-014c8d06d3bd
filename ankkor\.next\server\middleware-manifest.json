{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api\\/status\\/redis(.json)?[\\/#\\?]?$", "originalSource": "/api/status/redis"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/account(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/account/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/checkout(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/checkout/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/sign-in(.json)?[\\/#\\?]?$", "originalSource": "/sign-in"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/sign-up(.json)?[\\/#\\?]?$", "originalSource": "/sign-up"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "JSBnZksS_N7WF1IrtumII", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "f+E4bCAfraqrtVl0BFN/yF9GzSjfZGX1wnst/LTn23o=", "__NEXT_PREVIEW_MODE_ID": "963324960606b751318095267526ac83", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7b4c3289db05276e8609cfac0a45b81ac431492d5adc80569dd3210e4fa8a001", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c078504545c81d6bc3bcdf186d98b82b2a63bd0921b695bf27910b49d4e0b9fb"}}}, "functions": {}, "sortedMiddleware": ["/"]}