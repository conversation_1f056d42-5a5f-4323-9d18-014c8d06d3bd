(()=>{var e={};e.id=6232,e.ids=[6232],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},93690:e=>{"use strict";e.exports=import("graphql-request")},85051:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c}),s(38958),s(51806),s(12523);var r=s(23191),a=s(88716),o=s(37922),n=s.n(o),i=s(95231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(t,l);let c=["",{children:["woocommerce-test",{children:["success",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,38958)),"E:\\ankkorwoo\\ankkor\\src\\app\\woocommerce-test\\success\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,51806)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],d=["E:\\ankkorwoo\\ankkor\\src\\app\\woocommerce-test\\success\\page.tsx"],u="/woocommerce-test/success/page",m={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/woocommerce-test/success/page",pathname:"/woocommerce-test/success",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},13417:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,12994,23)),Promise.resolve().then(s.t.bind(s,96114,23)),Promise.resolve().then(s.t.bind(s,9727,23)),Promise.resolve().then(s.t.bind(s,79671,23)),Promise.resolve().then(s.t.bind(s,41868,23)),Promise.resolve().then(s.t.bind(s,84759,23))},96799:(e,t,s)=>{Promise.resolve().then(s.bind(s,68897)),Promise.resolve().then(s.bind(s,75367))},54039:(e,t,s)=>{Promise.resolve().then(s.bind(s,83846))},44928:(e,t,s)=>{Promise.resolve().then(s.bind(s,29099))},90434:(e,t,s)=>{"use strict";s.d(t,{default:()=>a.a});var r=s(79404),a=s.n(r)},83846:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var r=s(10326);s(17577);var a=s(33265);let o=()=>r.jsx("div",{className:"animate-pulse flex space-x-4",children:(0,r.jsxs)("div",{className:"flex-1 space-y-6 py-1",children:[r.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[r.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-2"}),r.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-1"})]}),r.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"})]})]})}),n=(0,a.default)(async()=>{},{loadableGenerated:{modules:["app\\not-found.tsx -> ./not-found-content"]},ssr:!1,loading:()=>r.jsx(o,{})});function i(){return r.jsx("div",{className:"container mx-auto py-20",children:r.jsx(n,{})})}},29099:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{default:()=>c});var a=s(10326),o=s(17577),n=s(90434),i=s(61296),l=e([i]);function c(){let[e,t]=(0,o.useState)(null),[s,r]=(0,o.useState)(!0);return a.jsx("div",{className:"container mx-auto py-12",children:(0,a.jsxs)("div",{className:"max-w-md mx-auto bg-white p-8 border border-gray-200",children:[a.jsx("h1",{className:"text-2xl font-bold mb-6 text-center",children:"Authentication Successful"}),s?a.jsx("p",{className:"text-center",children:"Loading user data..."}):e?(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("div",{className:"p-4 bg-green-50 border border-green-200 text-green-700",children:a.jsx("p",{className:"font-medium",children:"Successfully authenticated!"})}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("h2",{className:"text-lg font-medium",children:"User Information"}),(0,a.jsxs)("p",{children:[a.jsx("strong",{children:"Name:"})," ",e.firstName," ",e.lastName]}),(0,a.jsxs)("p",{children:[a.jsx("strong",{children:"Email:"})," ",e.email]}),(0,a.jsxs)("p",{children:[a.jsx("strong",{children:"ID:"})," ",e.id]})]})]}):a.jsx("div",{className:"p-4 bg-yellow-50 border border-yellow-200 text-yellow-700",children:a.jsx("p",{children:"No user data found. Authentication may have failed."})}),a.jsx("div",{className:"mt-8 flex justify-center",children:a.jsx(n.default,{href:"/woocommerce-test",className:"bg-[#2c2c27] text-white py-2 px-4 hover:bg-[#3c3c37]",children:"Back to Test Page"})})]})})}i=(l.then?(await l)():l)[0],r()}catch(e){r(e)}})},68897:(e,t,s)=>{"use strict";s.d(t,{CustomerProvider:()=>c,O:()=>l});var r=s(10326),a=s(17577),o=s(35047),n=s(75367);let i=(0,a.createContext)({customer:null,isLoading:!0,isAuthenticated:!1,token:null,login:async()=>{},register:async()=>{},logout:()=>{},updateProfile:async()=>{},error:null,refreshCustomer:async()=>{}}),l=()=>(0,a.useContext)(i);function c({children:e}){let[t,s]=(0,a.useState)(null),[l,c]=(0,a.useState)(!0),[d,u]=(0,a.useState)(null),[m,p]=(0,a.useState)(null),h=(0,o.useRouter)(),{addToast:g}=(0,n.p)(),f=e=>e?{...e,displayName:e.displayName||e.username||`${e.firstName||""} ${e.lastName||""}`.trim()||"User"}:null,x=async()=>{try{console.log("CustomerProvider: Checking authentication via /api/auth/me");let e=await fetch("/api/auth/me",{method:"GET",credentials:"include"});console.log("CustomerProvider: Auth API response status:",e.status);let t=await e.json();if(console.log("CustomerProvider: Auth API result:",t),!t.success||!t.customer)return p(null),{success:!1,message:t.message||"Not authenticated"};{let e=t.token;return console.log("CustomerProvider: Token from API response:",!!e),p(e||null),{success:!0,customer:t.customer,token:e}}}catch(e){return console.error("CustomerProvider: Error checking authentication:",e),p(null),{success:!1,message:"Network error"}}},y=async()=>{try{let e=await x();if(e.success){let t={...e.customer,token:e.token};s(f(t)),console.log("Customer data refreshed successfully"),console.log("Token available after refresh:",!!e.token)}else console.log("Failed to refresh customer data:",e.message),s(null),p(null)}catch(e){console.error("Error refreshing customer data:",e),s(null),p(null)}},v=async e=>{c(!0),u(null);try{throw Error("Login temporarily disabled for build fix")}catch(t){let e="Login temporarily disabled for build fix";throw u(e),g(e,"error"),t}finally{c(!1)}},w=async e=>{c(!0),u(null);try{throw Error("Register temporarily disabled for build fix")}catch(t){let e="Register temporarily disabled for build fix";throw u(e),g(e,"error"),t}finally{c(!1)}},k=async e=>{c(!0),u(null);try{throw Error("Profile update temporarily disabled for build fix")}catch(t){let e="Profile update temporarily disabled for build fix";throw u(e),g(e,"error"),t}finally{c(!1)}};return r.jsx(i.Provider,{value:{customer:t,isLoading:l,isAuthenticated:!!t,token:m,login:v,register:w,logout:()=>{s(null),p(null),console.log("Logout completed, token cleared"),g("You have been signed out successfully","info"),h.push("/"),h.refresh()},updateProfile:k,error:d,refreshCustomer:y},children:e})}},75367:(e,t,s)=>{"use strict";s.d(t,{ToastProvider:()=>m,p:()=>p});var r=s(10326),a=s(17577),o=s(92148),n=s(86462),i=s(54659),l=s(87888),c=s(18019),d=s(94019);let u=(0,a.createContext)(void 0);function m({children:e}){let[t,s]=(0,a.useState)([]);return(0,r.jsxs)(u.Provider,{value:{toasts:t,addToast:(e,t="info",r=3e3)=>{let a=Math.random().toString(36).substring(2,9);s(s=>[...s,{id:a,message:e,type:t,duration:r}])},removeToast:e=>{s(t=>t.filter(t=>t.id!==e))}},children:[e,r.jsx(g,{})]})}function p(){let e=(0,a.useContext)(u);if(void 0===e)throw Error("useToast must be used within a ToastProvider");return e}function h({toast:e,onRemove:t}){return(0,r.jsxs)(o.E.div,{initial:{opacity:0,y:-50},animate:{opacity:1,y:0},exit:{opacity:0,x:300},className:`flex items-center p-4 rounded-lg border shadow-lg ${(()=>{switch(e.type){case"success":return"bg-[#f4f3f0] border-[#8a8778]";case"error":return"bg-red-50 border-red-200";default:return"bg-[#f8f8f5] border-[#e5e2d9]"}})()} max-w-md`,children:[r.jsx(()=>{switch(e.type){case"success":return r.jsx(i.Z,{className:"h-5 w-5"});case"error":return r.jsx(l.Z,{className:"h-5 w-5"});default:return r.jsx(c.Z,{className:"h-5 w-5"})}},{}),r.jsx("span",{className:"ml-3 text-sm font-medium flex-1",children:e.message}),r.jsx("button",{onClick:t,className:"ml-4 text-gray-400 hover:text-gray-600",children:r.jsx(d.Z,{className:"h-4 w-4"})})]})}function g(){let{toasts:e,removeToast:t}=p();return r.jsx("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:r.jsx(n.M,{children:e.map(e=>r.jsx(h,{toast:e,onRemove:()=>t(e.id)},e.id))})})}},61296:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.d(t,{ts:()=>l,x4:()=>n,z2:()=>i});var a=s(93690);s(18201);var o=e([a]);a=(o.then?(await o)():o)[0],(0,a.gql)`
  mutation LoginUser($username: String!, $password: String!) {
    login(input: {
      clientMutationId: "login"
      username: $username
      password: $password
    }) {
      authToken
      refreshToken
      user {
        id
        databaseId
        email
        firstName
        lastName
      }
    }
  }
`,(0,a.gql)`
  mutation RegisterUser($input: RegisterCustomerInput!) {
    registerCustomer(input: $input) {
      clientMutationId
      authToken
      refreshToken
      customer {
        id
        databaseId
        email
        firstName
        lastName
      }
    }
  }
`,(0,a.gql)`
  mutation RefreshAuthToken($input: RefreshJwtAuthTokenInput!) {
    refreshJwtAuthToken(input: $input) {
      authToken
    }
  }
`,(0,a.gql)`
  query GetCustomer {
    customer {
      id
      databaseId
      email
      firstName
      lastName
      billing {
        firstName
        lastName
        company
        address1
        address2
        city
        state
        postcode
        country
        email
        phone
      }
      shipping {
        firstName
        lastName
        company
        address1
        address2
        city
        state
        postcode
        country
      }
      orders {
        nodes {
          id
          databaseId
          date
          status
          total
          lineItems {
            nodes {
              product {
                node {
                  id
                  name
                }
              }
              quantity
              total
            }
          }
        }
      }
    }
  }
`,(0,a.gql)`
  mutation UpdateCustomer($input: UpdateCustomerInput!) {
    updateCustomer(input: $input) {
      clientMutationId
      customer {
        id
        databaseId
        email
        firstName
        lastName
        displayName
        billing {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
          email
          phone
        }
        shipping {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
        }
      }
    }
  }
`;let c="https://maroon-lapwing-781450.hostingersite.com/graphql",d=c&&!c.startsWith("http")?`https://${c}`:c;async function n(e,t){try{let s=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"login",username:e,password:t}),credentials:"include"});if(!s.ok){let e=await s.json();throw Error(e.message||"Login failed")}let r=await s.json();if(r.success&&r.user)return"undefined"!=typeof localStorage&&localStorage.setItem("auth_session_started",Date.now().toString()),console.log("Login successful, user data received"),{success:!0,user:r.user,token:r.token};throw console.error("Login response missing user data"),Error("Login failed: Invalid response from server")}catch(e){return console.error("Login error:",e),{success:!1,message:e.message||"Login failed"}}}async function i(e,t,s,r){try{let a=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"register",email:e,firstName:t,lastName:s,password:r}),credentials:"include"});if(!a.ok){let e=await a.json();throw Error(e.message||"Registration failed")}let o=await a.json();if(o.success&&o.customer)return"undefined"!=typeof localStorage&&localStorage.setItem("auth_session_started",Date.now().toString()),console.log("Registration successful, user data received"),{success:!0,customer:o.customer,token:o.token};throw console.error("Registration response missing customer data"),Error("Registration failed: Invalid response from server")}catch(e){return console.error("Registration error:",e),{success:!1,message:e.message||"Registration failed"}}}async function l(){try{let e=await fetch("/api/auth/user",{method:"GET",headers:{"Content-Type":"application/json"}});if(401===e.status)return null;let t=await e.json();if(!e.ok||!t.success)return null;return t.user}catch(e){return console.error("Get user error:",e),null}}new a.GraphQLClient(d,{headers:{"Content-Type":"application/json"}}),r()}catch(e){r(e)}})},51806:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m,metadata:()=>u});var r=s(19510),a=s(10527),o=s.n(a),n=s(36822),i=s.n(n);s(5023);var l=s(68570);(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#useCustomer`);let c=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#CustomerProvider`),d=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#ToastProvider`);(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#useToast`);let u={title:"Ankkor | Timeless Menswear",description:"Elevated essentials for the discerning gentleman. Impeccably tailored garments crafted from the finest materials.",keywords:["menswear","luxury clothing","tailored","shirts","accessories"],icons:{icon:[{url:"/logo.PNG",sizes:"32x32",type:"image/png"},{url:"/logo.PNG",sizes:"16x16",type:"image/png"}],shortcut:"/logo.PNG",apple:"/logo.PNG"}};function m({children:e}){return r.jsx("html",{lang:"en",children:r.jsx("body",{className:`${o().variable} ${i().variable} font-sans antialiased min-h-screen bg-[#f8f8f5]`,children:r.jsx(d,{children:r.jsx(c,{children:r.jsx("main",{style:{paddingTop:0},className:"transition-all duration-300",children:e})})})})})}},12523:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\not-found.tsx#default`)},38958:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\woocommerce-test\success\page.tsx#default`)},5023:()=>{},18201:(e,t,s)=>{"use strict";class r extends Error{}r.prototype.name="InvalidTokenError"}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[8948,2344,9404],()=>s(85051));module.exports=r})();