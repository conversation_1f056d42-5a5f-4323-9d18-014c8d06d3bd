(()=>{var e={};e.id=1253,e.ids=[1253],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},32222:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c}),t(52618),t(5052),t(51806),t(12523);var n=t(23191),a=t(88716),s=t(37922),o=t.n(s),i=t(95231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let c=["",{children:["sign-in",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,52618)),"E:\\ankkorwoo\\ankkor\\src\\app\\sign-in\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,5052)),"E:\\ankkorwoo\\ankkor\\src\\app\\sign-in\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,51806)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],d=["E:\\ankkorwoo\\ankkor\\src\\app\\sign-in\\page.tsx"],u="/sign-in/page",p={require:t,loadChunk:()=>Promise.resolve()},x=new n.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/sign-in/page",pathname:"/sign-in",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},12271:(e,r,t)=>{Promise.resolve().then(t.bind(t,33840))},35303:()=>{},33840:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var n=t(10326),a=t(35047),s=t(32457);function o(){let e=(0,a.useSearchParams)().get("redirect")||"/";return n.jsx("div",{className:"container mx-auto py-12 px-4",children:(0,n.jsxs)("div",{className:"max-w-4xl mx-auto",children:[n.jsx("h1",{className:"text-3xl font-serif mb-8 text-center",children:"Sign In"}),n.jsx(s.default,{mode:"login",redirectUrl:e}),n.jsx("div",{className:"mt-8 text-center",children:(0,n.jsxs)("p",{className:"text-gray-600",children:["Don't have an account?"," ",n.jsx("a",{href:"/sign-up",className:"text-[#2c2c27] underline hover:text-[#8a8778]",children:"Create one here"})]})})]})})}},5052:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s,metadata:()=>a});var n=t(19510);let a={title:"Sign In | Ankkor",description:"Sign in to your Ankkor account to access your orders, wishlist, and more."};function s({children:e}){return n.jsx(n.Fragment,{children:e})}},52618:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});let n=(0,t(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\sign-in\page.tsx#default`)}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[8948,2344,5010,4154],()=>t(32222));module.exports=n})();