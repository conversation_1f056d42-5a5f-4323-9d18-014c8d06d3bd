exports.id=4931,exports.ids=[4931],exports.modules={13417:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,12994,23)),Promise.resolve().then(s.t.bind(s,96114,23)),Promise.resolve().then(s.t.bind(s,9727,23)),Promise.resolve().then(s.t.bind(s,79671,23)),Promise.resolve().then(s.t.bind(s,41868,23)),Promise.resolve().then(s.t.bind(s,84759,23))},96799:(e,r,s)=>{Promise.resolve().then(s.bind(s,68897)),Promise.resolve().then(s.bind(s,75367))},54039:(e,r,s)=>{Promise.resolve().then(s.bind(s,83846))},83846:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>n});var t=s(10326);s(17577);var o=s(33265);let a=()=>t.jsx("div",{className:"animate-pulse flex space-x-4",children:(0,t.jsxs)("div",{className:"flex-1 space-y-6 py-1",children:[t.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[t.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-2"}),t.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-1"})]}),t.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"})]})]})}),i=(0,o.default)(async()=>{},{loadableGenerated:{modules:["app\\not-found.tsx -> ./not-found-content"]},ssr:!1,loading:()=>t.jsx(a,{})});function n(){return t.jsx("div",{className:"container mx-auto py-20",children:t.jsx(i,{})})}},68897:(e,r,s)=>{"use strict";s.d(r,{CustomerProvider:()=>d,O:()=>l});var t=s(10326),o=s(17577),a=s(35047),i=s(75367);let n=(0,o.createContext)({customer:null,isLoading:!0,isAuthenticated:!1,token:null,login:async()=>{},register:async()=>{},logout:()=>{},updateProfile:async()=>{},error:null,refreshCustomer:async()=>{}}),l=()=>(0,o.useContext)(n);function d({children:e}){let[r,s]=(0,o.useState)(null),[l,d]=(0,o.useState)(!0),[c,u]=(0,o.useState)(null),[f,m]=(0,o.useState)(null),h=(0,a.useRouter)(),{addToast:x}=(0,i.p)(),g=e=>e?{...e,displayName:e.displayName||e.username||`${e.firstName||""} ${e.lastName||""}`.trim()||"User"}:null,v=async()=>{try{console.log("CustomerProvider: Checking authentication via /api/auth/me");let e=await fetch("/api/auth/me",{method:"GET",credentials:"include"});console.log("CustomerProvider: Auth API response status:",e.status);let r=await e.json();if(console.log("CustomerProvider: Auth API result:",r),!r.success||!r.customer)return m(null),{success:!1,message:r.message||"Not authenticated"};{let e=r.token;return console.log("CustomerProvider: Token from API response:",!!e),m(e||null),{success:!0,customer:r.customer,token:e}}}catch(e){return console.error("CustomerProvider: Error checking authentication:",e),m(null),{success:!1,message:"Network error"}}},p=async()=>{try{let e=await v();if(e.success){let r={...e.customer,token:e.token};s(g(r)),console.log("Customer data refreshed successfully"),console.log("Token available after refresh:",!!e.token)}else console.log("Failed to refresh customer data:",e.message),s(null),m(null)}catch(e){console.error("Error refreshing customer data:",e),s(null),m(null)}},b=async e=>{d(!0),u(null);try{throw Error("Login temporarily disabled for build fix")}catch(r){let e="Login temporarily disabled for build fix";throw u(e),x(e,"error"),r}finally{d(!1)}},y=async e=>{d(!0),u(null);try{throw Error("Register temporarily disabled for build fix")}catch(r){let e="Register temporarily disabled for build fix";throw u(e),x(e,"error"),r}finally{d(!1)}},w=async e=>{d(!0),u(null);try{throw Error("Profile update temporarily disabled for build fix")}catch(r){let e="Profile update temporarily disabled for build fix";throw u(e),x(e,"error"),r}finally{d(!1)}};return t.jsx(n.Provider,{value:{customer:r,isLoading:l,isAuthenticated:!!r,token:f,login:b,register:y,logout:()=>{s(null),m(null),console.log("Logout completed, token cleared"),x("You have been signed out successfully","info"),h.push("/"),h.refresh()},updateProfile:w,error:c,refreshCustomer:p},children:e})}},91664:(e,r,s)=>{"use strict";s.d(r,{z:()=>l});var t=s(10326);s(17577);var o=s(34214),a=s(79360),i=s(51223);let n=(0,a.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-[#2c2c27] text-[#f4f3f0] shadow-xs hover:bg-[#3d3d35]",destructive:"bg-[#ff4d4f] text-white shadow-xs hover:bg-[#ff4d4f]/90 focus-visible:ring-[#ff4d4f]/20 dark:focus-visible:ring-[#ff4d4f]/40",outline:"border border-[#e5e2d9] bg-[#f8f8f5] shadow-xs hover:bg-[#f4f3f0] hover:text-[#2c2c27]",secondary:"bg-[#e5e2d9] text-[#2c2c27] shadow-xs hover:bg-[#e5e2d9]/80",ghost:"hover:bg-[#f4f3f0] hover:text-[#2c2c27]",link:"text-[#2c2c27] underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:r,size:s,asChild:a=!1,...l}){let d=a?o.g7:"button";return t.jsx(d,{"data-slot":"button",className:(0,i.cn)(n({variant:r,size:s,className:e})),...l})}},41190:(e,r,s)=>{"use strict";s.d(r,{I:()=>i});var t=s(10326),o=s(17577),a=s(51223);let i=o.forwardRef(({className:e,type:r,...s},o)=>t.jsx("input",{type:r,"data-slot":"input",className:(0,a.cn)("border-[#e5e2d9] file:text-[#2c2c27] placeholder:text-[#8a8778] selection:bg-[#2c2c27] selection:text-[#f4f3f0] flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-[#8a8778] focus-visible:ring-[#8a8778]/50 focus-visible:ring-[3px]","aria-invalid:ring-[#ff4d4f]/20 dark:aria-invalid:ring-[#ff4d4f]/40 aria-invalid:border-[#ff4d4f]",e),ref:o,...s}));i.displayName="Input"},75367:(e,r,s)=>{"use strict";s.d(r,{ToastProvider:()=>f,p:()=>m});var t=s(10326),o=s(17577),a=s(92148),i=s(86462),n=s(54659),l=s(87888),d=s(18019),c=s(94019);let u=(0,o.createContext)(void 0);function f({children:e}){let[r,s]=(0,o.useState)([]);return(0,t.jsxs)(u.Provider,{value:{toasts:r,addToast:(e,r="info",t=3e3)=>{let o=Math.random().toString(36).substring(2,9);s(s=>[...s,{id:o,message:e,type:r,duration:t}])},removeToast:e=>{s(r=>r.filter(r=>r.id!==e))}},children:[e,t.jsx(x,{})]})}function m(){let e=(0,o.useContext)(u);if(void 0===e)throw Error("useToast must be used within a ToastProvider");return e}function h({toast:e,onRemove:r}){return(0,t.jsxs)(a.E.div,{initial:{opacity:0,y:-50},animate:{opacity:1,y:0},exit:{opacity:0,x:300},className:`flex items-center p-4 rounded-lg border shadow-lg ${(()=>{switch(e.type){case"success":return"bg-[#f4f3f0] border-[#8a8778]";case"error":return"bg-red-50 border-red-200";default:return"bg-[#f8f8f5] border-[#e5e2d9]"}})()} max-w-md`,children:[t.jsx(()=>{switch(e.type){case"success":return t.jsx(n.Z,{className:"h-5 w-5"});case"error":return t.jsx(l.Z,{className:"h-5 w-5"});default:return t.jsx(d.Z,{className:"h-5 w-5"})}},{}),t.jsx("span",{className:"ml-3 text-sm font-medium flex-1",children:e.message}),t.jsx("button",{onClick:r,className:"ml-4 text-gray-400 hover:text-gray-600",children:t.jsx(c.Z,{className:"h-4 w-4"})})]})}function x(){let{toasts:e,removeToast:r}=m();return t.jsx("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:t.jsx(i.M,{children:e.map(e=>t.jsx(h,{toast:e,onRemove:()=>r(e.id)},e.id))})})}},51223:(e,r,s)=>{"use strict";s.d(r,{cn:()=>a});var t=s(41135),o=s(31009);function a(...e){return(0,o.m6)((0,t.W)(e))}},51806:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>f,metadata:()=>u});var t=s(19510),o=s(10527),a=s.n(o),i=s(36822),n=s.n(i);s(5023);var l=s(68570);(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#useCustomer`);let d=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#CustomerProvider`),c=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#ToastProvider`);(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#useToast`);let u={title:"Ankkor | Timeless Menswear",description:"Elevated essentials for the discerning gentleman. Impeccably tailored garments crafted from the finest materials.",keywords:["menswear","luxury clothing","tailored","shirts","accessories"],icons:{icon:[{url:"/logo.PNG",sizes:"32x32",type:"image/png"},{url:"/logo.PNG",sizes:"16x16",type:"image/png"}],shortcut:"/logo.PNG",apple:"/logo.PNG"}};function f({children:e}){return t.jsx("html",{lang:"en",children:t.jsx("body",{className:`${a().variable} ${n().variable} font-sans antialiased min-h-screen bg-[#f8f8f5]`,children:t.jsx(c,{children:t.jsx(d,{children:t.jsx("main",{style:{paddingTop:0},className:"transition-all duration-300",children:e})})})})})}},12523:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\not-found.tsx#default`)},5023:()=>{},45226:(e,r,s)=>{"use strict";s.d(r,{WV:()=>i});var t=s(17577);s(60962);var o=s(34214),a=s(10326),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,r)=>{let s=t.forwardRef((e,s)=>{let{asChild:t,...i}=e,n=t?o.g7:r;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(n,{...i,ref:s})});return s.displayName=`Primitive.${r}`,{...e,[r]:s}},{})}};