(()=>{var e={};e.id=9346,e.ids=[9346],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},93690:e=>{"use strict";e.exports=import("graphql-request")},42291:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>h,originalPathname:()=>m,pages:()=>p,routeModule:()=>x,tree:()=>f});var s=r(36147);r(51806),r(12523);var a=r(23191),i=r(88716),o=r(37922),l=r.n(o),c=r(95231),d={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>c[e]);r.d(t,d);var u=e([s]);s=(u.then?(await u)():u)[0];let f=["",{children:["account",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,36147)),"E:\\ankkorwoo\\ankkor\\src\\app\\account\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,51806)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],p=["E:\\ankkorwoo\\ankkor\\src\\app\\account\\page.tsx"],m="/account/page",h={require:r,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/account/page",pathname:"/account",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:f}});n()}catch(e){n(e)}})},12448:(e,t,r)=>{Promise.resolve().then(r.bind(r,35063))},48705:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(76557).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},35063:(e,t,r)=>{"use strict";r.d(t,{default:()=>eg});var n=r(10326),s=r(17577),a=r.t(s,2),i=r(35047),o=r(92148);function l(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}function c(e,t=[]){let r=[],a=()=>{let t=r.map(e=>s.createContext(e));return function(r){let n=r?.[e]||t;return s.useMemo(()=>({[`__scope${e}`]:{...r,[e]:n}}),[r,n])}};return a.scopeName=e,[function(t,a){let i=s.createContext(a),o=r.length;r=[...r,a];let l=t=>{let{scope:r,children:a,...l}=t,c=r?.[e]?.[o]||i,d=s.useMemo(()=>l,Object.values(l));return(0,n.jsx)(c.Provider,{value:d,children:a})};return l.displayName=t+"Provider",[l,function(r,n){let l=n?.[e]?.[o]||i,c=s.useContext(l);if(c)return c;if(void 0!==a)return a;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=r.reduce((t,{useScope:r,scopeName:n})=>{let s=r(e)[`__scope${n}`];return{...t,...s}},{});return s.useMemo(()=>({[`__scope${t.scopeName}`]:n}),[n])}};return r.scopeName=t.scopeName,r}(a,...t)]}var d=r(48051),u=r(34214),f=globalThis?.document?s.useLayoutEffect:()=>{},p=a["useId".toString()]||(()=>void 0),m=0;function h(e){let[t,r]=s.useState(p());return f(()=>{e||r(e=>e??String(m++))},[e]),e||(t?`radix-${t}`:"")}var x=r(45226);function g(e){let t=s.useRef(e);return s.useEffect(()=>{t.current=e}),s.useMemo(()=>(...e)=>t.current?.(...e),[])}function y({prop:e,defaultProp:t,onChange:r=()=>{}}){let[n,a]=function({defaultProp:e,onChange:t}){let r=s.useState(e),[n]=r,a=s.useRef(n),i=g(t);return s.useEffect(()=>{a.current!==n&&(i(n),a.current=n)},[n,a,i]),r}({defaultProp:t,onChange:r}),i=void 0!==e,o=i?e:n,l=g(r);return[o,s.useCallback(t=>{if(i){let r="function"==typeof t?t(e):t;r!==e&&l(r)}else a(t)},[i,e,a,l])]}var b=s.createContext(void 0);function v(e){let t=s.useContext(b);return e||t||"ltr"}var j="rovingFocusGroup.onEntryFocus",N={bubbles:!1,cancelable:!0},w="RovingFocusGroup",[R,k,A]=function(e){let t=e+"CollectionProvider",[r,a]=c(t),[i,o]=r(t,{collectionRef:{current:null},itemMap:new Map}),l=e=>{let{scope:t,children:r}=e,a=s.useRef(null),o=s.useRef(new Map).current;return(0,n.jsx)(i,{scope:t,itemMap:o,collectionRef:a,children:r})};l.displayName=t;let f=e+"CollectionSlot",p=s.forwardRef((e,t)=>{let{scope:r,children:s}=e,a=o(f,r),i=(0,d.e)(t,a.collectionRef);return(0,n.jsx)(u.g7,{ref:i,children:s})});p.displayName=f;let m=e+"CollectionItemSlot",h="data-radix-collection-item",x=s.forwardRef((e,t)=>{let{scope:r,children:a,...i}=e,l=s.useRef(null),c=(0,d.e)(t,l),f=o(m,r);return s.useEffect(()=>(f.itemMap.set(l,{ref:l,...i}),()=>void f.itemMap.delete(l))),(0,n.jsx)(u.g7,{[h]:"",ref:c,children:a})});return x.displayName=m,[{Provider:l,Slot:p,ItemSlot:x},function(t){let r=o(e+"CollectionConsumer",t);return s.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${h}]`));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},a]}(w),[C,_]=c(w,[A]),[S,P]=C(w),O=s.forwardRef((e,t)=>(0,n.jsx)(R.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,n.jsx)(R.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,n.jsx)(M,{...e,ref:t})})}));O.displayName=w;var M=s.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:a,loop:i=!1,dir:o,currentTabStopId:c,defaultCurrentTabStopId:u,onCurrentTabStopIdChange:f,onEntryFocus:p,preventScrollOnEntryFocus:m=!1,...h}=e,b=s.useRef(null),w=(0,d.e)(t,b),R=v(o),[A=null,C]=y({prop:c,defaultProp:u,onChange:f}),[_,P]=s.useState(!1),O=g(p),M=k(r),E=s.useRef(!1),[I,T]=s.useState(0);return s.useEffect(()=>{let e=b.current;if(e)return e.addEventListener(j,O),()=>e.removeEventListener(j,O)},[O]),(0,n.jsx)(S,{scope:r,orientation:a,dir:R,loop:i,currentTabStopId:A,onItemFocus:s.useCallback(e=>C(e),[C]),onItemShiftTab:s.useCallback(()=>P(!0),[]),onFocusableItemAdd:s.useCallback(()=>T(e=>e+1),[]),onFocusableItemRemove:s.useCallback(()=>T(e=>e-1),[]),children:(0,n.jsx)(x.WV.div,{tabIndex:_||0===I?-1:0,"data-orientation":a,...h,ref:w,style:{outline:"none",...e.style},onMouseDown:l(e.onMouseDown,()=>{E.current=!0}),onFocus:l(e.onFocus,e=>{let t=!E.current;if(e.target===e.currentTarget&&t&&!_){let t=new CustomEvent(j,N);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=M().filter(e=>e.focusable);F([e.find(e=>e.active),e.find(e=>e.id===A),...e].filter(Boolean).map(e=>e.ref.current),m)}}E.current=!1}),onBlur:l(e.onBlur,()=>P(!1))})})}),E="RovingFocusGroupItem",I=s.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:a=!0,active:i=!1,tabStopId:o,...c}=e,d=h(),u=o||d,f=P(E,r),p=f.currentTabStopId===u,m=k(r),{onFocusableItemAdd:g,onFocusableItemRemove:y}=f;return s.useEffect(()=>{if(a)return g(),()=>y()},[a,g,y]),(0,n.jsx)(R.ItemSlot,{scope:r,id:u,focusable:a,active:i,children:(0,n.jsx)(x.WV.span,{tabIndex:p?0:-1,"data-orientation":f.orientation,...c,ref:t,onMouseDown:l(e.onMouseDown,e=>{a?f.onItemFocus(u):e.preventDefault()}),onFocus:l(e.onFocus,()=>f.onItemFocus(u)),onKeyDown:l(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){f.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let s=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(s))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(s)))return T[s]}(e,f.orientation,f.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=m().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=f.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>F(r))}})})})});I.displayName=E;var T={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function F(e,t=!1){let r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var D=e=>{let{present:t,children:r}=e,n=function(e){var t,r;let[n,a]=s.useState(),i=s.useRef({}),o=s.useRef(e),l=s.useRef("none"),[c,d]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},s.useReducer((e,t)=>r[e][t]??e,t));return s.useEffect(()=>{let e=L(i.current);l.current="mounted"===c?e:"none"},[c]),f(()=>{let t=i.current,r=o.current;if(r!==e){let n=l.current,s=L(t);e?d("MOUNT"):"none"===s||t?.display==="none"?d("UNMOUNT"):r&&n!==s?d("ANIMATION_OUT"):d("UNMOUNT"),o.current=e}},[e,d]),f(()=>{if(n){let e;let t=n.ownerDocument.defaultView??window,r=r=>{let s=L(i.current).includes(r.animationName);if(r.target===n&&s&&(d("ANIMATION_END"),!o.current)){let r=n.style.animationFillMode;n.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=r)})}},s=e=>{e.target===n&&(l.current=L(i.current))};return n.addEventListener("animationstart",s),n.addEventListener("animationcancel",r),n.addEventListener("animationend",r),()=>{t.clearTimeout(e),n.removeEventListener("animationstart",s),n.removeEventListener("animationcancel",r),n.removeEventListener("animationend",r)}}d("ANIMATION_END")},[n,d]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:s.useCallback(e=>{e&&(i.current=getComputedStyle(e)),a(e)},[])}}(t),a="function"==typeof r?r({present:n.isPresent}):s.Children.only(r),i=(0,d.e)(n.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(a));return"function"==typeof r||n.isPresent?s.cloneElement(a,{ref:i}):null};function L(e){return e?.animationName||"none"}D.displayName="Presence";var $="Tabs",[q,U]=c($,[_]),H=_(),[V,G]=q($),W=s.forwardRef((e,t)=>{let{__scopeTabs:r,value:s,onValueChange:a,defaultValue:i,orientation:o="horizontal",dir:l,activationMode:c="automatic",...d}=e,u=v(l),[f,p]=y({prop:s,onChange:a,defaultProp:i});return(0,n.jsx)(V,{scope:r,baseId:h(),value:f,onValueChange:p,orientation:o,dir:u,activationMode:c,children:(0,n.jsx)(x.WV.div,{dir:u,"data-orientation":o,...d,ref:t})})});W.displayName=$;var z="TabsList",K=s.forwardRef((e,t)=>{let{__scopeTabs:r,loop:s=!0,...a}=e,i=G(z,r),o=H(r);return(0,n.jsx)(O,{asChild:!0,...o,orientation:i.orientation,dir:i.dir,loop:s,children:(0,n.jsx)(x.WV.div,{role:"tablist","aria-orientation":i.orientation,...a,ref:t})})});K.displayName=z;var Z="TabsTrigger",B=s.forwardRef((e,t)=>{let{__scopeTabs:r,value:s,disabled:a=!1,...i}=e,o=G(Z,r),c=H(r),d=X(o.baseId,s),u=Y(o.baseId,s),f=s===o.value;return(0,n.jsx)(I,{asChild:!0,...c,focusable:!a,active:f,children:(0,n.jsx)(x.WV.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":u,"data-state":f?"active":"inactive","data-disabled":a?"":void 0,disabled:a,id:d,...i,ref:t,onMouseDown:l(e.onMouseDown,e=>{a||0!==e.button||!1!==e.ctrlKey?e.preventDefault():o.onValueChange(s)}),onKeyDown:l(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&o.onValueChange(s)}),onFocus:l(e.onFocus,()=>{let e="manual"!==o.activationMode;f||a||!e||o.onValueChange(s)})})})});B.displayName=Z;var J="TabsContent",Q=s.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,forceMount:i,children:o,...l}=e,c=G(J,r),d=X(c.baseId,a),u=Y(c.baseId,a),f=a===c.value,p=s.useRef(f);return s.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,n.jsx)(D,{present:i||f,children:({present:r})=>(0,n.jsx)(x.WV.div,{"data-state":f?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":d,hidden:!r,id:u,tabIndex:0,...l,ref:t,style:{...e.style,animationDuration:p.current?"0s":void 0},children:r&&o})})});function X(e,t){return`${e}-trigger-${t}`}function Y(e,t){return`${e}-content-${t}`}Q.displayName=J;var ee=r(51223);let et=s.forwardRef(({className:e,...t},r)=>n.jsx(K,{ref:r,className:(0,ee.cn)("inline-flex h-10 items-center justify-center rounded-md p-1 text-[#5c5c52]",e),...t}));et.displayName=K.displayName;let er=s.forwardRef(({className:e,...t},r)=>n.jsx(B,{ref:r,className:(0,ee.cn)("inline-flex items-center justify-center whitespace-nowrap px-3 py-1.5 text-sm font-medium ring-offset-white transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-[#8a8778] focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:border-b-2 data-[state=active]:border-[#2c2c27] data-[state=active]:text-[#2c2c27] data-[state=active]:font-semibold",e),...t}));er.displayName=B.displayName;let en=s.forwardRef(({className:e,...t},r)=>n.jsx(Q,{ref:r,className:(0,ee.cn)("mt-2 ring-offset-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-[#8a8778] focus-visible:ring-offset-2",e),...t}));en.displayName=Q.displayName;let es=s.forwardRef(({className:e,...t},r)=>n.jsx("div",{ref:r,className:(0,ee.cn)("rounded-lg border border-[#e5e2d9] bg-[#f8f8f5] text-[#2c2c27] shadow-sm",e),...t}));es.displayName="Card";let ea=s.forwardRef(({className:e,...t},r)=>n.jsx("div",{ref:r,className:(0,ee.cn)("flex flex-col space-y-1.5 p-6",e),...t}));ea.displayName="CardHeader";let ei=s.forwardRef(({className:e,...t},r)=>n.jsx("h3",{ref:r,className:(0,ee.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));ei.displayName="CardTitle";let eo=s.forwardRef(({className:e,...t},r)=>n.jsx("p",{ref:r,className:(0,ee.cn)("text-sm text-[#5c5c52]",e),...t}));eo.displayName="CardDescription";let el=s.forwardRef(({className:e,...t},r)=>n.jsx("div",{ref:r,className:(0,ee.cn)("p-6 pt-0",e),...t}));el.displayName="CardContent";let ec=s.forwardRef(({className:e,...t},r)=>n.jsx("div",{ref:r,className:(0,ee.cn)("flex items-center p-6 pt-0",e),...t}));ec.displayName="CardFooter";var ed=r(91664),eu=r(41190),ef=r(76557);let ep=(0,ef.Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);var em=r(48705);let eh=(0,ef.Z)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]]);var ex=r(68897);let eg=()=>{let e=(0,i.useRouter)(),{customer:t,updateProfile:r,refreshCustomer:a}=(0,ex.O)();if(!t)return n.jsx("div",{className:"flex justify-center items-center py-12",children:n.jsx("div",{className:"text-[#8a8778]",children:"Loading account information..."})});let[l,c]=(0,s.useState)("profile"),[d,u]=(0,s.useState)(!1),[f,p]=(0,s.useState)(!1),[m,h]=(0,s.useState)(null),[x,g]=(0,s.useState)(null),[y,b]=(0,s.useState)({firstName:t.firstName||"",lastName:t.lastName||"",email:t.email||"",phone:t.billing?.phone||""});(0,s.useEffect)(()=>{b({firstName:t.firstName||"",lastName:t.lastName||"",email:t.email||"",phone:t.billing?.phone||""})},[t]);let v=e=>{let{name:t,value:r}=e.target;b(e=>({...e,[t]:r}))},j=async e=>{e.preventDefault(),h(null),g(null),p(!0);try{let e={id:t.id,firstName:y.firstName,lastName:y.lastName,billing:{...t.billing,firstName:y.firstName,lastName:y.lastName,phone:y.phone}};await r(e),u(!1),g("Profile updated successfully"),setTimeout(()=>{g(null)},3e3)}catch(e){console.error("Error updating profile:",e),h(e.message||"An error occurred while updating your profile")}finally{p(!1)}};return(0,n.jsxs)(o.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:[(0,n.jsxs)("p",{className:"text-[#8a8778] mb-8",children:["Welcome back, ",t.firstName," ",t.lastName]}),(0,n.jsxs)(W,{defaultValue:l,onValueChange:c,className:"w-full",children:[(0,n.jsxs)(et,{className:`grid ${t.downloadableItems&&t.downloadableItems.nodes.length>0?"grid-cols-3":"grid-cols-2"} mb-8`,children:[(0,n.jsxs)(er,{value:"profile",className:"flex items-center gap-2",children:[n.jsx(ep,{className:"h-4 w-4"}),n.jsx("span",{className:"hidden sm:inline",children:"Profile"})]}),(0,n.jsxs)(er,{value:"orders",className:"flex items-center gap-2",children:[n.jsx(em.Z,{className:"h-4 w-4"}),n.jsx("span",{className:"hidden sm:inline",children:"Orders"})]}),t.downloadableItems&&t.downloadableItems.nodes.length>0&&(0,n.jsxs)(er,{value:"downloads",className:"flex items-center gap-2",children:[n.jsx(em.Z,{className:"h-4 w-4"}),n.jsx("span",{className:"hidden sm:inline",children:"Downloads"})]})]}),n.jsx(en,{value:"profile",children:(0,n.jsxs)(es,{children:[(0,n.jsxs)(ea,{children:[n.jsx(ei,{children:"Profile Information"}),n.jsx(eo,{children:"Manage your personal information"})]}),(0,n.jsxs)(el,{className:"space-y-4",children:[m&&n.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4",children:m}),x&&n.jsx("div",{className:"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-4",children:x}),d?(0,n.jsxs)("form",{onSubmit:j,className:"space-y-4",children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[n.jsx("label",{htmlFor:"firstName",className:"block text-sm font-medium text-[#5c5c52] mb-1",children:"First Name"}),n.jsx(eu.I,{id:"firstName",name:"firstName",type:"text",value:y.firstName,onChange:v,className:"w-full border-[#e5e2d9] focus:border-[#8a8778] focus:ring-[#8a8778]",required:!0})]}),(0,n.jsxs)("div",{children:[n.jsx("label",{htmlFor:"lastName",className:"block text-sm font-medium text-[#5c5c52] mb-1",children:"Last Name"}),n.jsx(eu.I,{id:"lastName",name:"lastName",type:"text",value:y.lastName,onChange:v,className:"w-full border-[#e5e2d9] focus:border-[#8a8778] focus:ring-[#8a8778]",required:!0})]})]}),(0,n.jsxs)("div",{children:[n.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-[#5c5c52] mb-1",children:"Email"}),n.jsx(eu.I,{id:"email",name:"email",type:"email",value:y.email,onChange:v,className:"w-full border-[#e5e2d9] focus:border-[#8a8778] focus:ring-[#8a8778] bg-[#f4f3f0]",disabled:!0}),n.jsx("p",{className:"mt-1 text-xs text-[#8a8778]",children:"Email cannot be changed. Please contact support if you need to change your email."})]}),(0,n.jsxs)("div",{children:[n.jsx("label",{htmlFor:"phone",className:"block text-sm font-medium text-[#5c5c52] mb-1",children:"Phone"}),n.jsx(eu.I,{id:"phone",name:"phone",type:"tel",value:y.phone,onChange:v,className:"w-full border-[#e5e2d9] focus:border-[#8a8778] focus:ring-[#8a8778]",placeholder:"(*************"})]}),(0,n.jsxs)("div",{className:"flex gap-2 pt-2",children:[n.jsx(ed.z,{type:"submit",disabled:f,className:"bg-[#2c2c27]",children:f?"Saving...":"Save Changes"}),n.jsx(ed.z,{type:"button",variant:"outline",onClick:()=>{u(!1),b({firstName:t.firstName||"",lastName:t.lastName||"",email:t.email||"",phone:t.billing?.phone||""})},children:"Cancel"})]})]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[n.jsx("h3",{className:"text-sm font-medium text-[#5c5c52]",children:"First Name"}),n.jsx("p",{className:"text-[#2c2c27]",children:t.firstName||"Not provided"})]}),(0,n.jsxs)("div",{children:[n.jsx("h3",{className:"text-sm font-medium text-[#5c5c52]",children:"Last Name"}),n.jsx("p",{className:"text-[#2c2c27]",children:t.lastName||"Not provided"})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[n.jsx("h3",{className:"text-sm font-medium text-[#5c5c52]",children:"Email"}),n.jsx("p",{className:"text-[#2c2c27]",children:t.email||"Not provided"})]}),(0,n.jsxs)("div",{children:[n.jsx("h3",{className:"text-sm font-medium text-[#5c5c52]",children:"Phone"}),n.jsx("p",{className:"text-[#2c2c27]",children:t.billing?.phone||"Not provided"})]})]})]})]}),n.jsx(ec,{children:!d&&(0,n.jsxs)(ed.z,{variant:"outline",onClick:()=>u(!0),className:"flex items-center gap-2",children:[n.jsx(eh,{className:"h-4 w-4"}),"Edit Profile"]})})]})}),n.jsx(en,{value:"orders",children:(0,n.jsxs)(es,{children:[(0,n.jsxs)(ea,{children:[n.jsx(ei,{children:"Order History"}),n.jsx(eo,{children:"View and track your orders"})]}),n.jsx(el,{children:t.orders&&t.orders.nodes.length>0?n.jsx("div",{className:"space-y-6",children:t.orders.nodes.map(e=>(0,n.jsxs)("div",{className:"border border-[#e5e2d9] p-6 rounded-md",children:[(0,n.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("h3",{className:"font-medium text-[#2c2c27]",children:["Order #",e.databaseId]}),(0,n.jsxs)("p",{className:"text-sm text-[#8a8778]",children:["Placed on ",new Date(e.date).toLocaleDateString()]}),e.paymentMethodTitle&&(0,n.jsxs)("p",{className:"text-xs text-[#8a8778]",children:["Payment: ",e.paymentMethodTitle]})]}),(0,n.jsxs)("div",{className:"text-right",children:[(0,n.jsxs)("p",{className:"font-medium text-[#2c2c27]",children:["$",parseFloat(e.total||"0").toFixed(2)]}),n.jsx("span",{className:`text-xs px-2 py-1 rounded ${"completed"===e.status?"bg-green-100 text-green-800":"processing"===e.status?"bg-blue-100 text-blue-800":"cancelled"===e.status?"bg-red-100 text-red-800":"bg-yellow-100 text-yellow-800"}`,children:e.status.toUpperCase()})]})]}),(0,n.jsxs)("div",{className:"space-y-3 mb-4",children:[n.jsx("h4",{className:"text-sm font-medium text-[#5c5c52]",children:"Items"}),e.lineItems.nodes.map((e,t)=>(0,n.jsxs)("div",{className:"flex items-center gap-4 p-3 bg-gray-50 rounded",children:[e.product.node.image&&n.jsx("div",{className:"w-12 h-12 bg-gray-200 rounded overflow-hidden flex-shrink-0",children:n.jsx("img",{src:e.product.node.image.sourceUrl,alt:e.product.node.image.altText||e.product.node.name,className:"w-full h-full object-cover"})}),(0,n.jsxs)("div",{className:"flex-1",children:[n.jsx("p",{className:"text-[#2c2c27] font-medium",children:e.product.node.name}),e.variation&&e.variation.node.attributes&&n.jsx("div",{className:"text-xs text-[#8a8778]",children:e.variation.node.attributes.nodes.map((t,r)=>(0,n.jsxs)("span",{children:[t.name,": ",t.value,r<e.variation.node.attributes.nodes.length-1&&", "]},r))}),(0,n.jsxs)("p",{className:"text-xs text-[#8a8778]",children:["Qty: ",e.quantity," \xd7 $",(parseFloat(e.total||"0")/e.quantity).toFixed(2)]})]}),n.jsx("div",{className:"text-right",children:(0,n.jsxs)("p",{className:"font-medium text-[#2c2c27]",children:["$",parseFloat(e.total||"0").toFixed(2)]})})]},t))]}),(0,n.jsxs)("div",{className:"border-t border-[#e5e2d9] pt-4",children:[(0,n.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[(0,n.jsxs)("div",{children:[n.jsx("span",{className:"text-[#8a8778]",children:"Subtotal:"}),(0,n.jsxs)("p",{className:"font-medium",children:["$",parseFloat(e.subtotal||"0").toFixed(2)]})]}),e.shippingTotal&&parseFloat(e.shippingTotal)>0&&(0,n.jsxs)("div",{children:[n.jsx("span",{className:"text-[#8a8778]",children:"Shipping:"}),(0,n.jsxs)("p",{className:"font-medium",children:["$",parseFloat(e.shippingTotal).toFixed(2)]})]}),e.totalTax&&parseFloat(e.totalTax)>0&&(0,n.jsxs)("div",{children:[n.jsx("span",{className:"text-[#8a8778]",children:"Tax:"}),(0,n.jsxs)("p",{className:"font-medium",children:["$",parseFloat(e.totalTax).toFixed(2)]})]}),e.discountTotal&&parseFloat(e.discountTotal)>0&&(0,n.jsxs)("div",{children:[n.jsx("span",{className:"text-[#8a8778]",children:"Discount:"}),(0,n.jsxs)("p",{className:"font-medium text-green-600",children:["-$",parseFloat(e.discountTotal).toFixed(2)]})]})]}),(e.shipping||e.billing)&&(0,n.jsxs)("div",{className:"mt-4 grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.billing&&(0,n.jsxs)("div",{children:[n.jsx("h5",{className:"text-sm font-medium text-[#5c5c52] mb-2",children:"Billing Address"}),(0,n.jsxs)("div",{className:"text-xs text-[#8a8778]",children:[(0,n.jsxs)("p",{children:[e.billing.firstName," ",e.billing.lastName]}),e.billing.company&&n.jsx("p",{children:e.billing.company}),n.jsx("p",{children:e.billing.address1}),e.billing.address2&&n.jsx("p",{children:e.billing.address2}),(0,n.jsxs)("p",{children:[e.billing.city,", ",e.billing.state," ",e.billing.postcode]}),n.jsx("p",{children:e.billing.country}),e.billing.phone&&(0,n.jsxs)("p",{children:["Phone: ",e.billing.phone]})]})]}),e.shipping&&e.shipping.address1&&(0,n.jsxs)("div",{children:[n.jsx("h5",{className:"text-sm font-medium text-[#5c5c52] mb-2",children:"Shipping Address"}),(0,n.jsxs)("div",{className:"text-xs text-[#8a8778]",children:[(0,n.jsxs)("p",{children:[e.shipping.firstName," ",e.shipping.lastName]}),e.shipping.company&&n.jsx("p",{children:e.shipping.company}),n.jsx("p",{children:e.shipping.address1}),e.shipping.address2&&n.jsx("p",{children:e.shipping.address2}),(0,n.jsxs)("p",{children:[e.shipping.city,", ",e.shipping.state," ",e.shipping.postcode]}),n.jsx("p",{children:e.shipping.country})]})]})]}),e.customerNote&&(0,n.jsxs)("div",{className:"mt-4",children:[n.jsx("h5",{className:"text-sm font-medium text-[#5c5c52] mb-2",children:"Order Notes"}),n.jsx("p",{className:"text-xs text-[#8a8778] bg-gray-50 p-2 rounded",children:e.customerNote})]}),n.jsx("div",{className:"mt-4 flex justify-end",children:n.jsx(ed.z,{variant:"outline",size:"sm",children:"View Full Details"})})]})]},e.id))}):(0,n.jsxs)("div",{className:"text-center py-8",children:[n.jsx("p",{className:"text-[#8a8778] mb-4",children:"You haven't placed any orders yet."}),n.jsx(ed.z,{onClick:()=>e.push("/collection"),children:"Start Shopping"})]})})]})}),t.downloadableItems&&t.downloadableItems.nodes.length>0&&n.jsx(en,{value:"downloads",children:(0,n.jsxs)(es,{children:[(0,n.jsxs)(ea,{children:[n.jsx(ei,{children:"Downloadable Items"}),n.jsx(eo,{children:"Access your digital downloads and products"})]}),n.jsx(el,{children:n.jsx("div",{className:"space-y-4",children:t.downloadableItems.nodes.map((e,t)=>n.jsx("div",{className:"border border-[#e5e2d9] p-4 rounded-md",children:(0,n.jsxs)("div",{className:"flex justify-between items-start",children:[(0,n.jsxs)("div",{className:"flex-1",children:[n.jsx("h3",{className:"font-medium text-[#2c2c27]",children:e.name}),(0,n.jsxs)("p",{className:"text-sm text-[#8a8778] mb-2",children:["Product: ",e.product.node.name]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm",children:[(0,n.jsxs)("div",{children:[n.jsx("label",{className:"text-xs text-[#8a8778]",children:"Download ID"}),n.jsx("p",{className:"text-[#2c2c27]",children:e.downloadId})]}),(0,n.jsxs)("div",{children:[n.jsx("label",{className:"text-xs text-[#8a8778]",children:"Downloads Remaining"}),n.jsx("p",{className:"text-[#2c2c27]",children:null!==e.downloadsRemaining?e.downloadsRemaining:"Unlimited"})]}),(0,n.jsxs)("div",{children:[n.jsx("label",{className:"text-xs text-[#8a8778]",children:"Access Expires"}),n.jsx("p",{className:"text-[#2c2c27]",children:e.accessExpires?new Date(e.accessExpires).toLocaleDateString():"Never"})]})]})]}),n.jsx("div",{className:"ml-4",children:n.jsx(ed.z,{variant:"outline",size:"sm",disabled:0===e.downloadsRemaining,children:"Download"})})]})},t))})})]})})]})]})}},71615:(e,t,r)=>{"use strict";var n=r(88757);r.o(n,"cookies")&&r.d(t,{cookies:function(){return n.cookies}})},58585:(e,t,r)=>{"use strict";var n=r(61085);r.o(n,"notFound")&&r.d(t,{notFound:function(){return n.notFound}}),r.o(n,"redirect")&&r.d(t,{redirect:function(){return n.redirect}})},33085:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"DraftMode",{enumerable:!0,get:function(){return a}});let n=r(45869),s=r(6278);class a{get isEnabled(){return this._provider.isEnabled}enable(){let e=n.staticGenerationAsyncStorage.getStore();return e&&(0,s.trackDynamicDataAccessed)(e,"draftMode().enable()"),this._provider.enable()}disable(){let e=n.staticGenerationAsyncStorage.getStore();return e&&(0,s.trackDynamicDataAccessed)(e,"draftMode().disable()"),this._provider.disable()}constructor(e){this._provider=e}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},88757:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cookies:function(){return f},draftMode:function(){return p},headers:function(){return u}});let n=r(68996),s=r(53047),a=r(92044),i=r(72934),o=r(33085),l=r(6278),c=r(45869),d=r(54580);function u(){let e="headers",t=c.staticGenerationAsyncStorage.getStore();if(t){if(t.forceStatic)return s.HeadersAdapter.seal(new Headers({}));(0,l.trackDynamicDataAccessed)(t,e)}return(0,d.getExpectedRequestStore)(e).headers}function f(){let e="cookies",t=c.staticGenerationAsyncStorage.getStore();if(t){if(t.forceStatic)return n.RequestCookiesAdapter.seal(new a.RequestCookies(new Headers({})));(0,l.trackDynamicDataAccessed)(t,e)}let r=(0,d.getExpectedRequestStore)(e),s=i.actionAsyncStorage.getStore();return(null==s?void 0:s.isAction)||(null==s?void 0:s.isAppRoute)?r.mutableCookies:r.cookies}function p(){let e=(0,d.getExpectedRequestStore)("draftMode");return new o.DraftMode(e.draftMode)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},61085:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return i},RedirectType:function(){return n.RedirectType},notFound:function(){return s.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect}});let n=r(83953),s=r(16399);class a extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class i extends URLSearchParams{append(){throw new a}delete(){throw new a}set(){throw new a}sort(){throw new a}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},16399:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isNotFoundError:function(){return s},notFound:function(){return n}});let r="NEXT_NOT_FOUND";function n(){let e=Error(r);throw e.digest=r,e}function s(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8586:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}}),function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(r||(r={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},83953:(e,t,r)=>{"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectType:function(){return n},getRedirectError:function(){return l},getRedirectStatusCodeFromError:function(){return m},getRedirectTypeFromError:function(){return p},getURLFromRedirectError:function(){return f},isRedirectError:function(){return u},permanentRedirect:function(){return d},redirect:function(){return c}});let s=r(54580),a=r(72934),i=r(8586),o="NEXT_REDIRECT";function l(e,t,r){void 0===r&&(r=i.RedirectStatusCode.TemporaryRedirect);let n=Error(o);n.digest=o+";"+t+";"+e+";"+r+";";let a=s.requestAsyncStorage.getStore();return a&&(n.mutableCookies=a.mutableCookies),n}function c(e,t){void 0===t&&(t="replace");let r=a.actionAsyncStorage.getStore();throw l(e,t,(null==r?void 0:r.isAction)?i.RedirectStatusCode.SeeOther:i.RedirectStatusCode.TemporaryRedirect)}function d(e,t){void 0===t&&(t="replace");let r=a.actionAsyncStorage.getStore();throw l(e,t,(null==r?void 0:r.isAction)?i.RedirectStatusCode.SeeOther:i.RedirectStatusCode.PermanentRedirect)}function u(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r,n,s]=e.digest.split(";",4),a=Number(s);return t===o&&("replace"===r||"push"===r)&&"string"==typeof n&&!isNaN(a)&&a in i.RedirectStatusCode}function f(e){return u(e)?e.digest.split(";",3)[2]:null}function p(e){if(!u(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}function m(e){if(!u(e))throw Error("Not a redirect error");return Number(e.digest.split(";",4)[3])}(function(e){e.push="push",e.replace="replace"})(n||(n={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},79925:e=>{"use strict";var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,s=Object.prototype.hasOwnProperty,a={};function i(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),n=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?n:`${n}; ${r.join("; ")}`}function o(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[n,s]=[r.slice(0,e),r.slice(e+1)];try{t.set(n,decodeURIComponent(null!=s?s:"true"))}catch{}}return t}function l(e){var t,r;if(!e)return;let[[n,s],...a]=o(e),{domain:i,expires:l,httponly:u,maxage:f,path:p,samesite:m,secure:h,partitioned:x,priority:g}=Object.fromEntries(a.map(([e,t])=>[e.toLowerCase(),t]));return function(e){let t={};for(let r in e)e[r]&&(t[r]=e[r]);return t}({name:n,value:decodeURIComponent(s),domain:i,...l&&{expires:new Date(l)},...u&&{httpOnly:!0},..."string"==typeof f&&{maxAge:Number(f)},path:p,...m&&{sameSite:c.includes(t=(t=m).toLowerCase())?t:void 0},...h&&{secure:!0},...g&&{priority:d.includes(r=(r=g).toLowerCase())?r:void 0},...x&&{partitioned:!0}})}((e,r)=>{for(var n in r)t(e,n,{get:r[n],enumerable:!0})})(a,{RequestCookies:()=>u,ResponseCookies:()=>f,parseCookie:()=>o,parseSetCookie:()=>l,stringifyCookie:()=>i}),e.exports=((e,a,i,o)=>{if(a&&"object"==typeof a||"function"==typeof a)for(let i of n(a))s.call(e,i)||void 0===i||t(e,i,{get:()=>a[i],enumerable:!(o=r(a,i))||o.enumerable});return e})(t({},"__esModule",{value:!0}),a);var c=["strict","lax","none"],d=["low","medium","high"],u=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of o(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===n).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,n=this._parsed;return n.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(n).map(([e,t])=>i(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>i(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},f=class{constructor(e){var t,r,n;this._parsed=new Map,this._headers=e;let s=null!=(n=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?n:[];for(let e of Array.isArray(s)?s:function(e){if(!e)return[];var t,r,n,s,a,i=[],o=0;function l(){for(;o<e.length&&/\s/.test(e.charAt(o));)o+=1;return o<e.length}for(;o<e.length;){for(t=o,a=!1;l();)if(","===(r=e.charAt(o))){for(n=o,o+=1,l(),s=o;o<e.length&&"="!==(r=e.charAt(o))&&";"!==r&&","!==r;)o+=1;o<e.length&&"="===e.charAt(o)?(a=!0,o=s,i.push(e.substring(t,n)),t=o):o=n+1}else o+=1;(!a||o>=e.length)&&i.push(e.substring(t,e.length))}return i}(s)){let t=l(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===n)}has(e){return this._parsed.has(e)}set(...e){let[t,r,n]=1===e.length?[e[0].name,e[0].value,e[0]]:e,s=this._parsed;return s.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...n})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=i(r);t.append("set-cookie",e)}}(s,this._headers),this}delete(...e){let[t,r,n]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0].path,e[0].domain];return this.set({name:t,path:r,domain:n,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(i).join("; ")}}},53047:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HeadersAdapter:function(){return a},ReadonlyHeadersError:function(){return s}});let n=r(38238);class s extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new s}}class a extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,s){if("symbol"==typeof r)return n.ReflectAdapter.get(t,r,s);let a=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===a);if(void 0!==i)return n.ReflectAdapter.get(t,i,s)},set(t,r,s,a){if("symbol"==typeof r)return n.ReflectAdapter.set(t,r,s,a);let i=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===i);return n.ReflectAdapter.set(t,o??r,s,a)},has(t,r){if("symbol"==typeof r)return n.ReflectAdapter.has(t,r);let s=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===s);return void 0!==a&&n.ReflectAdapter.has(t,a)},deleteProperty(t,r){if("symbol"==typeof r)return n.ReflectAdapter.deleteProperty(t,r);let s=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===s);return void 0===a||n.ReflectAdapter.deleteProperty(t,a)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return s.callable;default:return n.ReflectAdapter.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new a(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},68996:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{MutableRequestCookiesAdapter:function(){return u},ReadonlyRequestCookiesError:function(){return i},RequestCookiesAdapter:function(){return o},appendMutableCookies:function(){return d},getModifiedCookieValues:function(){return c}});let n=r(92044),s=r(38238),a=r(45869);class i extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#cookiessetname-value-options")}static callable(){throw new i}}class o{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return i.callable;default:return s.ReflectAdapter.get(e,t,r)}}})}}let l=Symbol.for("next.mutated.cookies");function c(e){let t=e[l];return t&&Array.isArray(t)&&0!==t.length?t:[]}function d(e,t){let r=c(t);if(0===r.length)return!1;let s=new n.ResponseCookies(e),a=s.getAll();for(let e of r)s.set(e);for(let e of a)s.set(e);return!0}class u{static wrap(e,t){let r=new n.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let i=[],o=new Set,c=()=>{let e=a.staticGenerationAsyncStorage.getStore();if(e&&(e.pathWasRevalidated=!0),i=r.getAll().filter(e=>o.has(e.name)),t){let e=[];for(let t of i){let r=new n.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}};return new Proxy(r,{get(e,t,r){switch(t){case l:return i;case"delete":return function(...t){o.add("string"==typeof t[0]?t[0]:t[0].name);try{e.delete(...t)}finally{c()}};case"set":return function(...t){o.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t)}finally{c()}};default:return s.ReflectAdapter.get(e,t,r)}}})}}},92044:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RequestCookies:function(){return n.RequestCookies},ResponseCookies:function(){return n.ResponseCookies},stringifyCookie:function(){return n.stringifyCookie}});let n=r(79925)},36147:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{default:()=>f,dynamic:()=>p,metadata:()=>m});var s=r(19510),a=r(58585),i=r(71615),o=r(70591),l=r(93690),c=r(63253),d=e([l]);l=(d.then?(await d)():d)[0];let p="force-dynamic",m={title:"My Account | Ankkor",description:"View your account details, order history, and manage your profile."},h=process.env.WOOCOMMERCE_GRAPHQL_URL||"https://your-wordpress-site.com/graphql",x=(0,l.gql)`
  query GetCustomer {
    customer {
      id
      databaseId
      email
      firstName
      lastName
      username
      billing {
        firstName
        lastName
        company
        address1
        address2
        city
        state
        postcode
        country
        email
        phone
      }
      shipping {
        firstName
        lastName
        company
        address1
        address2
        city
        state
        postcode
        country
      }
      orders {
        nodes {
          id
          databaseId
          date
          status
          total
          lineItems {
            nodes {
              product {
                node {
                  id
                  name
                }
              }
              quantity
              total
            }
          }
        }
      }
    }
  }
`;async function u(e){let t=new l.GraphQLClient(h,{headers:{"Content-Type":"application/json",Accept:"application/json",Authorization:`Bearer ${e}`}});try{let e=await t.request(x);return{success:!0,customer:e.customer}}catch(e){return console.error("Error fetching customer data:",e),{success:!1,error:"Failed to fetch customer data"}}}async function f(){let e=(0,i.cookies)().get("woo_auth_token");e&&e.value||(0,a.redirect)("/sign-in?redirect=/account");try{let t=(0,o.o)(e.value),r=Math.floor(Date.now()/1e3);t.exp<r&&(0,a.redirect)("/sign-in?redirect=/account&reason=expired")}catch(e){console.error("Invalid JWT token:",e),(0,a.redirect)("/sign-in?redirect=/account&reason=invalid")}let t=await u(e.value),r=t.success?t.customer:null;return r?(0,s.jsxs)("div",{className:"container mx-auto py-12 px-4",children:[s.jsx("h1",{className:"text-3xl font-serif mb-8",children:"My Account"}),r?s.jsx(c.Z,{}):s.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 p-4 rounded",children:"Unable to load account information. Please try again later."})]}):(0,s.jsxs)("div",{className:"container mx-auto py-12 px-4",children:[s.jsx("h1",{className:"text-3xl font-serif mb-8",children:"My Account"}),(0,s.jsxs)("div",{className:"bg-red-50 border border-red-200 text-red-700 p-4 rounded",children:["Unable to load account information. Please try ",s.jsx("a",{href:"/sign-in",className:"underline",children:"signing in again"}),"."]})]})}n()}catch(e){n(e)}})},63253:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\account\AccountDashboard.tsx#default`)},70591:(e,t,r)=>{"use strict";r.d(t,{o:()=>s});class n extends Error{}function s(e,t){let r;if("string"!=typeof e)throw new n("Invalid token specified: must be a string");t||(t={});let s=!0===t.header?0:1,a=e.split(".")[s];if("string"!=typeof a)throw new n(`Invalid token specified: missing part #${s+1}`);try{r=function(e){let t=e.replace(/-/g,"+").replace(/_/g,"/");switch(t.length%4){case 0:break;case 2:t+="==";break;case 3:t+="=";break;default:throw Error("base64 string is not of the correct length")}try{var r;return r=t,decodeURIComponent(atob(r).replace(/(.)/g,(e,t)=>{let r=t.charCodeAt(0).toString(16).toUpperCase();return r.length<2&&(r="0"+r),"%"+r}))}catch(e){return atob(t)}}(a)}catch(e){throw new n(`Invalid token specified: invalid base64 for part #${s+1} (${e.message})`)}try{return JSON.parse(r)}catch(e){throw new n(`Invalid token specified: invalid json for part #${s+1} (${e.message})`)}}n.prototype.name="InvalidTokenError"}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[8948,2344,2325,4931],()=>r(42291));module.exports=n})();