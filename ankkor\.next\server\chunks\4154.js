exports.id=4154,exports.ids=[4154],exports.modules={13417:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,12994,23)),Promise.resolve().then(r.t.bind(r,96114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,79671,23)),Promise.resolve().then(r.t.bind(r,41868,23)),Promise.resolve().then(r.t.bind(r,84759,23))},96799:(e,s,r)=>{Promise.resolve().then(r.bind(r,68897)),Promise.resolve().then(r.bind(r,75367))},54039:(e,s,r)=>{Promise.resolve().then(r.bind(r,83846))},83846:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>l});var t=r(10326);r(17577);var a=r(33265);let o=()=>t.jsx("div",{className:"animate-pulse flex space-x-4",children:(0,t.jsxs)("div",{className:"flex-1 space-y-6 py-1",children:[t.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[t.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-2"}),t.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-1"})]}),t.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"})]})]})}),i=(0,a.default)(async()=>{},{loadableGenerated:{modules:["app\\not-found.tsx -> ./not-found-content"]},ssr:!1,loading:()=>t.jsx(o,{})});function l(){return t.jsx("div",{className:"container mx-auto py-20",children:t.jsx(i,{})})}},32457:(e,s,r)=>{"use strict";r.d(s,{default:()=>d});var t=r(10326),a=r(17577),o=r(35047),i=r(74723),l=r(75290),n=r(68897);let d=({mode:e,redirectUrl:s="/"})=>{let r=(0,o.useRouter)(),{refreshCustomer:d}=(0,n.O)(),[c,m]=(0,a.useState)(!1),[u,x]=(0,a.useState)(null),[h,g]=(0,a.useState)(null),[f,p]=(0,a.useState)(null),b="login"===e,{register:y,handleSubmit:w,watch:N,formState:{errors:v}}=(0,i.cI)({mode:"onBlur"}),j=N("password",""),P=async e=>{m(!0),x(null),g(null),p(null);try{if(b){console.log("Attempting login with:",e.email);let t=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"login",username:e.email,password:e.password})}),a=await t.json();a.success?(g("Login successful! Redirecting..."),setTimeout(async()=>{await d(),r.push(s),r.refresh()},500)):x(a.message||"Login failed. Please check your credentials.")}else{console.log("Attempting registration for:",e.email);let t=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"register",email:e.email,firstName:e.firstName,lastName:e.lastName,password:e.password})}),a=await t.json();a.success?(g("Registration successful! Redirecting..."),await d(),setTimeout(()=>{r.push(s),r.refresh()},1e3)):x(a.message||"Registration failed. Please try again.")}}catch(e){console.error("Authentication error:",e),x(e.message||"An error occurred during authentication"),g(null)}finally{m(!1)}};return(0,t.jsxs)("div",{className:"max-w-md mx-auto bg-white p-8 border border-gray-200",children:[t.jsx("h2",{className:"text-2xl font-serif mb-6 text-center",children:b?"Sign In to Your Account":"Create an Account"}),u&&t.jsx("div",{className:"mb-4 p-3 bg-red-50 text-red-700 text-sm border border-red-200",children:u}),h&&t.jsx("div",{className:"mb-4 p-3 bg-green-50 text-green-700 text-sm border border-green-200",children:h}),f&&!1,(0,t.jsxs)("form",{onSubmit:w(P),className:"space-y-4",children:[!b&&t.jsx(t.Fragment,{children:(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"firstName",className:"block text-sm font-medium text-gray-700 mb-1",children:"First Name"}),t.jsx("input",{id:"firstName",type:"text",className:`w-full p-2 border ${v.firstName?"border-red-500":"border-gray-300"}`,...y("firstName",{required:"First name is required"})}),v.firstName&&t.jsx("p",{className:"mt-1 text-xs text-red-600",children:v.firstName.message})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"lastName",className:"block text-sm font-medium text-gray-700 mb-1",children:"Last Name"}),t.jsx("input",{id:"lastName",type:"text",className:`w-full p-2 border ${v.lastName?"border-red-500":"border-gray-300"}`,...y("lastName",{required:"Last name is required"})}),v.lastName&&t.jsx("p",{className:"mt-1 text-xs text-red-600",children:v.lastName.message})]})]})}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email Address"}),t.jsx("input",{id:"email",type:"email",className:`w-full p-2 border ${v.email?"border-red-500":"border-gray-300"}`,...y("email",{required:"Email is required",pattern:{value:/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,message:"Invalid email address"}})}),v.email&&t.jsx("p",{className:"mt-1 text-xs text-red-600",children:v.email.message})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),t.jsx("input",{id:"password",type:"password",className:`w-full p-2 border ${v.password?"border-red-500":"border-gray-300"}`,...y("password",{required:"Password is required",minLength:{value:8,message:"Password must be at least 8 characters"}})}),v.password&&t.jsx("p",{className:"mt-1 text-xs text-red-600",children:v.password.message})]}),!b&&(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:"Confirm Password"}),t.jsx("input",{id:"confirmPassword",type:"password",className:`w-full p-2 border ${v.confirmPassword?"border-red-500":"border-gray-300"}`,...y("confirmPassword",{required:"Please confirm your password",validate:e=>e===j||"Passwords do not match"})}),v.confirmPassword&&t.jsx("p",{className:"mt-1 text-xs text-red-600",children:v.confirmPassword.message})]}),t.jsx("button",{type:"submit",disabled:c,className:"w-full bg-[#2c2c27] text-white py-2 px-4 hover:bg-[#4c4c47] transition-colors duration-300 disabled:bg-gray-400 disabled:cursor-not-allowed",children:c?(0,t.jsxs)("span",{className:"flex items-center justify-center",children:[t.jsx(l.Z,{className:"animate-spin mr-2 h-4 w-4"}),b?"Signing in...":"Creating account..."]}):b?"Sign In":"Create Account"})]}),b?t.jsx("div",{className:"mt-4 text-center",children:t.jsx("a",{href:"/forgot-password",className:"text-sm text-[#2c2c27] hover:text-[#8a8778] underline",children:"Forgot your password?"})}):null]})}},68897:(e,s,r)=>{"use strict";r.d(s,{CustomerProvider:()=>d,O:()=>n});var t=r(10326),a=r(17577),o=r(35047),i=r(75367);let l=(0,a.createContext)({customer:null,isLoading:!0,isAuthenticated:!1,token:null,login:async()=>{},register:async()=>{},logout:()=>{},updateProfile:async()=>{},error:null,refreshCustomer:async()=>{}}),n=()=>(0,a.useContext)(l);function d({children:e}){let[s,r]=(0,a.useState)(null),[n,d]=(0,a.useState)(!0),[c,m]=(0,a.useState)(null),[u,x]=(0,a.useState)(null),h=(0,o.useRouter)(),{addToast:g}=(0,i.p)(),f=e=>e?{...e,displayName:e.displayName||e.username||`${e.firstName||""} ${e.lastName||""}`.trim()||"User"}:null,p=async()=>{try{console.log("CustomerProvider: Checking authentication via /api/auth/me");let e=await fetch("/api/auth/me",{method:"GET",credentials:"include"});console.log("CustomerProvider: Auth API response status:",e.status);let s=await e.json();if(console.log("CustomerProvider: Auth API result:",s),!s.success||!s.customer)return x(null),{success:!1,message:s.message||"Not authenticated"};{let e=s.token;return console.log("CustomerProvider: Token from API response:",!!e),x(e||null),{success:!0,customer:s.customer,token:e}}}catch(e){return console.error("CustomerProvider: Error checking authentication:",e),x(null),{success:!1,message:"Network error"}}},b=async()=>{try{let e=await p();if(e.success){let s={...e.customer,token:e.token};r(f(s)),console.log("Customer data refreshed successfully"),console.log("Token available after refresh:",!!e.token)}else console.log("Failed to refresh customer data:",e.message),r(null),x(null)}catch(e){console.error("Error refreshing customer data:",e),r(null),x(null)}},y=async e=>{d(!0),m(null);try{throw Error("Login temporarily disabled for build fix")}catch(s){let e="Login temporarily disabled for build fix";throw m(e),g(e,"error"),s}finally{d(!1)}},w=async e=>{d(!0),m(null);try{throw Error("Register temporarily disabled for build fix")}catch(s){let e="Register temporarily disabled for build fix";throw m(e),g(e,"error"),s}finally{d(!1)}},N=async e=>{d(!0),m(null);try{throw Error("Profile update temporarily disabled for build fix")}catch(s){let e="Profile update temporarily disabled for build fix";throw m(e),g(e,"error"),s}finally{d(!1)}};return t.jsx(l.Provider,{value:{customer:s,isLoading:n,isAuthenticated:!!s,token:u,login:y,register:w,logout:()=>{r(null),x(null),console.log("Logout completed, token cleared"),g("You have been signed out successfully","info"),h.push("/"),h.refresh()},updateProfile:N,error:c,refreshCustomer:b},children:e})}},75367:(e,s,r)=>{"use strict";r.d(s,{ToastProvider:()=>u,p:()=>x});var t=r(10326),a=r(17577),o=r(92148),i=r(86462),l=r(54659),n=r(87888),d=r(18019),c=r(94019);let m=(0,a.createContext)(void 0);function u({children:e}){let[s,r]=(0,a.useState)([]);return(0,t.jsxs)(m.Provider,{value:{toasts:s,addToast:(e,s="info",t=3e3)=>{let a=Math.random().toString(36).substring(2,9);r(r=>[...r,{id:a,message:e,type:s,duration:t}])},removeToast:e=>{r(s=>s.filter(s=>s.id!==e))}},children:[e,t.jsx(g,{})]})}function x(){let e=(0,a.useContext)(m);if(void 0===e)throw Error("useToast must be used within a ToastProvider");return e}function h({toast:e,onRemove:s}){return(0,t.jsxs)(o.E.div,{initial:{opacity:0,y:-50},animate:{opacity:1,y:0},exit:{opacity:0,x:300},className:`flex items-center p-4 rounded-lg border shadow-lg ${(()=>{switch(e.type){case"success":return"bg-[#f4f3f0] border-[#8a8778]";case"error":return"bg-red-50 border-red-200";default:return"bg-[#f8f8f5] border-[#e5e2d9]"}})()} max-w-md`,children:[t.jsx(()=>{switch(e.type){case"success":return t.jsx(l.Z,{className:"h-5 w-5"});case"error":return t.jsx(n.Z,{className:"h-5 w-5"});default:return t.jsx(d.Z,{className:"h-5 w-5"})}},{}),t.jsx("span",{className:"ml-3 text-sm font-medium flex-1",children:e.message}),t.jsx("button",{onClick:s,className:"ml-4 text-gray-400 hover:text-gray-600",children:t.jsx(c.Z,{className:"h-4 w-4"})})]})}function g(){let{toasts:e,removeToast:s}=x();return t.jsx("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:t.jsx(i.M,{children:e.map(e=>t.jsx(h,{toast:e,onRemove:()=>s(e.id)},e.id))})})}},51806:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>u,metadata:()=>m});var t=r(19510),a=r(10527),o=r.n(a),i=r(36822),l=r.n(i);r(5023);var n=r(68570);(0,n.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#useCustomer`);let d=(0,n.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#CustomerProvider`),c=(0,n.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#ToastProvider`);(0,n.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#useToast`);let m={title:"Ankkor | Timeless Menswear",description:"Elevated essentials for the discerning gentleman. Impeccably tailored garments crafted from the finest materials.",keywords:["menswear","luxury clothing","tailored","shirts","accessories"],icons:{icon:[{url:"/logo.PNG",sizes:"32x32",type:"image/png"},{url:"/logo.PNG",sizes:"16x16",type:"image/png"}],shortcut:"/logo.PNG",apple:"/logo.PNG"}};function u({children:e}){return t.jsx("html",{lang:"en",children:t.jsx("body",{className:`${o().variable} ${l().variable} font-sans antialiased min-h-screen bg-[#f8f8f5]`,children:t.jsx(c,{children:t.jsx(d,{children:t.jsx("main",{style:{paddingTop:0},className:"transition-all duration-300",children:e})})})})})}},12523:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\not-found.tsx#default`)},5023:()=>{}};