(()=>{var e={};e.id=7409,e.ids=[7409],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},24243:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>f,tree:()=>d}),t(35866),t(12523),t(51806);var s=t(23191),o=t(88716),n=t(37922),a=t.n(n),i=t(95231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let d=["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,51806)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],c=[],u="/_not-found/page",m={require:t,loadChunk:()=>Promise.resolve()},f=new s.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},13417:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,12994,23)),Promise.resolve().then(t.t.bind(t,96114,23)),Promise.resolve().then(t.t.bind(t,9727,23)),Promise.resolve().then(t.t.bind(t,79671,23)),Promise.resolve().then(t.t.bind(t,41868,23)),Promise.resolve().then(t.t.bind(t,84759,23))},96799:(e,r,t)=>{Promise.resolve().then(t.bind(t,68897)),Promise.resolve().then(t.bind(t,75367))},54039:(e,r,t)=>{Promise.resolve().then(t.bind(t,83846))},35303:()=>{},83846:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var s=t(10326);t(17577);var o=t(33265);let n=()=>s.jsx("div",{className:"animate-pulse flex space-x-4",children:(0,s.jsxs)("div",{className:"flex-1 space-y-6 py-1",children:[s.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[s.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-2"}),s.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-1"})]}),s.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"})]})]})}),a=(0,o.default)(async()=>{},{loadableGenerated:{modules:["app\\not-found.tsx -> ./not-found-content"]},ssr:!1,loading:()=>s.jsx(n,{})});function i(){return s.jsx("div",{className:"container mx-auto py-20",children:s.jsx(a,{})})}},68897:(e,r,t)=>{"use strict";t.d(r,{CustomerProvider:()=>d,O:()=>l});var s=t(10326),o=t(17577),n=t(35047),a=t(75367);let i=(0,o.createContext)({customer:null,isLoading:!0,isAuthenticated:!1,token:null,login:async()=>{},register:async()=>{},logout:()=>{},updateProfile:async()=>{},error:null,refreshCustomer:async()=>{}}),l=()=>(0,o.useContext)(i);function d({children:e}){let[r,t]=(0,o.useState)(null),[l,d]=(0,o.useState)(!0),[c,u]=(0,o.useState)(null),[m,f]=(0,o.useState)(null),h=(0,n.useRouter)(),{addToast:p}=(0,a.p)(),x=e=>e?{...e,displayName:e.displayName||e.username||`${e.firstName||""} ${e.lastName||""}`.trim()||"User"}:null,g=async()=>{try{console.log("CustomerProvider: Checking authentication via /api/auth/me");let e=await fetch("/api/auth/me",{method:"GET",credentials:"include"});console.log("CustomerProvider: Auth API response status:",e.status);let r=await e.json();if(console.log("CustomerProvider: Auth API result:",r),!r.success||!r.customer)return f(null),{success:!1,message:r.message||"Not authenticated"};{let e=r.token;return console.log("CustomerProvider: Token from API response:",!!e),f(e||null),{success:!0,customer:r.customer,token:e}}}catch(e){return console.error("CustomerProvider: Error checking authentication:",e),f(null),{success:!1,message:"Network error"}}},y=async()=>{try{let e=await g();if(e.success){let r={...e.customer,token:e.token};t(x(r)),console.log("Customer data refreshed successfully"),console.log("Token available after refresh:",!!e.token)}else console.log("Failed to refresh customer data:",e.message),t(null),f(null)}catch(e){console.error("Error refreshing customer data:",e),t(null),f(null)}},v=async e=>{d(!0),u(null);try{throw Error("Login temporarily disabled for build fix")}catch(r){let e="Login temporarily disabled for build fix";throw u(e),p(e,"error"),r}finally{d(!1)}},b=async e=>{d(!0),u(null);try{throw Error("Register temporarily disabled for build fix")}catch(r){let e="Register temporarily disabled for build fix";throw u(e),p(e,"error"),r}finally{d(!1)}},k=async e=>{d(!0),u(null);try{throw Error("Profile update temporarily disabled for build fix")}catch(r){let e="Profile update temporarily disabled for build fix";throw u(e),p(e,"error"),r}finally{d(!1)}};return s.jsx(i.Provider,{value:{customer:r,isLoading:l,isAuthenticated:!!r,token:m,login:v,register:b,logout:()=>{t(null),f(null),console.log("Logout completed, token cleared"),p("You have been signed out successfully","info"),h.push("/"),h.refresh()},updateProfile:k,error:c,refreshCustomer:y},children:e})}},75367:(e,r,t)=>{"use strict";t.d(r,{ToastProvider:()=>m,p:()=>f});var s=t(10326),o=t(17577),n=t(92148),a=t(86462),i=t(54659),l=t(87888),d=t(18019),c=t(94019);let u=(0,o.createContext)(void 0);function m({children:e}){let[r,t]=(0,o.useState)([]);return(0,s.jsxs)(u.Provider,{value:{toasts:r,addToast:(e,r="info",s=3e3)=>{let o=Math.random().toString(36).substring(2,9);t(t=>[...t,{id:o,message:e,type:r,duration:s}])},removeToast:e=>{t(r=>r.filter(r=>r.id!==e))}},children:[e,s.jsx(p,{})]})}function f(){let e=(0,o.useContext)(u);if(void 0===e)throw Error("useToast must be used within a ToastProvider");return e}function h({toast:e,onRemove:r}){return(0,s.jsxs)(n.E.div,{initial:{opacity:0,y:-50},animate:{opacity:1,y:0},exit:{opacity:0,x:300},className:`flex items-center p-4 rounded-lg border shadow-lg ${(()=>{switch(e.type){case"success":return"bg-[#f4f3f0] border-[#8a8778]";case"error":return"bg-red-50 border-red-200";default:return"bg-[#f8f8f5] border-[#e5e2d9]"}})()} max-w-md`,children:[s.jsx(()=>{switch(e.type){case"success":return s.jsx(i.Z,{className:"h-5 w-5"});case"error":return s.jsx(l.Z,{className:"h-5 w-5"});default:return s.jsx(d.Z,{className:"h-5 w-5"})}},{}),s.jsx("span",{className:"ml-3 text-sm font-medium flex-1",children:e.message}),s.jsx("button",{onClick:r,className:"ml-4 text-gray-400 hover:text-gray-600",children:s.jsx(c.Z,{className:"h-4 w-4"})})]})}function p(){let{toasts:e,removeToast:r}=f();return s.jsx("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:s.jsx(a.M,{children:e.map(e=>s.jsx(h,{toast:e,onRemove:()=>r(e.id)},e.id))})})}},35866:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"default",{enumerable:!0,get:function(){return n}}),t(53370);let s=t(19510);t(71159);let o={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},desc:{display:"inline-block"},h1:{display:"inline-block",margin:"0 20px 0 0",padding:"0 23px 0 0",fontSize:24,fontWeight:500,verticalAlign:"top",lineHeight:"49px"},h2:{fontSize:14,fontWeight:400,lineHeight:"49px",margin:0}};function n(){return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("title",{children:"404: This page could not be found."}),(0,s.jsx)("div",{style:o.error,children:(0,s.jsxs)("div",{children:[(0,s.jsx)("style",{dangerouslySetInnerHTML:{__html:"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}),(0,s.jsx)("h1",{className:"next-error-h1",style:o.h1,children:"404"}),(0,s.jsx)("div",{style:o.desc,children:(0,s.jsx)("h2",{style:o.h2,children:"This page could not be found."})})]})})]})}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},51806:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>m,metadata:()=>u});var s=t(19510),o=t(10527),n=t.n(o),a=t(36822),i=t.n(a);t(5023);var l=t(68570);(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#useCustomer`);let d=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#CustomerProvider`),c=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#ToastProvider`);(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#useToast`);let u={title:"Ankkor | Timeless Menswear",description:"Elevated essentials for the discerning gentleman. Impeccably tailored garments crafted from the finest materials.",keywords:["menswear","luxury clothing","tailored","shirts","accessories"],icons:{icon:[{url:"/logo.PNG",sizes:"32x32",type:"image/png"},{url:"/logo.PNG",sizes:"16x16",type:"image/png"}],shortcut:"/logo.PNG",apple:"/logo.PNG"}};function m({children:e}){return s.jsx("html",{lang:"en",children:s.jsx("body",{className:`${n().variable} ${i().variable} font-sans antialiased min-h-screen bg-[#f8f8f5]`,children:s.jsx(c,{children:s.jsx(d,{children:s.jsx("main",{style:{paddingTop:0},className:"transition-all duration-300",children:e})})})})})}},12523:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\not-found.tsx#default`)},5023:()=>{},53370:(e,r,t)=>{"use strict";function s(e){return e&&e.__esModule?e:{default:e}}t.r(r),t.d(r,{_:()=>s,_interop_require_default:()=>s})}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8948,2344],()=>t(24243));module.exports=s})();