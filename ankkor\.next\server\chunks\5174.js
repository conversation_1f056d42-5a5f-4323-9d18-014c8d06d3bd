exports.id=5174,exports.ids=[5174],exports.modules={13417:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,12994,23)),Promise.resolve().then(t.t.bind(t,96114,23)),Promise.resolve().then(t.t.bind(t,9727,23)),Promise.resolve().then(t.t.bind(t,79671,23)),Promise.resolve().then(t.t.bind(t,41868,23)),Promise.resolve().then(t.t.bind(t,84759,23))},96799:(e,r,t)=>{Promise.resolve().then(t.bind(t,68897)),Promise.resolve().then(t.bind(t,75367))},54039:(e,r,t)=>{Promise.resolve().then(t.bind(t,83846))},6035:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let s=(0,t(76557).Z)("Ruler",[["path",{d:"M21.3 15.3a2.4 2.4 0 0 1 0 3.4l-2.6 2.6a2.4 2.4 0 0 1-3.4 0L2.7 8.7a2.41 2.41 0 0 1 0-3.4l2.6-2.6a2.41 2.41 0 0 1 3.4 0Z",key:"icamh8"}],["path",{d:"m14.5 12.5 2-2",key:"inckbg"}],["path",{d:"m11.5 9.5 2-2",key:"fmmyf7"}],["path",{d:"m8.5 6.5 2-2",key:"vc6u1g"}],["path",{d:"m17.5 15.5 2-2",key:"wo5hmg"}]])},46226:(e,r,t)=>{"use strict";t.d(r,{default:()=>o.a});var s=t(69029),o=t.n(s)},90434:(e,r,t)=>{"use strict";t.d(r,{default:()=>o.a});var s=t(79404),o=t.n(s)},69029:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{default:function(){return i},getImageProps:function(){return l}});let s=t(91174),o=t(23078),a=t(92481),n=s._(t(86820));function l(e){let{props:r}=(0,o.getImgProps)(e,{defaultLoader:n.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!0,unoptimized:!1}});for(let[e,t]of Object.entries(r))void 0===t&&delete r[e];return{props:r}}let i=a.Image},83846:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l});var s=t(10326);t(17577);var o=t(33265);let a=()=>s.jsx("div",{className:"animate-pulse flex space-x-4",children:(0,s.jsxs)("div",{className:"flex-1 space-y-6 py-1",children:[s.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[s.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-2"}),s.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-1"})]}),s.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"})]})]})}),n=(0,o.default)(async()=>{},{loadableGenerated:{modules:["app\\not-found.tsx -> ./not-found-content"]},ssr:!1,loading:()=>s.jsx(a,{})});function l(){return s.jsx("div",{className:"container mx-auto py-20",children:s.jsx(n,{})})}},68897:(e,r,t)=>{"use strict";t.d(r,{CustomerProvider:()=>d,O:()=>i});var s=t(10326),o=t(17577),a=t(35047),n=t(75367);let l=(0,o.createContext)({customer:null,isLoading:!0,isAuthenticated:!1,token:null,login:async()=>{},register:async()=>{},logout:()=>{},updateProfile:async()=>{},error:null,refreshCustomer:async()=>{}}),i=()=>(0,o.useContext)(l);function d({children:e}){let[r,t]=(0,o.useState)(null),[i,d]=(0,o.useState)(!0),[c,u]=(0,o.useState)(null),[m,f]=(0,o.useState)(null),h=(0,a.useRouter)(),{addToast:g}=(0,n.p)(),p=e=>e?{...e,displayName:e.displayName||e.username||`${e.firstName||""} ${e.lastName||""}`.trim()||"User"}:null,x=async()=>{try{console.log("CustomerProvider: Checking authentication via /api/auth/me");let e=await fetch("/api/auth/me",{method:"GET",credentials:"include"});console.log("CustomerProvider: Auth API response status:",e.status);let r=await e.json();if(console.log("CustomerProvider: Auth API result:",r),!r.success||!r.customer)return f(null),{success:!1,message:r.message||"Not authenticated"};{let e=r.token;return console.log("CustomerProvider: Token from API response:",!!e),f(e||null),{success:!0,customer:r.customer,token:e}}}catch(e){return console.error("CustomerProvider: Error checking authentication:",e),f(null),{success:!1,message:"Network error"}}},y=async()=>{try{let e=await x();if(e.success){let r={...e.customer,token:e.token};t(p(r)),console.log("Customer data refreshed successfully"),console.log("Token available after refresh:",!!e.token)}else console.log("Failed to refresh customer data:",e.message),t(null),f(null)}catch(e){console.error("Error refreshing customer data:",e),t(null),f(null)}},v=async e=>{d(!0),u(null);try{throw Error("Login temporarily disabled for build fix")}catch(r){let e="Login temporarily disabled for build fix";throw u(e),g(e,"error"),r}finally{d(!1)}},b=async e=>{d(!0),u(null);try{throw Error("Register temporarily disabled for build fix")}catch(r){let e="Register temporarily disabled for build fix";throw u(e),g(e,"error"),r}finally{d(!1)}},k=async e=>{d(!0),u(null);try{throw Error("Profile update temporarily disabled for build fix")}catch(r){let e="Profile update temporarily disabled for build fix";throw u(e),g(e,"error"),r}finally{d(!1)}};return s.jsx(l.Provider,{value:{customer:r,isLoading:i,isAuthenticated:!!r,token:m,login:v,register:b,logout:()=>{t(null),f(null),console.log("Logout completed, token cleared"),g("You have been signed out successfully","info"),h.push("/"),h.refresh()},updateProfile:k,error:c,refreshCustomer:y},children:e})}},75367:(e,r,t)=>{"use strict";t.d(r,{ToastProvider:()=>m,p:()=>f});var s=t(10326),o=t(17577),a=t(92148),n=t(86462),l=t(54659),i=t(87888),d=t(18019),c=t(94019);let u=(0,o.createContext)(void 0);function m({children:e}){let[r,t]=(0,o.useState)([]);return(0,s.jsxs)(u.Provider,{value:{toasts:r,addToast:(e,r="info",s=3e3)=>{let o=Math.random().toString(36).substring(2,9);t(t=>[...t,{id:o,message:e,type:r,duration:s}])},removeToast:e=>{t(r=>r.filter(r=>r.id!==e))}},children:[e,s.jsx(g,{})]})}function f(){let e=(0,o.useContext)(u);if(void 0===e)throw Error("useToast must be used within a ToastProvider");return e}function h({toast:e,onRemove:r}){return(0,s.jsxs)(a.E.div,{initial:{opacity:0,y:-50},animate:{opacity:1,y:0},exit:{opacity:0,x:300},className:`flex items-center p-4 rounded-lg border shadow-lg ${(()=>{switch(e.type){case"success":return"bg-[#f4f3f0] border-[#8a8778]";case"error":return"bg-red-50 border-red-200";default:return"bg-[#f8f8f5] border-[#e5e2d9]"}})()} max-w-md`,children:[s.jsx(()=>{switch(e.type){case"success":return s.jsx(l.Z,{className:"h-5 w-5"});case"error":return s.jsx(i.Z,{className:"h-5 w-5"});default:return s.jsx(d.Z,{className:"h-5 w-5"})}},{}),s.jsx("span",{className:"ml-3 text-sm font-medium flex-1",children:e.message}),s.jsx("button",{onClick:r,className:"ml-4 text-gray-400 hover:text-gray-600",children:s.jsx(c.Z,{className:"h-4 w-4"})})]})}function g(){let{toasts:e,removeToast:r}=f();return s.jsx("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:s.jsx(n.M,{children:e.map(e=>s.jsx(h,{toast:e,onRemove:()=>r(e.id)},e.id))})})}},51806:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>m,metadata:()=>u});var s=t(19510),o=t(10527),a=t.n(o),n=t(36822),l=t.n(n);t(5023);var i=t(68570);(0,i.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#useCustomer`);let d=(0,i.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#CustomerProvider`),c=(0,i.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#ToastProvider`);(0,i.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#useToast`);let u={title:"Ankkor | Timeless Menswear",description:"Elevated essentials for the discerning gentleman. Impeccably tailored garments crafted from the finest materials.",keywords:["menswear","luxury clothing","tailored","shirts","accessories"],icons:{icon:[{url:"/logo.PNG",sizes:"32x32",type:"image/png"},{url:"/logo.PNG",sizes:"16x16",type:"image/png"}],shortcut:"/logo.PNG",apple:"/logo.PNG"}};function m({children:e}){return s.jsx("html",{lang:"en",children:s.jsx("body",{className:`${a().variable} ${l().variable} font-sans antialiased min-h-screen bg-[#f8f8f5]`,children:s.jsx(c,{children:s.jsx(d,{children:s.jsx("main",{style:{paddingTop:0},className:"transition-all duration-300",children:e})})})})})}},12523:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\not-found.tsx#default`)},5023:()=>{}};