(()=>{var e={};e.id=5212,e.ids=[5212],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},56614:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>d}),s(21042),s(51806),s(12523);var r=s(23191),a=s(88716),o=s(37922),n=s.n(o),i=s(95231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(t,l);let d=["",{children:["test-auth",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,21042)),"E:\\ankkorwoo\\ankkor\\src\\app\\test-auth\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,51806)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],c=["E:\\ankkorwoo\\ankkor\\src\\app\\test-auth\\page.tsx"],u="/test-auth/page",m={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/test-auth/page",pathname:"/test-auth",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9332:(e,t,s)=>{Promise.resolve().then(s.bind(s,32457))},21042:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(19510);s(71159);var a=s(55361);function o(){return(0,r.jsxs)("div",{className:"container mx-auto py-12",children:[r.jsx("h1",{className:"text-3xl font-bold mb-8 text-center",children:"WooCommerce Authentication Test"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-12",children:[(0,r.jsxs)("div",{className:"bg-white p-6 shadow-md",children:[r.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Login Form"}),r.jsx(a.Z,{mode:"login",redirectUrl:"/test-auth/success"})]}),(0,r.jsxs)("div",{className:"bg-white p-6 shadow-md",children:[r.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Register Form"}),r.jsx(a.Z,{mode:"register",redirectUrl:"/test-auth/success"})]})]}),(0,r.jsxs)("div",{className:"mt-12 bg-gray-50 p-6 border border-gray-200",children:[r.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Note"}),r.jsx("p",{children:"This page is used to test the WooCommerce authentication with Next.js 14's Server Components architecture."}),r.jsx("p",{className:"mt-2",children:"We've separated the client and server authentication logic to make it work with Next.js 14."})]})]})}},55361:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\auth\AuthForm.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[8948,2344,5010,4154],()=>s(56614));module.exports=r})();