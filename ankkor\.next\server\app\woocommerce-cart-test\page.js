(()=>{var e={};e.id=1660,e.ids=[1660],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21273:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>m,originalPathname:()=>d,pages:()=>u,routeModule:()=>h,tree:()=>c}),t(42399),t(51806),t(12523);var o=t(23191),s=t(88716),n=t(37922),a=t.n(n),i=t(95231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let c=["",{children:["woocommerce-cart-test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,42399)),"E:\\ankkorwoo\\ankkor\\src\\app\\woocommerce-cart-test\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,51806)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],u=["E:\\ankkorwoo\\ankkor\\src\\app\\woocommerce-cart-test\\page.tsx"],d="/woocommerce-cart-test/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new o.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/woocommerce-cart-test/page",pathname:"/woocommerce-cart-test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},13417:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,12994,23)),Promise.resolve().then(t.t.bind(t,96114,23)),Promise.resolve().then(t.t.bind(t,9727,23)),Promise.resolve().then(t.t.bind(t,79671,23)),Promise.resolve().then(t.t.bind(t,41868,23)),Promise.resolve().then(t.t.bind(t,84759,23))},96799:(e,r,t)=>{Promise.resolve().then(t.bind(t,68897)),Promise.resolve().then(t.bind(t,75367))},54039:(e,r,t)=>{Promise.resolve().then(t.bind(t,83846))},87024:(e,r,t)=>{Promise.resolve().then(t.bind(t,32903))},83846:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var o=t(10326);t(17577);var s=t(33265);let n=()=>o.jsx("div",{className:"animate-pulse flex space-x-4",children:(0,o.jsxs)("div",{className:"flex-1 space-y-6 py-1",children:[o.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"}),(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[o.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-2"}),o.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-1"})]}),o.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"})]})]})}),a=(0,s.default)(async()=>{},{loadableGenerated:{modules:["app\\not-found.tsx -> ./not-found-content"]},ssr:!1,loading:()=>o.jsx(n,{})});function i(){return o.jsx("div",{className:"container mx-auto py-20",children:o.jsx(a,{})})}},32903:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>v});var o=t(10326),s=t(17577),n=t(86806);class a{getCartToken(){return`cart_${Math.random().toString(36).substring(2)}_${Date.now()}`}headers(e){let r={"Content-Type":"application/json","Cart-Token":this.getCartToken()};return e&&(r["X-WC-Store-API-Nonce"]=e),r}fetchOptions(e="GET",r,t){let o={method:e,headers:this.headers(r),credentials:"include"};return t&&(o.body=JSON.stringify(t)),o}getWooCommerceSessionCookie(){if("undefined"==typeof document)return null;let e=document.cookie.split(";").find(e=>e.trim().startsWith("wp_woocommerce_session_"));return e?e.trim():null}debugToken(){console.log("CartSession: Running on server, no token available")}constructor(){this.CART_TOKEN_KEY="woo_cart_token",this.TOKEN_EXPIRY_KEY="woo_cart_token_expiry",this.TOKEN_EXPIRY_DAYS=30}}let i=new a,l={retries:3,initialDelay:500,maxDelay:1e4,jitter:.1,retryableError:()=>!0,onRetry:(e,r)=>console.warn(`Retry attempt ${r} after error:`,e)};function c(e,r={}){let t={...l,...r};return async function(...r){let o;for(let s=0;s<=t.retries;s++)try{return await e(...r)}catch(r){if(o=r,s>=t.retries||!t.retryableError(r))break;t.onRetry(r,s+1);let e=Math.min(t.initialDelay*Math.pow(2,s),t.maxDelay)*(1+t.jitter*(2*Math.random()-1));await new Promise(r=>setTimeout(r,e))}throw o}}function u(e){var r;return e instanceof TypeError||e.message?.includes("network")||e.message?.includes("Network")||e.message?.includes("fetch")||e.message?.includes("connection")||e.message?.includes("timeout")||e.message?.includes("abort")||(r=e).status>=500||r.response&&r.response.status>=500}let d="https://maroon-lapwing-781450.hostingersite.com",m={CART:"/wp-json/wc/store/v1/cart",CART_ITEMS:"/wp-json/wc/store/v1/cart/items",ADD_ITEM:"/wp-json/wc/store/v1/cart/add-item",CHECKOUT:"/wp-json/wc/store/v1/checkout"};function h(e){if("number"==typeof e)return e;if(/^[0-9]+$/.test(e))return Number(e);try{if(e.includes("=")){let r=Buffer.from(e,"base64").toString().match(/(\d+)$/);if(r)return Number(r[1])}}catch(e){console.warn("Error parsing product ID:",e)}return e}async function f(){try{let e=await p();if(e)return e;let r=await g();if(r)return r;let t=await x();if(t)return t;throw Error("Could not obtain a valid nonce from any source")}catch(e){throw console.error("Error fetching nonce:",e),e}}async function p(){try{if(!d)throw Error("WooCommerce URL not configured");let e=await fetch(`${d}${m.CART}`,i.fetchOptions("GET")),r=e.headers.get("x-wc-store-api-nonce")||e.headers.get("X-WC-Store-API-Nonce");if(r)return r;try{let r=await e.json();if(r.extensions&&r.extensions.store_api_nonce)return r.extensions.store_api_nonce}catch(e){console.warn("Error parsing cart response:",e)}return null}catch(e){return console.error("Error fetching nonce from cart:",e),null}}async function g(){try{let e=await fetch("/api/ankkor/v1/nonce",i.fetchOptions("GET"));if(!e.ok)return null;let r=await e.json();if(r&&r.nonce)return r.nonce;return null}catch(e){return console.error("Error fetching nonce from custom endpoint:",e),null}}async function x(){try{if(!d)throw Error("WooCommerce URL not configured");let e=await fetch(`${d}/wp-json/wc/store/v1/products?per_page=1`,i.fetchOptions("GET")),r=e.headers.get("x-wc-store-api-nonce")||e.headers.get("X-WC-Store-API-Nonce");if(r)return r;return null}catch(e){return console.error("Error fetching nonce from products:",e),null}}c(async e=>{if(!d)throw Error("WooCommerce URL not configured");let r=await fetch(`${d}${m.CART}`,i.fetchOptions("GET",e));if(!r.ok)throw Error(`Failed to get cart: ${r.status}`);return await r.json()},{retryableError:u});let y=c(async e=>{if(!d)throw Error("WooCommerce URL not configured");let r=await fetch(`${d}${m.CART_ITEMS}`,i.fetchOptions("DELETE",e));if(!r.ok)throw Error(`Failed to clear cart: ${r.status}`);return await r.json()},{retryableError:u}),w=c(async(e,r,t,o,s)=>{if(!d)throw Error("WooCommerce URL not configured");let n={id:h(r),quantity:t};o&&(n.variation_id=h(o)),s&&(n.variation=s);let a=await fetch(`${d}${m.ADD_ITEM}`,i.fetchOptions("POST",e,n));if(!a.ok)throw Error(`Failed to add item to cart: ${a.status}`);return await a.json()},{retryableError:u}),b=c(async(e,r)=>{if(!d)throw Error("WooCommerce URL not configured");if(0===r.length)throw Error("Cart is empty");await y(e);let t=null;for(let o of r){let r={};o.attributes&&o.attributes.length>0&&o.attributes.forEach(e=>{r[`attribute_${e.name.toLowerCase().replace(/\s+/g,"-")}`]=e.value}),t=await w(e,o.productId,o.quantity,o.variationId,Object.keys(r).length>0?r:void 0)}if(!t)throw Error("Failed to sync cart with WooCommerce");return t},{retryableError:u});function v(){let[e,r]=(0,s.useState)(!1),[t,a]=(0,s.useState)(null),[l,c]=(0,s.useState)(null),[u,d]=(0,s.useState)(null),m=(0,n.rY)(),h=async()=>{r(!0),a(null),c(null);try{i.debugToken();let e=await m.syncWithWooCommerce();if(e)c(`Cart synced successfully. Redirecting to: ${e}`),setTimeout(()=>{window.location.href=e},2e3);else throw Error("Failed to get checkout URL")}catch(e){console.error("Checkout error:",e),a(e instanceof Error?e.message:"An error occurred during checkout")}finally{r(!1)}},p=async()=>{r(!0),a(null),c(null);try{let e=await f();console.log("Fetched nonce:",e);let r=m.items;if(0===r.length)throw Error("Cart is empty");let t=await b(e,r);console.log("Cart sync response:",t);let o=i.getWooCommerceSessionCookie();d({nonce:e,cartItems:r,cartResponse:t,wooSessionCookie:o}),c("Direct API call successful")}catch(e){console.error("API error:",e),a(e instanceof Error?e.message:"An error occurred during API call")}finally{r(!1)}};return(0,o.jsxs)("div",{className:"container mx-auto p-4",children:[o.jsx("h1",{className:"text-2xl font-bold mb-6",children:"WooCommerce Cart Test"}),(0,o.jsxs)("div",{className:"mb-8",children:[o.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Cart Contents"}),0===m.items.length?o.jsx("p",{children:"Cart is empty"}):(0,o.jsxs)("div",{children:[(0,o.jsxs)("p",{children:["Items: ",m.items.length]}),o.jsx("ul",{className:"list-disc pl-5",children:m.items.map(e=>(0,o.jsxs)("li",{children:[e.name," - Quantity: ",e.quantity," - Price: $",e.price]},e.id))}),(0,o.jsxs)("p",{className:"mt-2",children:["Subtotal: $",m.subtotal().toFixed(2)]})]})]}),(0,o.jsxs)("div",{className:"flex flex-wrap gap-4 mb-6",children:[o.jsx("button",{onClick:()=>{m.addToCart({productId:"123",name:"Test Product",price:"99.99",quantity:1,image:{url:"/shirt.png",altText:"Test Product"}}),c("Test product added to cart")},className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",children:"Add Test Product"}),o.jsx("button",{onClick:h,disabled:e||0===m.items.length,className:`px-4 py-2 ${e||0===m.items.length?"bg-gray-400":"bg-green-600 hover:bg-green-700"} text-white rounded`,children:e?"Processing...":"Proceed to Checkout"}),o.jsx("button",{onClick:p,disabled:e||0===m.items.length,className:`px-4 py-2 ${e||0===m.items.length?"bg-gray-400":"bg-purple-600 hover:bg-purple-700"} text-white rounded`,children:"Test Direct API Call"}),o.jsx("button",{onClick:()=>{m.clearCart(),c("Cart cleared")},disabled:e||0===m.items.length,className:`px-4 py-2 ${e||0===m.items.length?"bg-gray-400":"bg-red-600 hover:bg-red-700"} text-white rounded`,children:"Clear Cart"})]}),t&&(0,o.jsxs)("div",{className:"p-4 mb-4 bg-red-50 border border-red-200 text-red-700 rounded",children:[o.jsx("strong",{children:"Error:"})," ",t]}),l&&(0,o.jsxs)("div",{className:"p-4 mb-4 bg-green-50 border border-green-200 text-green-700 rounded",children:[o.jsx("strong",{children:"Success:"})," ",l]}),u&&(0,o.jsxs)("div",{className:"mt-8",children:[o.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Debug Information"}),o.jsx("pre",{className:"p-4 bg-gray-100 rounded overflow-auto max-h-96",children:JSON.stringify(u,null,2)})]})]})}c(async(e,r)=>{if(!d)throw Error("WooCommerce URL not configured");let t=await fetch(`${d}${m.CHECKOUT}`,i.fetchOptions("POST",e,r));if(!t.ok){let e=await t.text();throw Error(`Checkout failed: ${t.status} - ${e}`)}return await t.json()},{retryableError:u})},68897:(e,r,t)=>{"use strict";t.d(r,{CustomerProvider:()=>c,O:()=>l});var o=t(10326),s=t(17577),n=t(35047),a=t(75367);let i=(0,s.createContext)({customer:null,isLoading:!0,isAuthenticated:!1,token:null,login:async()=>{},register:async()=>{},logout:()=>{},updateProfile:async()=>{},error:null,refreshCustomer:async()=>{}}),l=()=>(0,s.useContext)(i);function c({children:e}){let[r,t]=(0,s.useState)(null),[l,c]=(0,s.useState)(!0),[u,d]=(0,s.useState)(null),[m,h]=(0,s.useState)(null),f=(0,n.useRouter)(),{addToast:p}=(0,a.p)(),g=e=>e?{...e,displayName:e.displayName||e.username||`${e.firstName||""} ${e.lastName||""}`.trim()||"User"}:null,x=async()=>{try{console.log("CustomerProvider: Checking authentication via /api/auth/me");let e=await fetch("/api/auth/me",{method:"GET",credentials:"include"});console.log("CustomerProvider: Auth API response status:",e.status);let r=await e.json();if(console.log("CustomerProvider: Auth API result:",r),!r.success||!r.customer)return h(null),{success:!1,message:r.message||"Not authenticated"};{let e=r.token;return console.log("CustomerProvider: Token from API response:",!!e),h(e||null),{success:!0,customer:r.customer,token:e}}}catch(e){return console.error("CustomerProvider: Error checking authentication:",e),h(null),{success:!1,message:"Network error"}}},y=async()=>{try{let e=await x();if(e.success){let r={...e.customer,token:e.token};t(g(r)),console.log("Customer data refreshed successfully"),console.log("Token available after refresh:",!!e.token)}else console.log("Failed to refresh customer data:",e.message),t(null),h(null)}catch(e){console.error("Error refreshing customer data:",e),t(null),h(null)}},w=async e=>{c(!0),d(null);try{throw Error("Login temporarily disabled for build fix")}catch(r){let e="Login temporarily disabled for build fix";throw d(e),p(e,"error"),r}finally{c(!1)}},b=async e=>{c(!0),d(null);try{throw Error("Register temporarily disabled for build fix")}catch(r){let e="Register temporarily disabled for build fix";throw d(e),p(e,"error"),r}finally{c(!1)}},v=async e=>{c(!0),d(null);try{throw Error("Profile update temporarily disabled for build fix")}catch(r){let e="Profile update temporarily disabled for build fix";throw d(e),p(e,"error"),r}finally{c(!1)}};return o.jsx(i.Provider,{value:{customer:r,isLoading:l,isAuthenticated:!!r,token:m,login:w,register:b,logout:()=>{t(null),h(null),console.log("Logout completed, token cleared"),p("You have been signed out successfully","info"),f.push("/"),f.refresh()},updateProfile:v,error:u,refreshCustomer:y},children:e})}},75367:(e,r,t)=>{"use strict";t.d(r,{ToastProvider:()=>m,p:()=>h});var o=t(10326),s=t(17577),n=t(92148),a=t(86462),i=t(54659),l=t(87888),c=t(18019),u=t(94019);let d=(0,s.createContext)(void 0);function m({children:e}){let[r,t]=(0,s.useState)([]);return(0,o.jsxs)(d.Provider,{value:{toasts:r,addToast:(e,r="info",o=3e3)=>{let s=Math.random().toString(36).substring(2,9);t(t=>[...t,{id:s,message:e,type:r,duration:o}])},removeToast:e=>{t(r=>r.filter(r=>r.id!==e))}},children:[e,o.jsx(p,{})]})}function h(){let e=(0,s.useContext)(d);if(void 0===e)throw Error("useToast must be used within a ToastProvider");return e}function f({toast:e,onRemove:r}){return(0,o.jsxs)(n.E.div,{initial:{opacity:0,y:-50},animate:{opacity:1,y:0},exit:{opacity:0,x:300},className:`flex items-center p-4 rounded-lg border shadow-lg ${(()=>{switch(e.type){case"success":return"bg-[#f4f3f0] border-[#8a8778]";case"error":return"bg-red-50 border-red-200";default:return"bg-[#f8f8f5] border-[#e5e2d9]"}})()} max-w-md`,children:[o.jsx(()=>{switch(e.type){case"success":return o.jsx(i.Z,{className:"h-5 w-5"});case"error":return o.jsx(l.Z,{className:"h-5 w-5"});default:return o.jsx(c.Z,{className:"h-5 w-5"})}},{}),o.jsx("span",{className:"ml-3 text-sm font-medium flex-1",children:e.message}),o.jsx("button",{onClick:r,className:"ml-4 text-gray-400 hover:text-gray-600",children:o.jsx(u.Z,{className:"h-4 w-4"})})]})}function p(){let{toasts:e,removeToast:r}=h();return o.jsx("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:o.jsx(a.M,{children:e.map(e=>o.jsx(f,{toast:e,onRemove:()=>r(e.id)},e.id))})})}},51806:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>m,metadata:()=>d});var o=t(19510),s=t(10527),n=t.n(s),a=t(36822),i=t.n(a);t(5023);var l=t(68570);(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#useCustomer`);let c=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#CustomerProvider`),u=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#ToastProvider`);(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#useToast`);let d={title:"Ankkor | Timeless Menswear",description:"Elevated essentials for the discerning gentleman. Impeccably tailored garments crafted from the finest materials.",keywords:["menswear","luxury clothing","tailored","shirts","accessories"],icons:{icon:[{url:"/logo.PNG",sizes:"32x32",type:"image/png"},{url:"/logo.PNG",sizes:"16x16",type:"image/png"}],shortcut:"/logo.PNG",apple:"/logo.PNG"}};function m({children:e}){return o.jsx("html",{lang:"en",children:o.jsx("body",{className:`${n().variable} ${i().variable} font-sans antialiased min-h-screen bg-[#f8f8f5]`,children:o.jsx(u,{children:o.jsx(c,{children:o.jsx("main",{style:{paddingTop:0},className:"transition-all duration-300",children:e})})})})})}},12523:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});let o=(0,t(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\not-found.tsx#default`)},42399:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});let o=(0,t(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\woocommerce-cart-test\page.tsx#default`)},5023:()=>{}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8948,2344,7207,6806],()=>t(21273));module.exports=o})();