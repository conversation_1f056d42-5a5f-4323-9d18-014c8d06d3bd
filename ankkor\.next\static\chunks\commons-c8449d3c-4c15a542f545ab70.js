"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9482],{77690:function(t,e,n){n.d(e,{ts:function(){return p},x4:function(){return g},z2:function(){return m}});var o=n(45008),r=n(34206);function a(){let t=(0,o._)(['\n  mutation LoginUser($username: String!, $password: String!) {\n    login(input: {\n      clientMutationId: "login"\n      username: $username\n      password: $password\n    }) {\n      authToken\n      refreshToken\n      user {\n        id\n        databaseId\n        email\n        firstName\n        lastName\n      }\n    }\n  }\n']);return a=function(){return t},t}function s(){let t=(0,o._)(["\n  mutation RegisterUser($input: RegisterCustomerInput!) {\n    registerCustomer(input: $input) {\n      clientMutationId\n      authToken\n      refreshToken\n      customer {\n        id\n        databaseId\n        email\n        firstName\n        lastName\n      }\n    }\n  }\n"]);return s=function(){return t},t}function i(){let t=(0,o._)(["\n  mutation RefreshAuthToken($input: RefreshJwtAuthTokenInput!) {\n    refreshJwtAuthToken(input: $input) {\n      authToken\n    }\n  }\n"]);return i=function(){return t},t}function c(){let t=(0,o._)(["\n  query GetCustomer {\n    customer {\n      id\n      databaseId\n      email\n      firstName\n      lastName\n      billing {\n        firstName\n        lastName\n        company\n        address1\n        address2\n        city\n        state\n        postcode\n        country\n        email\n        phone\n      }\n      shipping {\n        firstName\n        lastName\n        company\n        address1\n        address2\n        city\n        state\n        postcode\n        country\n      }\n      orders {\n        nodes {\n          id\n          databaseId\n          date\n          status\n          total\n          lineItems {\n            nodes {\n              product {\n                node {\n                  id\n                  name\n                }\n              }\n              quantity\n              total\n            }\n          }\n        }\n      }\n    }\n  }\n"]);return c=function(){return t},t}function l(){let t=(0,o._)(["\n  mutation UpdateCustomer($input: UpdateCustomerInput!) {\n    updateCustomer(input: $input) {\n      clientMutationId\n      customer {\n        id\n        databaseId\n        email\n        firstName\n        lastName\n        displayName\n        billing {\n          firstName\n          lastName\n          company\n          address1\n          address2\n          city\n          state\n          postcode\n          country\n          email\n          phone\n        }\n        shipping {\n          firstName\n          lastName\n          company\n          address1\n          address2\n          city\n          state\n          postcode\n          country\n        }\n      }\n    }\n  }\n"]);return l=function(){return t},t}n(14474),(0,r.Ps)(a()),(0,r.Ps)(s()),(0,r.Ps)(i()),(0,r.Ps)(c()),(0,r.Ps)(l());let u="https://maroon-lapwing-781450.hostingersite.com/graphql",d=u&&!u.startsWith("http")?"https://".concat(u):u;async function g(t,e){try{let n=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"login",username:t,password:e}),credentials:"include"});if(!n.ok){let t=await n.json();throw Error(t.message||"Login failed")}let o=await n.json();if(o.success&&o.user)return"undefined"!=typeof localStorage&&localStorage.setItem("auth_session_started",Date.now().toString()),console.log("Login successful, user data received"),{success:!0,user:o.user,token:o.token};throw console.error("Login response missing user data"),Error("Login failed: Invalid response from server")}catch(t){return console.error("Login error:",t),{success:!1,message:t.message||"Login failed"}}}async function m(t,e,n,o){try{let r=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"register",email:t,firstName:e,lastName:n,password:o}),credentials:"include"});if(!r.ok){let t=await r.json();throw Error(t.message||"Registration failed")}let a=await r.json();if(a.success&&a.customer)return"undefined"!=typeof localStorage&&localStorage.setItem("auth_session_started",Date.now().toString()),console.log("Registration successful, user data received"),{success:!0,customer:a.customer,token:a.token};throw console.error("Registration response missing customer data"),Error("Registration failed: Invalid response from server")}catch(t){return console.error("Registration error:",t),{success:!1,message:t.message||"Registration failed"}}}async function p(){try{let t=await fetch("/api/auth/user",{method:"GET",headers:{"Content-Type":"application/json"}});if(401===t.status)return null;let e=await t.json();if(!t.ok||!e.success)return null;return e.user}catch(t){return console.error("Get user error:",t),null}}new r.g6(d,{headers:{"Content-Type":"application/json"}})},70597:function(t,e,n){n.d(e,{EJ:function(){return r},J6:function(){return o}});let o="₹",r="INR"},94348:function(t,e,n){n.r(e),n.d(e,{addInventoryMapping:function(){return d},clearInventoryMappings:function(){return y},getAllInventoryMappings:function(){return p},getProductHandleFromInventory:function(){return g},loadInventoryMap:function(){return l},saveInventoryMap:function(){return u},updateInventoryMappings:function(){return m}});var o=n(29865),r=n(40257);let a="inventory:mapping:",s=new o.s({url:r.env.UPSTASH_REDIS_REST_URL||r.env.NEXT_PUBLIC_KV_REST_API_URL||"",token:r.env.UPSTASH_REDIS_REST_TOKEN||r.env.NEXT_PUBLIC_KV_REST_API_TOKEN||""}),i={};function c(){return!!(r.env.UPSTASH_REDIS_REST_URL&&r.env.UPSTASH_REDIS_REST_TOKEN||r.env.NEXT_PUBLIC_KV_REST_API_URL&&r.env.NEXT_PUBLIC_KV_REST_API_TOKEN)}async function l(){if(!c())return{...i};try{let t=await s.keys("".concat(a,"*"));if(0===t.length)return console.log("No existing inventory mappings found in Redis"),{};let e={},n=await s.mget(...t);return t.forEach((t,o)=>{let r=t.replace(a,""),s=n[o];e[r]=s}),console.log("Loaded inventory mapping with ".concat(Object.keys(e).length," entries from Redis")),e}catch(t){return console.error("Error loading inventory mapping from Redis:",t),console.log("Falling back to in-memory storage"),{...i}}}async function u(t){if(c())try{let e=s.pipeline(),n=await s.keys("".concat(a,"*"));n.length>0&&e.del(...n),Object.entries(t).forEach(t=>{let[n,o]=t;e.set("".concat(a).concat(n),o)}),await e.exec(),console.log("Saved inventory mapping with ".concat(Object.keys(t).length," entries to Redis"))}catch(e){console.error("Error saving inventory mapping to Redis:",e),console.log("Falling back to in-memory storage"),Object.assign(i,t)}else Object.assign(i,t),console.log("Saved inventory mapping with ".concat(Object.keys(t).length," entries to memory"))}async function d(t,e){try{return c()?(await s.set("".concat(a).concat(t),e),console.log("Added mapping to Redis: ".concat(t," -> ").concat(e))):(i[t]=e,console.log("Added mapping to memory: ".concat(t," -> ").concat(e))),!0}catch(n){console.error("Error adding inventory mapping:",n);try{return i[t]=e,console.log("Added mapping to memory fallback: ".concat(t," -> ").concat(e)),!0}catch(t){return console.error("Error adding to memory fallback:",t),!1}}}async function g(t){try{if(c())return await s.get("".concat(a).concat(t))||null;return i[t]||null}catch(e){console.error("Error getting product handle from Redis:",e);try{return i[t]||null}catch(t){return console.error("Error getting from memory fallback:",t),null}}}async function m(t){try{if(c()){let e=s.pipeline();for(let{inventoryItemId:n,productHandle:o}of t)e.set("".concat(a).concat(n),o);await e.exec(),console.log("Updated ".concat(t.length," inventory mappings in Redis"))}else{for(let{inventoryItemId:e,productHandle:n}of t)i[e]=n;console.log("Updated ".concat(t.length," inventory mappings in memory"))}return!0}catch(e){console.error("Error updating inventory mappings in Redis:",e);try{for(let{inventoryItemId:e,productHandle:n}of t)i[e]=n;return console.log("Updated ".concat(t.length," inventory mappings in memory fallback")),!0}catch(t){return console.error("Error updating in memory fallback:",t),!1}}}async function p(){return await l()}async function y(){try{if(c()){let t=await s.keys("".concat(a,"*"));t.length>0&&await s.del(...t),console.log("Cleared all inventory mappings from Redis")}else Object.keys(i).forEach(t=>{delete i[t]}),console.log("Cleared all inventory mappings from memory");return!0}catch(t){return console.error("Error clearing inventory mappings:",t),!1}}},87758:function(t,e,n){n.d(e,{rY:function(){return i}});var o=n(59625),r=n(89134);let a=()=>Math.random().toString(36).substring(2,15),s=async(t,e,n)=>{try{let o=await fetch("/api/products/".concat(t,"/stock").concat(n?"?variation_id=".concat(n):""));if(!o.ok)return{available:!1,message:"Unable to verify stock availability"};let r=await o.json();if("IN_STOCK"!==r.stockStatus&&"instock"!==r.stockStatus)return{available:!1,message:"This product is currently out of stock",stockStatus:r.stockStatus};if(null!==r.stockQuantity&&r.stockQuantity<e)return{available:!1,message:"Only ".concat(r.stockQuantity," items available in stock"),stockQuantity:r.stockQuantity,stockStatus:r.stockStatus};return{available:!0,stockQuantity:r.stockQuantity,stockStatus:r.stockStatus}}catch(t){return console.error("Stock validation error:",t),{available:!0,message:"Stock validation temporarily unavailable"}}},i=(0,o.Ue)()((0,r.tJ)((t,e)=>({items:[],itemCount:0,isLoading:!1,error:null,addToCart:async n=>{t({isLoading:!0,error:null});try{let o=await s(n.productId,n.quantity,n.variationId);if(!o.available)throw Error(o.message||"Product is out of stock");let r=e().items,i=n.price;"string"==typeof i&&(i=i.replace(/[₹$€£]/g,"").trim().replace(/,/g,""));let c={...n,price:i},l=r.findIndex(t=>t.productId===c.productId&&t.variationId===c.variationId);if(-1!==l){let e=[...r];e[l].quantity+=c.quantity,t({items:e,itemCount:e.reduce((t,e)=>t+e.quantity,0),isLoading:!1})}else{let e={...c,id:a()};t({items:[...r,e],itemCount:r.reduce((t,e)=>t+e.quantity,0)+e.quantity,isLoading:!1})}console.log("Item added to cart successfully");try{let t={state:{items:e().items,itemCount:e().itemCount,isLoading:!1,error:null},version:1};localStorage.setItem("ankkor-local-cart",JSON.stringify(t))}catch(t){console.warn("Failed to manually persist cart to localStorage:",t)}}catch(e){console.error("Error adding item to cart:",e),t({error:e instanceof Error?e.message:"An unknown error occurred",isLoading:!1})}},updateCartItem:(n,o)=>{t({isLoading:!0,error:null});try{let r=e().items;if(o<=0)return e().removeCartItem(n);let a=r.map(t=>t.id===n?{...t,quantity:o}:t);t({items:a,itemCount:a.reduce((t,e)=>t+e.quantity,0),isLoading:!1});try{let t={state:{items:a,itemCount:a.reduce((t,e)=>t+e.quantity,0),isLoading:!1,error:null},version:1};localStorage.setItem("ankkor-local-cart",JSON.stringify(t))}catch(t){console.warn("Failed to manually persist cart update to localStorage:",t)}}catch(e){console.error("Error updating cart item:",e),t({error:e instanceof Error?e.message:"An unknown error occurred",isLoading:!1})}},removeCartItem:n=>{t({isLoading:!0,error:null});try{let o=e().items.filter(t=>t.id!==n);t({items:o,itemCount:o.reduce((t,e)=>t+e.quantity,0),isLoading:!1});try{let t={state:{items:o,itemCount:o.reduce((t,e)=>t+e.quantity,0),isLoading:!1,error:null},version:1};localStorage.setItem("ankkor-local-cart",JSON.stringify(t))}catch(t){console.warn("Failed to manually persist cart removal to localStorage:",t)}}catch(e){console.error("Error removing cart item:",e),t({error:e instanceof Error?e.message:"An unknown error occurred",isLoading:!1})}},clearCart:()=>{t({items:[],itemCount:0,isLoading:!1,error:null});try{localStorage.setItem("ankkor-local-cart",JSON.stringify({state:{items:[],itemCount:0,isLoading:!1,error:null},version:1}))}catch(t){console.warn("Failed to manually persist cart clearing to localStorage:",t)}},setError:e=>{t({error:e})},setIsLoading:e=>{t({isLoading:e})},subtotal:()=>{let t=e().items;try{let e=t.reduce((t,e)=>{let n=0;if("string"==typeof e.price){let t=e.price.replace(/[₹$€£]/g,"").trim().replace(/,/g,"");n=parseFloat(t)}else n=e.price;return isNaN(n)?(console.warn("Invalid price for item ".concat(e.id,": ").concat(e.price)),t):t+n*e.quantity},0);return isNaN(e)?0:e}catch(t){return console.error("Error calculating subtotal:",t),0}},total:()=>{let t=e().subtotal();return isNaN(t)?0:t},syncWithWooCommerce:async n=>{let{items:o}=e();if(0===o.length)throw Error("Cart is empty");try{if(console.log("Syncing cart with WooCommerce..."),console.log("Auth token provided:",!!n),t({isLoading:!0}),n){console.log("User is authenticated, using JWT-to-Cookie bridge");try{let e=await c(n,o);return t({isLoading:!1}),e}catch(t){console.error("JWT-to-Cookie bridge failed:",t),console.log("Falling back to guest checkout...")}}console.log("User is not authenticated, redirecting to WooCommerce checkout");let e="".concat("https://maroon-lapwing-781450.hostingersite.com","/checkout/");return console.log("Guest checkout URL:",e),t({isLoading:!1}),e}catch(e){console.error("Error syncing cart with WooCommerce:",e),t({isLoading:!1});try{console.log("Attempting fallback method for cart sync...");let t="".concat("https://maroon-lapwing-781450.hostingersite.com","/checkout/?guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0&create_account=0&skip_login=1&force_guest_checkout=1");return o.forEach((e,n)=>{0===n?t+="&add-to-cart=".concat(e.productId,"&quantity=").concat(e.quantity):t+="&add-to-cart[".concat(n,"]=").concat(e.productId,"&quantity[").concat(n,"]=").concat(e.quantity),e.variationId&&(t+="&variation_id=".concat(e.variationId))}),console.log("Fallback checkout URL:",t),t}catch(t){throw console.error("Fallback method failed:",t),Error("Failed to sync cart with WooCommerce. Please try again or contact support.")}}}}),{name:"ankkor-local-cart",version:1}));async function c(t,e){if(!t)throw Error("Authentication token is required");let n="https://maroon-lapwing-781450.hostingersite.com",o="https://maroon-lapwing-781450.hostingersite.com/checkout/";if(!n||!o)throw Error("WordPress or checkout URL not configured. Check your environment variables.");try{console.log("Creating WordPress session from JWT token..."),console.log("Using endpoint:","".concat(n,"/wp-json/headless/v1/create-wp-session")),console.log("Token length:",t.length),console.log("Token preview:",t.substring(0,20)+"...");let e=await fetch("".concat(n,"/wp-json/headless/v1/create-wp-session"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)},body:JSON.stringify({token:t}),credentials:"include"});if(console.log("Response status:",e.status),console.log("Response headers:",Object.fromEntries(e.headers.entries())),!e.ok){let t="HTTP ".concat(e.status,": ").concat(e.statusText);try{let n=await e.json();t=n.message||n.code||t,console.error("Error response data:",n)}catch(t){console.error("Could not parse error response:",t)}throw Error("Failed to create WordPress session: ".concat(t))}let r=await e.json();if(console.log("Response data:",r),!r.success)throw Error(r.message||"Failed to create WordPress session");return console.log("WordPress session created successfully"),console.log("Redirecting to checkout URL:",o),o}catch(t){if(console.error("Error creating WordPress session:",t),t instanceof TypeError&&t.message.includes("fetch"))throw Error("Network error: Could not connect to WordPress. Please check your internet connection.");throw Error(t instanceof Error?t.message:"Failed to prepare checkout")}}},82429:function(t,e,n){function o(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"INR";switch(t){case"INR":return"₹";case"USD":return"$";case"EUR":return"€";case"GBP":return"\xa3";default:return t}}n.d(e,{jK:function(){return o}}),n(94348)}}]);