"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("commons-src_lib_a",{

/***/ "(app-pages-browser)/./src/lib/currency.ts":
/*!*****************************!*\
  !*** ./src/lib/currency.ts ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_CURRENCY_CODE: function() { return /* binding */ DEFAULT_CURRENCY_CODE; },\n/* harmony export */   DEFAULT_CURRENCY_SYMBOL: function() { return /* binding */ DEFAULT_CURRENCY_SYMBOL; },\n/* harmony export */   formatPrice: function() { return /* binding */ formatPrice; },\n/* harmony export */   formatPriceWithoutSymbol: function() { return /* binding */ formatPriceWithoutSymbol; },\n/* harmony export */   getCurrencySymbol: function() { return /* binding */ getCurrencySymbol; }\n/* harmony export */ });\n/**\r\n * Currency utility functions for Ankkor\r\n */ /**\r\n * Format a numeric price to a currency string\r\n */ function formatPrice(amount) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { locale = \"en-IN\", currency = \"INR\", minimumFractionDigits = 0, maximumFractionDigits = 2 } = options;\n    const numericAmount = typeof amount === \"string\" ? parseFloat(amount) : amount;\n    return new Intl.NumberFormat(locale, {\n        style: \"currency\",\n        currency,\n        minimumFractionDigits,\n        maximumFractionDigits\n    }).format(numericAmount);\n}\n/**\r\n * Get currency symbol for a given currency code\r\n */ function getCurrencySymbol() {\n    let currencyCode = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"INR\", locale = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"en-IN\";\n    return 0..toLocaleString(locale, {\n        style: \"currency\",\n        currency: currencyCode,\n        minimumFractionDigits: 0,\n        maximumFractionDigits: 0\n    }).replace(/\\d/g, \"\").trim();\n}\n/**\r\n * Format price without currency symbol\r\n */ function formatPriceWithoutSymbol(amount) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { locale = \"en-IN\", minimumFractionDigits = 0, maximumFractionDigits = 2 } = options;\n    const numericAmount = typeof amount === \"string\" ? parseFloat(amount) : amount;\n    return new Intl.NumberFormat(locale, {\n        style: \"decimal\",\n        minimumFractionDigits,\n        maximumFractionDigits\n    }).format(numericAmount);\n}\n/**\r\n * Default currency symbol for the application\r\n */ const DEFAULT_CURRENCY_SYMBOL = \"₹\";\nconst DEFAULT_CURRENCY_CODE = \"INR\";\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/currency.ts\n"));

/***/ })

});