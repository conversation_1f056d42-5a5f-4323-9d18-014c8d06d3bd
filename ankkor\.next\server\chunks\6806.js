"use strict";exports.id=6806,exports.ids=[6806],exports.modules={86806:(t,e,o)=>{o.d(e,{rY:()=>n});var r=o(60114),s=o(85251);let a=()=>Math.random().toString(36).substring(2,15),i=async(t,e,o)=>{try{let r=await fetch(`/api/products/${t}/stock${o?`?variation_id=${o}`:""}`);if(!r.ok)return{available:!1,message:"Unable to verify stock availability"};let s=await r.json();if("IN_STOCK"!==s.stockStatus&&"instock"!==s.stockStatus)return{available:!1,message:"This product is currently out of stock",stockStatus:s.stockStatus};if(null!==s.stockQuantity&&s.stockQuantity<e)return{available:!1,message:`Only ${s.stockQuantity} items available in stock`,stockQuantity:s.stockQuantity,stockStatus:s.stockStatus};return{available:!0,stockQuantity:s.stockQuantity,stockStatus:s.stockStatus}}catch(t){return console.error("Stock validation error:",t),{available:!0,message:"Stock validation temporarily unavailable"}}},n=(0,r.Ue)()((0,s.tJ)((t,e)=>({items:[],itemCount:0,isLoading:!1,error:null,addToCart:async o=>{t({isLoading:!0,error:null});try{let r=await i(o.productId,o.quantity,o.variationId);if(!r.available)throw Error(r.message||"Product is out of stock");let s=e().items,n=o.price;"string"==typeof n&&(n=n.replace(/[₹$€£]/g,"").trim().replace(/,/g,""));let c={...o,price:n},l=s.findIndex(t=>t.productId===c.productId&&t.variationId===c.variationId);if(-1!==l){let e=[...s];e[l].quantity+=c.quantity,t({items:e,itemCount:e.reduce((t,e)=>t+e.quantity,0),isLoading:!1})}else{let e={...c,id:a()};t({items:[...s,e],itemCount:s.reduce((t,e)=>t+e.quantity,0)+e.quantity,isLoading:!1})}console.log("Item added to cart successfully")}catch(e){console.error("Error adding item to cart:",e),t({error:e instanceof Error?e.message:"An unknown error occurred",isLoading:!1})}},updateCartItem:(o,r)=>{t({isLoading:!0,error:null});try{let s=e().items;if(r<=0)return e().removeCartItem(o);let a=s.map(t=>t.id===o?{...t,quantity:r}:t);t({items:a,itemCount:a.reduce((t,e)=>t+e.quantity,0),isLoading:!1})}catch(e){console.error("Error updating cart item:",e),t({error:e instanceof Error?e.message:"An unknown error occurred",isLoading:!1})}},removeCartItem:o=>{t({isLoading:!0,error:null});try{let r=e().items.filter(t=>t.id!==o);t({items:r,itemCount:r.reduce((t,e)=>t+e.quantity,0),isLoading:!1})}catch(e){console.error("Error removing cart item:",e),t({error:e instanceof Error?e.message:"An unknown error occurred",isLoading:!1})}},clearCart:()=>{t({items:[],itemCount:0,isLoading:!1,error:null})},setError:e=>{t({error:e})},setIsLoading:e=>{t({isLoading:e})},subtotal:()=>{let t=e().items;try{let e=t.reduce((t,e)=>{let o=0;if("string"==typeof e.price){let t=e.price.replace(/[₹$€£]/g,"").trim().replace(/,/g,"");o=parseFloat(t)}else o=e.price;return isNaN(o)?(console.warn(`Invalid price for item ${e.id}: ${e.price}`),t):t+o*e.quantity},0);return isNaN(e)?0:e}catch(t){return console.error("Error calculating subtotal:",t),0}},total:()=>{let t=e().subtotal();return isNaN(t)?0:t},syncWithWooCommerce:async o=>{let{items:r}=e();if(0===r.length)throw Error("Cart is empty");try{if(console.log("Syncing cart with WooCommerce..."),console.log("Auth token provided:",!!o),t({isLoading:!0}),o){console.log("User is authenticated, using JWT-to-Cookie bridge");try{let e=await c(o,r);return t({isLoading:!1}),e}catch(t){console.error("JWT-to-Cookie bridge failed:",t),console.log("Falling back to guest checkout...")}}console.log("User is not authenticated, redirecting to WooCommerce checkout");let e="https://maroon-lapwing-781450.hostingersite.com/checkout/";return console.log("Guest checkout URL:",e),t({isLoading:!1}),e}catch(e){console.error("Error syncing cart with WooCommerce:",e),t({isLoading:!1});try{console.log("Attempting fallback method for cart sync...");let t="https://maroon-lapwing-781450.hostingersite.com/checkout/?guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0&create_account=0&skip_login=1&force_guest_checkout=1";return r.forEach((e,o)=>{0===o?t+=`&add-to-cart=${e.productId}&quantity=${e.quantity}`:t+=`&add-to-cart[${o}]=${e.productId}&quantity[${o}]=${e.quantity}`,e.variationId&&(t+=`&variation_id=${e.variationId}`)}),console.log("Fallback checkout URL:",t),t}catch(t){throw console.error("Fallback method failed:",t),Error("Failed to sync cart with WooCommerce. Please try again or contact support.")}}}}),{name:"ankkor-local-cart",version:1}));async function c(t,e){if(!t)throw Error("Authentication token is required");let o="https://maroon-lapwing-781450.hostingersite.com",r="https://maroon-lapwing-781450.hostingersite.com/checkout/";if(!o||!r)throw Error("WordPress or checkout URL not configured. Check your environment variables.");try{console.log("Creating WordPress session from JWT token..."),console.log("Using endpoint:",`${o}/wp-json/headless/v1/create-wp-session`),console.log("Token length:",t.length),console.log("Token preview:",t.substring(0,20)+"...");let e=await fetch(`${o}/wp-json/headless/v1/create-wp-session`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${t}`},body:JSON.stringify({token:t}),credentials:"include"});if(console.log("Response status:",e.status),console.log("Response headers:",Object.fromEntries(e.headers.entries())),!e.ok){let t=`HTTP ${e.status}: ${e.statusText}`;try{let o=await e.json();t=o.message||o.code||t,console.error("Error response data:",o)}catch(t){console.error("Could not parse error response:",t)}throw Error(`Failed to create WordPress session: ${t}`)}let s=await e.json();if(console.log("Response data:",s),!s.success)throw Error(s.message||"Failed to create WordPress session");return console.log("WordPress session created successfully"),console.log("Redirecting to checkout URL:",r),r}catch(t){if(console.error("Error creating WordPress session:",t),t instanceof TypeError&&t.message.includes("fetch"))throw Error("Network error: Could not connect to WordPress. Please check your internet connection.");throw Error(t instanceof Error?t.message:"Failed to prepare checkout")}}}};