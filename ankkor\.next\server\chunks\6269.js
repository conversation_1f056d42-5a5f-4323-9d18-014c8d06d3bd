"use strict";exports.id=6269,exports.ids=[6269],exports.modules={99063:(e,n,r)=>{r.d(n,{Z:()=>i});var o=r(17577);r(10326),r(35047);let t=(0,o.createContext)({isLoading:!1,setLoading:()=>{},variant:"thread",setVariant:()=>{}}),a=()=>(0,o.useContext)(t),i=function(e,n){let{setLoading:r,setVariant:o}=a()}},92079:(e,n,r)=>{r.r(n),r.d(n,{addInventoryMapping:()=>g,clearInventoryMappings:()=>m,getAllInventoryMappings:()=>y,getProductHandleFromInventory:()=>p,loadInventoryMap:()=>l,saveInventoryMap:()=>c,updateInventoryMappings:()=>d});var o=r(78578);let t="inventory:mapping:",a=new o.s({url:process.env.UPSTASH_REDIS_REST_URL||process.env.NEXT_PUBLIC_KV_REST_API_URL||"",token:process.env.UPSTASH_REDIS_REST_TOKEN||process.env.NEXT_PUBLIC_KV_REST_API_TOKEN||""}),i={};function s(){return!!(process.env.UPSTASH_REDIS_REST_URL&&process.env.UPSTASH_REDIS_REST_TOKEN||process.env.NEXT_PUBLIC_KV_REST_API_URL&&process.env.NEXT_PUBLIC_KV_REST_API_TOKEN)}async function l(){if(!s())return{...i};try{let e=await a.keys(`${t}*`);if(0===e.length)return console.log("No existing inventory mappings found in Redis"),{};let n={},r=await a.mget(...e);return e.forEach((e,o)=>{let a=e.replace(t,""),i=r[o];n[a]=i}),console.log(`Loaded inventory mapping with ${Object.keys(n).length} entries from Redis`),n}catch(e){return console.error("Error loading inventory mapping from Redis:",e),console.log("Falling back to in-memory storage"),{...i}}}async function c(e){if(s())try{let n=a.pipeline(),r=await a.keys(`${t}*`);r.length>0&&n.del(...r),Object.entries(e).forEach(([e,r])=>{n.set(`${t}${e}`,r)}),await n.exec(),console.log(`Saved inventory mapping with ${Object.keys(e).length} entries to Redis`)}catch(n){console.error("Error saving inventory mapping to Redis:",n),console.log("Falling back to in-memory storage"),Object.assign(i,e)}else Object.assign(i,e),console.log(`Saved inventory mapping with ${Object.keys(e).length} entries to memory`)}async function g(e,n){try{return s()?(await a.set(`${t}${e}`,n),console.log(`Added mapping to Redis: ${e} -> ${n}`)):(i[e]=n,console.log(`Added mapping to memory: ${e} -> ${n}`)),!0}catch(r){console.error("Error adding inventory mapping:",r);try{return i[e]=n,console.log(`Added mapping to memory fallback: ${e} -> ${n}`),!0}catch(e){return console.error("Error adding to memory fallback:",e),!1}}}async function p(e){try{if(s())return await a.get(`${t}${e}`)||null;return i[e]||null}catch(n){console.error("Error getting product handle from Redis:",n);try{return i[e]||null}catch(e){return console.error("Error getting from memory fallback:",e),null}}}async function d(e){try{if(s()){let n=a.pipeline();for(let{inventoryItemId:r,productHandle:o}of e)n.set(`${t}${r}`,o);await n.exec(),console.log(`Updated ${e.length} inventory mappings in Redis`)}else{for(let{inventoryItemId:n,productHandle:r}of e)i[n]=r;console.log(`Updated ${e.length} inventory mappings in memory`)}return!0}catch(n){console.error("Error updating inventory mappings in Redis:",n);try{for(let{inventoryItemId:n,productHandle:r}of e)i[n]=r;return console.log(`Updated ${e.length} inventory mappings in memory fallback`),!0}catch(e){return console.error("Error updating in memory fallback:",e),!1}}}async function y(){return await l()}async function m(){try{if(s()){let e=await a.keys(`${t}*`);e.length>0&&await a.del(...e),console.log("Cleared all inventory mappings from Redis")}else Object.keys(i).forEach(e=>{delete i[e]}),console.log("Cleared all inventory mappings from memory");return!0}catch(e){return console.error("Error clearing inventory mappings:",e),!1}}},45107:(e,n,r)=>{r.d(n,{jK:()=>o}),r(92079);function o(e="INR"){switch(e){case"INR":return"₹";case"USD":return"$";case"EUR":return"€";case"GBP":return"\xa3";default:return e}}}};