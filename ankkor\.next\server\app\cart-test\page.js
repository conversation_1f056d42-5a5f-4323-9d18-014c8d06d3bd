(()=>{var e={};e.id=1214,e.ids=[1214],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5799:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d}),t(36663),t(51806),t(12523);var s=t(23191),o=t(88716),a=t(37922),n=t.n(a),l=t(95231),i={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);t.d(r,i);let d=["",{children:["cart-test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,36663)),"E:\\ankkorwoo\\ankkor\\src\\app\\cart-test\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,51806)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],c=["E:\\ankkorwoo\\ankkor\\src\\app\\cart-test\\page.tsx"],u="/cart-test/page",m={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/cart-test/page",pathname:"/cart-test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},13417:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,12994,23)),Promise.resolve().then(t.t.bind(t,96114,23)),Promise.resolve().then(t.t.bind(t,9727,23)),Promise.resolve().then(t.t.bind(t,79671,23)),Promise.resolve().then(t.t.bind(t,41868,23)),Promise.resolve().then(t.t.bind(t,84759,23))},96799:(e,r,t)=>{Promise.resolve().then(t.bind(t,68897)),Promise.resolve().then(t.bind(t,75367))},47474:(e,r,t)=>{Promise.resolve().then(t.bind(t,93198))},54039:(e,r,t)=>{Promise.resolve().then(t.bind(t,83846))},93198:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(10326);t(17577);var o=t(86806);function a(){let e=(0,o.rY)();return(0,s.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[s.jsx("h1",{className:"text-2xl font-bold mb-6",children:"Cart Test"}),(0,s.jsxs)("div",{className:"space-y-4",children:[s.jsx("button",{onClick:()=>{e.addToCart({productId:"123",quantity:1,name:"Test Product",price:"99.99",image:{url:"/placeholder-product.jpg",altText:"Test Product"}})},className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"Add Test Item to Cart"}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("h2",{className:"text-lg font-semibold",children:["Cart Items (",e.itemCount,")"]}),0===e.items.length?s.jsx("p",{children:"No items in cart"}):s.jsx("ul",{className:"space-y-2",children:e.items.map(r=>(0,s.jsxs)("li",{className:"border p-2 rounded",children:[(0,s.jsxs)("div",{children:[r.name," - ₹",r.price," x ",r.quantity]}),s.jsx("button",{onClick:()=>e.removeCartItem(r.id),className:"text-red-500 text-sm",children:"Remove"})]},r.id))})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{children:["Subtotal: ₹",e.subtotal().toFixed(2)]}),(0,s.jsxs)("p",{children:["Total: ₹",e.total().toFixed(2)]})]}),s.jsx("button",{onClick:e.clearCart,className:"px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600",children:"Clear Cart"})]})]})}},83846:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l});var s=t(10326);t(17577);var o=t(33265);let a=()=>s.jsx("div",{className:"animate-pulse flex space-x-4",children:(0,s.jsxs)("div",{className:"flex-1 space-y-6 py-1",children:[s.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[s.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-2"}),s.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-1"})]}),s.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"})]})]})}),n=(0,o.default)(async()=>{},{loadableGenerated:{modules:["app\\not-found.tsx -> ./not-found-content"]},ssr:!1,loading:()=>s.jsx(a,{})});function l(){return s.jsx("div",{className:"container mx-auto py-20",children:s.jsx(n,{})})}},68897:(e,r,t)=>{"use strict";t.d(r,{CustomerProvider:()=>d,O:()=>i});var s=t(10326),o=t(17577),a=t(35047),n=t(75367);let l=(0,o.createContext)({customer:null,isLoading:!0,isAuthenticated:!1,token:null,login:async()=>{},register:async()=>{},logout:()=>{},updateProfile:async()=>{},error:null,refreshCustomer:async()=>{}}),i=()=>(0,o.useContext)(l);function d({children:e}){let[r,t]=(0,o.useState)(null),[i,d]=(0,o.useState)(!0),[c,u]=(0,o.useState)(null),[m,x]=(0,o.useState)(null),p=(0,a.useRouter)(),{addToast:h}=(0,n.p)(),g=e=>e?{...e,displayName:e.displayName||e.username||`${e.firstName||""} ${e.lastName||""}`.trim()||"User"}:null,f=async()=>{try{console.log("CustomerProvider: Checking authentication via /api/auth/me");let e=await fetch("/api/auth/me",{method:"GET",credentials:"include"});console.log("CustomerProvider: Auth API response status:",e.status);let r=await e.json();if(console.log("CustomerProvider: Auth API result:",r),!r.success||!r.customer)return x(null),{success:!1,message:r.message||"Not authenticated"};{let e=r.token;return console.log("CustomerProvider: Token from API response:",!!e),x(e||null),{success:!0,customer:r.customer,token:e}}}catch(e){return console.error("CustomerProvider: Error checking authentication:",e),x(null),{success:!1,message:"Network error"}}},v=async()=>{try{let e=await f();if(e.success){let r={...e.customer,token:e.token};t(g(r)),console.log("Customer data refreshed successfully"),console.log("Token available after refresh:",!!e.token)}else console.log("Failed to refresh customer data:",e.message),t(null),x(null)}catch(e){console.error("Error refreshing customer data:",e),t(null),x(null)}},y=async e=>{d(!0),u(null);try{throw Error("Login temporarily disabled for build fix")}catch(r){let e="Login temporarily disabled for build fix";throw u(e),h(e,"error"),r}finally{d(!1)}},b=async e=>{d(!0),u(null);try{throw Error("Register temporarily disabled for build fix")}catch(r){let e="Register temporarily disabled for build fix";throw u(e),h(e,"error"),r}finally{d(!1)}},k=async e=>{d(!0),u(null);try{throw Error("Profile update temporarily disabled for build fix")}catch(r){let e="Profile update temporarily disabled for build fix";throw u(e),h(e,"error"),r}finally{d(!1)}};return s.jsx(l.Provider,{value:{customer:r,isLoading:i,isAuthenticated:!!r,token:m,login:y,register:b,logout:()=>{t(null),x(null),console.log("Logout completed, token cleared"),h("You have been signed out successfully","info"),p.push("/"),p.refresh()},updateProfile:k,error:c,refreshCustomer:v},children:e})}},75367:(e,r,t)=>{"use strict";t.d(r,{ToastProvider:()=>m,p:()=>x});var s=t(10326),o=t(17577),a=t(92148),n=t(86462),l=t(54659),i=t(87888),d=t(18019),c=t(94019);let u=(0,o.createContext)(void 0);function m({children:e}){let[r,t]=(0,o.useState)([]);return(0,s.jsxs)(u.Provider,{value:{toasts:r,addToast:(e,r="info",s=3e3)=>{let o=Math.random().toString(36).substring(2,9);t(t=>[...t,{id:o,message:e,type:r,duration:s}])},removeToast:e=>{t(r=>r.filter(r=>r.id!==e))}},children:[e,s.jsx(h,{})]})}function x(){let e=(0,o.useContext)(u);if(void 0===e)throw Error("useToast must be used within a ToastProvider");return e}function p({toast:e,onRemove:r}){return(0,s.jsxs)(a.E.div,{initial:{opacity:0,y:-50},animate:{opacity:1,y:0},exit:{opacity:0,x:300},className:`flex items-center p-4 rounded-lg border shadow-lg ${(()=>{switch(e.type){case"success":return"bg-[#f4f3f0] border-[#8a8778]";case"error":return"bg-red-50 border-red-200";default:return"bg-[#f8f8f5] border-[#e5e2d9]"}})()} max-w-md`,children:[s.jsx(()=>{switch(e.type){case"success":return s.jsx(l.Z,{className:"h-5 w-5"});case"error":return s.jsx(i.Z,{className:"h-5 w-5"});default:return s.jsx(d.Z,{className:"h-5 w-5"})}},{}),s.jsx("span",{className:"ml-3 text-sm font-medium flex-1",children:e.message}),s.jsx("button",{onClick:r,className:"ml-4 text-gray-400 hover:text-gray-600",children:s.jsx(c.Z,{className:"h-4 w-4"})})]})}function h(){let{toasts:e,removeToast:r}=x();return s.jsx("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:s.jsx(n.M,{children:e.map(e=>s.jsx(p,{toast:e,onRemove:()=>r(e.id)},e.id))})})}},36663:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\cart-test\page.tsx#default`)},51806:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>m,metadata:()=>u});var s=t(19510),o=t(10527),a=t.n(o),n=t(36822),l=t.n(n);t(5023);var i=t(68570);(0,i.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#useCustomer`);let d=(0,i.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#CustomerProvider`),c=(0,i.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#ToastProvider`);(0,i.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#useToast`);let u={title:"Ankkor | Timeless Menswear",description:"Elevated essentials for the discerning gentleman. Impeccably tailored garments crafted from the finest materials.",keywords:["menswear","luxury clothing","tailored","shirts","accessories"],icons:{icon:[{url:"/logo.PNG",sizes:"32x32",type:"image/png"},{url:"/logo.PNG",sizes:"16x16",type:"image/png"}],shortcut:"/logo.PNG",apple:"/logo.PNG"}};function m({children:e}){return s.jsx("html",{lang:"en",children:s.jsx("body",{className:`${a().variable} ${l().variable} font-sans antialiased min-h-screen bg-[#f8f8f5]`,children:s.jsx(c,{children:s.jsx(d,{children:s.jsx("main",{style:{paddingTop:0},className:"transition-all duration-300",children:e})})})})})}},12523:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\not-found.tsx#default`)},5023:()=>{}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8948,2344,7207,6806],()=>t(5799));module.exports=s})();