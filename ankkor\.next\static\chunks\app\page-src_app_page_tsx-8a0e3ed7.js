"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/page-src_app_page_tsx-8a0e3ed7"],{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Scissors_Shirt_ShoppingBag_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Scissors,Shirt,ShoppingBag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Scissors_Shirt_ShoppingBag_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Scissors,Shirt,ShoppingBag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Scissors_Shirt_ShoppingBag_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Scissors,Shirt,ShoppingBag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shirt.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Scissors_Shirt_ShoppingBag_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Scissors,Shirt,ShoppingBag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scissors.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Scissors_Shirt_ShoppingBag_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Scissors,Shirt,ShoppingBag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_product_ProductCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/product/ProductCard */ \"(app-pages-browser)/./src/components/product/ProductCard.tsx\");\n/* harmony import */ var _hooks_usePageLoading__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/usePageLoading */ \"(app-pages-browser)/./src/hooks/usePageLoading.ts\");\n/* harmony import */ var _lib_woocommerce__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/woocommerce */ \"(app-pages-browser)/./src/lib/woocommerce.ts\");\n/* harmony import */ var _components_home_BannerSlider__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/home/<USER>/ \"(app-pages-browser)/./src/components/home/<USER>");\n/* harmony import */ var _components_home_NewsletterPopup__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/home/<USER>/ \"(app-pages-browser)/./src/components/home/<USER>");\n/* harmony import */ var _components_home_LaunchingSoon__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/home/<USER>/ \"(app-pages-browser)/./src/components/home/<USER>");\n/* harmony import */ var _lib_productUtils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/productUtils */ \"(app-pages-browser)/./src/lib/productUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [featuredProducts, setFeaturedProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch products from WooCommerce\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchProducts = async ()=>{\n            try {\n                console.log(\"Fetching products for homepage...\");\n                const products = await (0,_lib_woocommerce__WEBPACK_IMPORTED_MODULE_6__.getAllProducts)(8); // Fetch 8 products\n                if (!products || products.length === 0) {\n                    console.warn(\"No products returned from WooCommerce\");\n                    setError(\"No products found. Please check your WooCommerce store configuration.\");\n                    setIsLoading(false);\n                    return;\n                }\n                console.log(\"Fetched \".concat(products.length, \" products from WooCommerce\"));\n                // Normalize the products\n                const normalizedProducts = products.map((product)=>{\n                    const normalizedProduct = (0,_lib_woocommerce__WEBPACK_IMPORTED_MODULE_6__.normalizeProduct)(product);\n                    // Ensure currencyCode is included for use with currency symbols\n                    if (normalizedProduct) {\n                        normalizedProduct.currencyCode = \"INR\"; // Default to INR or get from WooCommerce settings\n                    }\n                    return normalizedProduct;\n                }).filter(Boolean);\n                console.log(\"Normalized products:\", normalizedProducts);\n                setFeaturedProducts(normalizedProducts);\n                setIsLoading(false);\n            } catch (err) {\n                console.error(\"Error fetching products:\", err);\n                setError(\"Failed to load products from WooCommerce\");\n                setIsLoading(false);\n            }\n        };\n        fetchProducts();\n    }, []);\n    // Use the page loading hook\n    (0,_hooks_usePageLoading__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(isLoading, \"thread\");\n    // Enhanced animation variants\n    const fadeIn = {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.8,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    const staggerChildren = {\n        animate: {\n            transition: {\n                staggerChildren: 0.15\n            }\n        }\n    };\n    const slideIn = {\n        initial: {\n            opacity: 0,\n            x: -30\n        },\n        animate: {\n            opacity: 1,\n            x: 0,\n            transition: {\n                duration: 0.9,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    const scaleIn = {\n        initial: {\n            opacity: 0,\n            scale: 0.95\n        },\n        animate: {\n            opacity: 1,\n            scale: 1,\n            transition: {\n                duration: 0.9,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    // Fallback products in case WooCommerce API fails\n    const fallbackProducts = [\n        {\n            id: \"prod_1\",\n            title: \"Oxford Dress Shirt\",\n            price: \"4999.00\",\n            images: [\n                {\n                    url: \"https://images.unsplash.com/photo-1598033129183-c4f50c736f10?q=80\"\n                }\n            ],\n            handle: \"oxford-dress-shirt\",\n            metafields: {\n                custom_material: \"Egyptian Cotton\"\n            },\n            isNew: true,\n            variants: [\n                {\n                    id: \"var_1\"\n                }\n            ]\n        },\n        {\n            id: \"prod_2\",\n            title: \"Italian Slim Chinos\",\n            price: \"5999.00\",\n            images: [\n                {\n                    url: \"https://images.unsplash.com/photo-1541099649105-f69ad21f3246?q=80\"\n                }\n            ],\n            handle: \"italian-slim-chinos\",\n            metafields: {\n                custom_material: \"Cotton Twill\"\n            },\n            isNew: true,\n            variants: [\n                {\n                    id: \"var_2\"\n                }\n            ]\n        },\n        {\n            id: \"prod_3\",\n            title: \"Premium Linen Shirt\",\n            price: \"4499.00\",\n            images: [\n                {\n                    url: \"https://images.unsplash.com/photo-1604695573706-53170668f6a6?q=80\"\n                }\n            ],\n            handle: \"premium-linen-shirt\",\n            metafields: {\n                custom_material: \"Irish Linen\"\n            },\n            isNew: true,\n            variants: [\n                {\n                    id: \"var_3\"\n                }\n            ]\n        },\n        {\n            id: \"prod_4\",\n            title: \"Tailored Wool Pants\",\n            price: \"6499.00\",\n            images: [\n                {\n                    url: \"https://images.unsplash.com/photo-1594938298603-c8148c4dae35?q=80\"\n                }\n            ],\n            handle: \"tailored-wool-pants\",\n            metafields: {\n                custom_material: \"Italian Wool\"\n            },\n            isNew: true,\n            variants: [\n                {\n                    id: \"var_4\"\n                }\n            ]\n        }\n    ];\n    // Use real products if available, otherwise use fallback\n    const displayProducts = featuredProducts.length > 0 ? featuredProducts : fallbackProducts;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-[#f8f8f5]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_home_LaunchingSoon__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_home_NewsletterPopup__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_home_BannerSlider__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"pt-36 pb-20 px-4 bg-gradient-to-b from-[#f4f3f0] to-[#f8f8f5]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto flex flex-col lg:flex-row items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                            className: \"lg:w-1/2\",\n                            variants: slideIn,\n                            initial: \"initial\",\n                            animate: \"animate\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-[#8a8778] text-lg mb-5 font-light tracking-widest uppercase\",\n                                    children: \"Timeless Distinction\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl md:text-6xl font-serif font-bold leading-tight mb-8 text-[#2c2c27]\",\n                                    children: [\n                                        \"Elevated essentials \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 35\n                                        }, this),\n                                        \"for the discerning gentleman\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-[#5c5c52] mb-8 leading-relaxed max-w-lg\",\n                                    children: \"Impeccably tailored garments crafted from the finest materials, designed to stand the test of time with understated elegance.\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.button, {\n                                    onClick: ()=>{},\n                                    className: \"bg-[#2c2c27] text-[#f4f3f0] px-10 py-4 hover:bg-[#3d3d35] transition-colors flex items-center gap-3 text-sm tracking-wider uppercase font-medium\",\n                                    whileHover: {\n                                        scale: 1.02\n                                    },\n                                    whileTap: {\n                                        scale: 0.98\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/collection\",\n                                        children: [\n                                            \"Explore Collection\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-block text-lg font-light\",\n                                                children: \"→\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                            className: \"lg:w-1/2 mt-12 lg:mt-0 relative\",\n                            variants: scaleIn,\n                            initial: \"initial\",\n                            animate: \"animate\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -z-10 top-0 right-0 w-80 h-80 bg-[#e0ddd3] rounded-full opacity-40 blur-3xl\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -z-10 bottom-0 right-20 w-64 h-64 bg-[#d5d0c3] rounded-full opacity-30 blur-3xl\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    src: \"/hero.jpg\",\n                                    alt: \"Ankkor Classic Style\",\n                                    width: 600,\n                                    height: 800,\n                                    className: \"rounded-sm shadow-lg relative z-10 image-animate border border-[#e5e2d9]\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -bottom-6 -left-6 bg-[#2c2c27] text-[#f4f3f0] py-4 px-8 text-sm tracking-wider uppercase z-20 hidden md:block\",\n                                    children: \"Est. 2025\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 152,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.section, {\n                className: \"py-24 px-4\",\n                initial: \"initial\",\n                whileInView: \"animate\",\n                viewport: {\n                    once: true\n                },\n                variants: staggerChildren,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-serif font-bold mb-5 text-[#2c2c27]\",\n                                    children: \"Signature Pieces\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-[#5c5c52] max-w-2xl mx-auto\",\n                                    children: \"Our most distinguished garments, selected for their exceptional quality and timeless appeal\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 text-red-700 p-4 mb-8 rounded\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm mt-2\",\n                                    children: \"Please check your WooCommerce configuration in the .env.local file.\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8\",\n                            children: displayProducts.map((product)=>{\n                                var _product_priceRange_minVariantPrice, _product_priceRange, _product_images_;\n                                const originalProduct = product._originalWooProduct;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_ProductCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    id: product.id,\n                                    name: product.title,\n                                    price: (originalProduct === null || originalProduct === void 0 ? void 0 : originalProduct.salePrice) || (originalProduct === null || originalProduct === void 0 ? void 0 : originalProduct.price) || ((_product_priceRange = product.priceRange) === null || _product_priceRange === void 0 ? void 0 : (_product_priceRange_minVariantPrice = _product_priceRange.minVariantPrice) === null || _product_priceRange_minVariantPrice === void 0 ? void 0 : _product_priceRange_minVariantPrice.amount) || \"0\",\n                                    image: ((_product_images_ = product.images[0]) === null || _product_images_ === void 0 ? void 0 : _product_images_.url) || \"\",\n                                    slug: product.handle,\n                                    material: (0,_lib_woocommerce__WEBPACK_IMPORTED_MODULE_6__.getMetafield)(product, \"custom_material\", undefined, product.vendor || \"Premium Fabric\"),\n                                    isNew: true,\n                                    stockStatus: (originalProduct === null || originalProduct === void 0 ? void 0 : originalProduct.stockStatus) || \"IN_STOCK\",\n                                    compareAtPrice: product.compareAtPrice,\n                                    regularPrice: originalProduct === null || originalProduct === void 0 ? void 0 : originalProduct.regularPrice,\n                                    salePrice: originalProduct === null || originalProduct === void 0 ? void 0 : originalProduct.salePrice,\n                                    onSale: (originalProduct === null || originalProduct === void 0 ? void 0 : originalProduct.onSale) || false,\n                                    currencySymbol: (0,_lib_productUtils__WEBPACK_IMPORTED_MODULE_10__.getCurrencySymbol)(product.currencyCode),\n                                    currencyCode: product.currencyCode || \"INR\",\n                                    shortDescription: originalProduct === null || originalProduct === void 0 ? void 0 : originalProduct.shortDescription,\n                                    type: originalProduct === null || originalProduct === void 0 ? void 0 : originalProduct.type\n                                }, product.id, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mt-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/collection\",\n                                className: \"inline-flex items-center text-[#2c2c27] hover:text-[#8a8778] transition-colors\",\n                                children: [\n                                    \"View Full Collection\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Scissors_Shirt_ShoppingBag_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"ml-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.section, {\n                className: \"py-24 px-4 bg-[#f4f3f0]\",\n                initial: \"initial\",\n                whileInView: \"animate\",\n                viewport: {\n                    once: true\n                },\n                variants: staggerChildren,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto max-w-6xl flex flex-col lg:flex-row items-center gap-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                            className: \"lg:w-1/2 relative\",\n                            variants: fadeIn,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -inset-4 border border-[#8a8778] -z-10 opacity-40\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    src: \"https://images.unsplash.com/photo-1596755094514-f87e34085b2c?q=80&w=2944&auto=format&fit=crop\",\n                                    alt: \"Ankkor Premium Shirt\",\n                                    width: 600,\n                                    height: 600,\n                                    sizes: \"(max-width: 768px) 100vw, 600px\",\n                                    className: \"w-full h-[600px] object-cover image-animate border border-[#e5e2d9] transition-all duration-700\",\n                                    priority: true\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -bottom-6 -right-6 bg-[#2c2c27] text-[#f4f3f0] py-3 px-6 text-xs tracking-widest uppercase\",\n                                    children: \"Est. 2025\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                            className: \"lg:w-1/2 space-y-8\",\n                            variants: fadeIn,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-px w-12 bg-[#8a8778]\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-[#8a8778] text-sm tracking-widest uppercase\",\n                                            children: \"Our Heritage\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-serif font-bold text-[#2c2c27] mb-8\",\n                                    children: \"ANKKOR – The Anchor of Timeless Style\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6 text-[#5c5c52] leading-relaxed\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Founded in 2025, ANKKOR emerged with a vision: to revive the elegance of classic, old money formals and make them a part of everyday wear. In a world of fleeting fashion, ANKKOR stands still—rooted in sophistication, purpose, and grace.\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-px w-full bg-[#e5e2d9]\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"The name ANKKOR draws strength from the anchor—symbolizing durability, stability, and timeless resilience. Just like an anchor holds firm amidst shifting tides, our brand is grounded in the belief that true style never fades. This philosophy carries through in every stitch of our garments—crafted with high-quality threads that mirror the strength and integrity of the symbol we stand by.\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Our designs speak the language of quiet luxury: clean cuts, refined fabrics, and enduring silhouettes. ANKKOR is for those who appreciate the subtle power of understated elegance and the confidence it brings.\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Every piece we create is a commitment—to strength, to style, and to the timeless values of classic fashion.\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-serif text-xl text-[#2c2c27] italic\",\n                                            children: \"ANKKOR – Where class meets character. Wear timeless. Be anchored.\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-10\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/about\",\n                                                className: \"inline-flex items-center px-6 py-3 text-sm font-medium text-[#2c2c27] border border-[#2c2c27] hover:bg-[#2c2c27] hover:text-white transition-colors\",\n                                                children: [\n                                                    \"Read more\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        fill: \"none\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        height: \"15px\",\n                                                        width: \"15px\",\n                                                        className: \"ml-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinejoin: \"round\",\n                                                            strokeLinecap: \"round\",\n                                                            strokeMiterlimit: 10,\n                                                            strokeWidth: \"1.5\",\n                                                            stroke: \"currentColor\",\n                                                            d: \"M8.91016 19.9201L15.4302 13.4001C16.2002 12.6301 16.2002 11.3701 15.4302 10.6001L8.91016 4.08008\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 315,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 308,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.section, {\n                className: \"py-24 px-4 bg-[#f0ede6]\",\n                initial: \"initial\",\n                whileInView: \"animate\",\n                viewport: {\n                    once: true\n                },\n                variants: staggerChildren,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto max-w-6xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                            className: \"text-center max-w-xl mx-auto mb-16\",\n                            variants: fadeIn,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-[#8a8778] text-sm mb-3 tracking-widest uppercase\",\n                                    children: \"Our Promise\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-serif font-bold mb-4 text-[#2c2c27]\",\n                                    children: \"The Ankkor Distinction\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-[#5c5c52]\",\n                                    children: \"We uphold the highest standards in every aspect of our craft, ensuring an exceptional experience with every garment\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 432,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 427,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                    className: \"text-center space-y-5\",\n                                    variants: fadeIn,\n                                    whileHover: {\n                                        y: -5\n                                    },\n                                    transition: {\n                                        duration: 0.3\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-[#faf9f6] w-20 h-20 mx-auto rounded-none flex items-center justify-center shadow-sm border border-[#e5e2d9]\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Scissors_Shirt_ShoppingBag_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-8 h-8 text-[#2c2c27]\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-serif font-semibold text-lg text-[#2c2c27]\",\n                                            children: \"Curated Selection\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-[#5c5c52] text-sm\",\n                                            children: \"Each piece is meticulously selected to ensure exceptional quality and enduring style\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                    className: \"text-center space-y-5\",\n                                    variants: fadeIn,\n                                    whileHover: {\n                                        y: -5\n                                    },\n                                    transition: {\n                                        duration: 0.3\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-[#faf9f6] w-20 h-20 mx-auto rounded-none flex items-center justify-center shadow-sm border border-[#e5e2d9]\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Scissors_Shirt_ShoppingBag_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-8 h-8 text-[#2c2c27]\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-serif font-semibold text-lg text-[#2c2c27]\",\n                                            children: \"Master Tailoring\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 463,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-[#5c5c52] text-sm\",\n                                            children: \"Precision craftsmanship ensuring impeccable fit, superior comfort, and distinctive character\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 454,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                    className: \"text-center space-y-5\",\n                                    variants: fadeIn,\n                                    whileHover: {\n                                        y: -5\n                                    },\n                                    transition: {\n                                        duration: 0.3\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-[#faf9f6] w-20 h-20 mx-auto rounded-none flex items-center justify-center shadow-sm border border-[#e5e2d9]\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Scissors_Shirt_ShoppingBag_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-8 h-8 text-[#2c2c27]\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-serif font-semibold text-lg text-[#2c2c27]\",\n                                            children: \"Exceptional Materials\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-[#5c5c52] text-sm\",\n                                            children: \"Sourced from heritage mills with centuries of tradition and uncompromising standards\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 479,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 469,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                    className: \"text-center space-y-5\",\n                                    variants: fadeIn,\n                                    whileHover: {\n                                        y: -5\n                                    },\n                                    transition: {\n                                        duration: 0.3\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-[#faf9f6] w-20 h-20 mx-auto rounded-none flex items-center justify-center shadow-sm border border-[#e5e2d9]\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Scissors_Shirt_ShoppingBag_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-8 h-8 text-[#2c2c27]\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 490,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-serif font-semibold text-lg text-[#2c2c27]\",\n                                            children: \"Client Dedication\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 493,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-[#5c5c52] text-sm\",\n                                            children: \"Personalized attention and service that honors the tradition of bespoke craftsmanship\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 438,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 426,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 419,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.section, {\n                className: \"py-24 px-4 bg-[#2c2c27] text-[#f4f3f0] relative overflow-hidden\",\n                initial: {\n                    opacity: 0\n                },\n                whileInView: {\n                    opacity: 1\n                },\n                viewport: {\n                    once: true\n                },\n                transition: {\n                    duration: 0.8\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-[#8a8778] to-transparent opacity-40\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 510,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto max-w-4xl text-center relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl font-serif font-bold mb-6\",\n                                children: \"Experience Ankkor\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 512,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-[#d5d0c3] mb-10 max-w-2xl mx-auto\",\n                                children: \"Visit our flagship boutique for a personalized styling consultation with our master tailors, or explore our collection online.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 513,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/collection\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.button, {\n                                    className: \"border border-[#8a8778] text-[#f4f3f0] px-10 py-4 hover:bg-[#3d3d35] transition-colors text-sm tracking-wider uppercase font-medium\",\n                                    whileHover: {\n                                        scale: 1.02\n                                    },\n                                    whileTap: {\n                                        scale: 0.98\n                                    },\n                                    children: \"Shop the Collection\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 518,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 517,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 511,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-0 right-0 w-full h-px bg-gradient-to-r from-transparent via-[#8a8778] to-transparent opacity-40\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 527,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 503,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 141,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"16Idl6PK0JKYVPWt1SVhtOApV+U=\", false, function() {\n    return [\n        _hooks_usePageLoading__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

}]);