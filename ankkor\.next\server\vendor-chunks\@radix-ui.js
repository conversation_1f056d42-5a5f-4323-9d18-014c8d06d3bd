"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui";
exports.ids = ["vendor-chunks/@radix-ui"];
exports.modules = {

/***/ "(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@radix-ui/primitive/dist/index.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeEventHandlers: () => (/* binding */ composeEventHandlers)\n/* harmony export */ });\n// packages/core/primitive/src/primitive.tsx\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {\n  return function handleEvent(event) {\n    originalEventHandler?.(event);\n    if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3ByaW1pdGl2ZS9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSx1RUFBdUUsa0NBQWtDLElBQUk7QUFDN0c7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW5ra29yLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9wcmltaXRpdmUvZGlzdC9pbmRleC5tanM/MzQ1NSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9jb3JlL3ByaW1pdGl2ZS9zcmMvcHJpbWl0aXZlLnRzeFxuZnVuY3Rpb24gY29tcG9zZUV2ZW50SGFuZGxlcnMob3JpZ2luYWxFdmVudEhhbmRsZXIsIG91ckV2ZW50SGFuZGxlciwgeyBjaGVja0ZvckRlZmF1bHRQcmV2ZW50ZWQgPSB0cnVlIH0gPSB7fSkge1xuICByZXR1cm4gZnVuY3Rpb24gaGFuZGxlRXZlbnQoZXZlbnQpIHtcbiAgICBvcmlnaW5hbEV2ZW50SGFuZGxlcj8uKGV2ZW50KTtcbiAgICBpZiAoY2hlY2tGb3JEZWZhdWx0UHJldmVudGVkID09PSBmYWxzZSB8fCAhZXZlbnQuZGVmYXVsdFByZXZlbnRlZCkge1xuICAgICAgcmV0dXJuIG91ckV2ZW50SGFuZGxlcj8uKGV2ZW50KTtcbiAgICB9XG4gIH07XG59XG5leHBvcnQge1xuICBjb21wb3NlRXZlbnRIYW5kbGVyc1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-compose-refs/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeRefs: () => (/* binding */ composeRefs),\n/* harmony export */   useComposedRefs: () => (/* binding */ useComposedRefs)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/compose-refs/src/composeRefs.tsx\n\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    return ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == \"function\") {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == \"function\") {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\nfunction useComposedRefs(...refs) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(composeRefs(...refs), refs);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@radix-ui/react-context/dist/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createContext: () => (/* binding */ createContext2),\n/* harmony export */   createContextScope: () => (/* binding */ createContextScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/context/src/createContext.tsx\n\n\nfunction createContext2(rootComponentName, defaultContext) {\n  const Context = react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n  const Provider = (props) => {\n    const { children, ...context } = props;\n    const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => context, Object.values(context));\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value, children });\n  };\n  Provider.displayName = rootComponentName + \"Provider\";\n  function useContext2(consumerName) {\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== void 0) return defaultContext;\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n  return [Provider, useContext2];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n  let defaultContexts = [];\n  function createContext3(rootComponentName, defaultContext) {\n    const BaseContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n    const Provider = (props) => {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => context, Object.values(context));\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value, children });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName, scope) {\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== void 0) return defaultContext;\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [Provider, useContext2];\n  }\n  const createScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    });\n    return function useScope(scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n  createScope.scopeName = scopeName;\n  return [createContext3, composeContextScopes(createScope, ...createContextScopeDeps)];\n}\nfunction composeContextScopes(...scopes) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n  const createScope = () => {\n    const scopeHooks = scopes.map((createScope2) => ({\n      useScope: createScope2(),\n      scopeName: createScope2.scopeName\n    }));\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName }) => {\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes2, ...currentScope };\n      }, {});\n      return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-dialog/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@radix-ui/react-dialog/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Close: () => (/* binding */ Close),\n/* harmony export */   Content: () => (/* binding */ Content),\n/* harmony export */   Description: () => (/* binding */ Description),\n/* harmony export */   Dialog: () => (/* binding */ Dialog),\n/* harmony export */   DialogClose: () => (/* binding */ DialogClose),\n/* harmony export */   DialogContent: () => (/* binding */ DialogContent),\n/* harmony export */   DialogDescription: () => (/* binding */ DialogDescription),\n/* harmony export */   DialogOverlay: () => (/* binding */ DialogOverlay),\n/* harmony export */   DialogPortal: () => (/* binding */ DialogPortal),\n/* harmony export */   DialogTitle: () => (/* binding */ DialogTitle),\n/* harmony export */   DialogTrigger: () => (/* binding */ DialogTrigger),\n/* harmony export */   Overlay: () => (/* binding */ Overlay),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Title: () => (/* binding */ Title),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   WarningProvider: () => (/* binding */ WarningProvider),\n/* harmony export */   createDialogScope: () => (/* binding */ createDialogScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @radix-ui/react-focus-scope */ \"(ssr)/./node_modules/@radix-ui/react-focus-scope/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-focus-guards */ \"(ssr)/./node_modules/@radix-ui/react-focus-guards/dist/index.mjs\");\n/* harmony import */ var react_remove_scroll__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-remove-scroll */ \"(ssr)/./node_modules/react-remove-scroll/dist/es2015/Combination.js\");\n/* harmony import */ var aria_hidden__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! aria-hidden */ \"(ssr)/./node_modules/aria-hidden/dist/es2015/index.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Close,Content,Description,Dialog,DialogClose,DialogContent,DialogDescription,DialogOverlay,DialogPortal,DialogTitle,DialogTrigger,Overlay,Portal,Root,Title,Trigger,WarningProvider,createDialogScope auto */ // packages/react/dialog/src/dialog.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar DIALOG_NAME = \"Dialog\";\nvar [createDialogContext, createDialogScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(DIALOG_NAME);\nvar [DialogProvider, useDialogContext] = createDialogContext(DIALOG_NAME);\nvar Dialog = (props)=>{\n    const { __scopeDialog, children, open: openProp, defaultOpen, onOpenChange, modal = true } = props;\n    const triggerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const [open = false, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen,\n        onChange: onOpenChange\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogProvider, {\n        scope: __scopeDialog,\n        triggerRef,\n        contentRef,\n        contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)(),\n        titleId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)(),\n        descriptionId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)(),\n        open,\n        onOpenChange: setOpen,\n        onOpenToggle: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>setOpen((prevOpen)=>!prevOpen), [\n            setOpen\n        ]),\n        modal,\n        children\n    });\n};\nDialog.displayName = DIALOG_NAME;\nvar TRIGGER_NAME = \"DialogTrigger\";\nvar DialogTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...triggerProps } = props;\n    const context = useDialogContext(TRIGGER_NAME, __scopeDialog);\n    const composedTriggerRef = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, context.triggerRef);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.button, {\n        type: \"button\",\n        \"aria-haspopup\": \"dialog\",\n        \"aria-expanded\": context.open,\n        \"aria-controls\": context.contentId,\n        \"data-state\": getState(context.open),\n        ...triggerProps,\n        ref: composedTriggerRef,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onClick, context.onOpenToggle)\n    });\n});\nDialogTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"DialogPortal\";\nvar [PortalProvider, usePortalContext] = createDialogContext(PORTAL_NAME, {\n    forceMount: void 0\n});\nvar DialogPortal = (props)=>{\n    const { __scopeDialog, forceMount, children, container } = props;\n    const context = useDialogContext(PORTAL_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PortalProvider, {\n        scope: __scopeDialog,\n        forceMount,\n        children: react__WEBPACK_IMPORTED_MODULE_0__.Children.map(children, (child)=>/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n                present: forceMount || context.open,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_9__.Portal, {\n                    asChild: true,\n                    container,\n                    children: child\n                })\n            }))\n    });\n};\nDialogPortal.displayName = PORTAL_NAME;\nvar OVERLAY_NAME = \"DialogOverlay\";\nvar DialogOverlay = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(OVERLAY_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, props.__scopeDialog);\n    return context.modal ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n        present: forceMount || context.open,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogOverlayImpl, {\n            ...overlayProps,\n            ref: forwardedRef\n        })\n    }) : null;\n});\nDialogOverlay.displayName = OVERLAY_NAME;\nvar DialogOverlayImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, __scopeDialog);\n    return(// Make sure `Content` is scrollable even when it doesn't live inside `RemoveScroll`\n    // ie. when `Overlay` and `Content` are siblings\n    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_remove_scroll__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n        as: _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__.Slot,\n        allowPinchZoom: true,\n        shards: [\n            context.contentRef\n        ],\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.div, {\n            \"data-state\": getState(context.open),\n            ...overlayProps,\n            ref: forwardedRef,\n            style: {\n                pointerEvents: \"auto\",\n                ...overlayProps.style\n            }\n        })\n    }));\n});\nvar CONTENT_NAME = \"DialogContent\";\nvar DialogContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n        present: forceMount || context.open,\n        children: context.modal ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentModal, {\n            ...contentProps,\n            ref: forwardedRef\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentNonModal, {\n            ...contentProps,\n            ref: forwardedRef\n        })\n    });\n});\nDialogContent.displayName = CONTENT_NAME;\nvar DialogContentModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, context.contentRef, contentRef);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const content = contentRef.current;\n        if (content) return (0,aria_hidden__WEBPACK_IMPORTED_MODULE_12__.hideOthers)(content);\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentImpl, {\n        ...props,\n        ref: composedRefs,\n        trapFocus: context.open,\n        disableOutsidePointerEvents: true,\n        onCloseAutoFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onCloseAutoFocus, (event)=>{\n            event.preventDefault();\n            context.triggerRef.current?.focus();\n        }),\n        onPointerDownOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerDownOutside, (event)=>{\n            const originalEvent = event.detail.originalEvent;\n            const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n            const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n            if (isRightClick) event.preventDefault();\n        }),\n        onFocusOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onFocusOutside, (event)=>event.preventDefault())\n    });\n});\nvar DialogContentNonModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const hasInteractedOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const hasPointerDownOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentImpl, {\n        ...props,\n        ref: forwardedRef,\n        trapFocus: false,\n        disableOutsidePointerEvents: false,\n        onCloseAutoFocus: (event)=>{\n            props.onCloseAutoFocus?.(event);\n            if (!event.defaultPrevented) {\n                if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n                event.preventDefault();\n            }\n            hasInteractedOutsideRef.current = false;\n            hasPointerDownOutsideRef.current = false;\n        },\n        onInteractOutside: (event)=>{\n            props.onInteractOutside?.(event);\n            if (!event.defaultPrevented) {\n                hasInteractedOutsideRef.current = true;\n                if (event.detail.originalEvent.type === \"pointerdown\") {\n                    hasPointerDownOutsideRef.current = true;\n                }\n            }\n            const target = event.target;\n            const targetIsTrigger = context.triggerRef.current?.contains(target);\n            if (targetIsTrigger) event.preventDefault();\n            if (event.detail.originalEvent.type === \"focusin\" && hasPointerDownOutsideRef.current) {\n                event.preventDefault();\n            }\n        }\n    });\n});\nvar DialogContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, trapFocus, onOpenAutoFocus, onCloseAutoFocus, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, __scopeDialog);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, contentRef);\n    (0,_radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_13__.useFocusGuards)();\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_14__.FocusScope, {\n                asChild: true,\n                loop: true,\n                trapped: trapFocus,\n                onMountAutoFocus: onOpenAutoFocus,\n                onUnmountAutoFocus: onCloseAutoFocus,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_15__.DismissableLayer, {\n                    role: \"dialog\",\n                    id: context.contentId,\n                    \"aria-describedby\": context.descriptionId,\n                    \"aria-labelledby\": context.titleId,\n                    \"data-state\": getState(context.open),\n                    ...contentProps,\n                    ref: composedRefs,\n                    onDismiss: ()=>context.onOpenChange(false)\n                })\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n                children: [\n                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TitleWarning, {\n                        titleId: context.titleId\n                    }),\n                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DescriptionWarning, {\n                        contentRef,\n                        descriptionId: context.descriptionId\n                    })\n                ]\n            })\n        ]\n    });\n});\nvar TITLE_NAME = \"DialogTitle\";\nvar DialogTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...titleProps } = props;\n    const context = useDialogContext(TITLE_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.h2, {\n        id: context.titleId,\n        ...titleProps,\n        ref: forwardedRef\n    });\n});\nDialogTitle.displayName = TITLE_NAME;\nvar DESCRIPTION_NAME = \"DialogDescription\";\nvar DialogDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...descriptionProps } = props;\n    const context = useDialogContext(DESCRIPTION_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.p, {\n        id: context.descriptionId,\n        ...descriptionProps,\n        ref: forwardedRef\n    });\n});\nDialogDescription.displayName = DESCRIPTION_NAME;\nvar CLOSE_NAME = \"DialogClose\";\nvar DialogClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...closeProps } = props;\n    const context = useDialogContext(CLOSE_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.button, {\n        type: \"button\",\n        ...closeProps,\n        ref: forwardedRef,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onClick, ()=>context.onOpenChange(false))\n    });\n});\nDialogClose.displayName = CLOSE_NAME;\nfunction getState(open) {\n    return open ? \"open\" : \"closed\";\n}\nvar TITLE_WARNING_NAME = \"DialogTitleWarning\";\nvar [WarningProvider, useWarningContext] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContext)(TITLE_WARNING_NAME, {\n    contentName: CONTENT_NAME,\n    titleName: TITLE_NAME,\n    docsSlug: \"dialog\"\n});\nvar TitleWarning = ({ titleId })=>{\n    const titleWarningContext = useWarningContext(TITLE_WARNING_NAME);\n    const MESSAGE = `\\`${titleWarningContext.contentName}\\` requires a \\`${titleWarningContext.titleName}\\` for the component to be accessible for screen reader users.\n\nIf you want to hide the \\`${titleWarningContext.titleName}\\`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/${titleWarningContext.docsSlug}`;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (titleId) {\n            const hasTitle = document.getElementById(titleId);\n            if (!hasTitle) console.error(MESSAGE);\n        }\n    }, [\n        MESSAGE,\n        titleId\n    ]);\n    return null;\n};\nvar DESCRIPTION_WARNING_NAME = \"DialogDescriptionWarning\";\nvar DescriptionWarning = ({ contentRef, descriptionId })=>{\n    const descriptionWarningContext = useWarningContext(DESCRIPTION_WARNING_NAME);\n    const MESSAGE = `Warning: Missing \\`Description\\` or \\`aria-describedby={undefined}\\` for {${descriptionWarningContext.contentName}}.`;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const describedById = contentRef.current?.getAttribute(\"aria-describedby\");\n        if (descriptionId && describedById) {\n            const hasDescription = document.getElementById(descriptionId);\n            if (!hasDescription) console.warn(MESSAGE);\n        }\n    }, [\n        MESSAGE,\n        contentRef,\n        descriptionId\n    ]);\n    return null;\n};\nvar Root = Dialog;\nvar Trigger = DialogTrigger;\nvar Portal = DialogPortal;\nvar Overlay = DialogOverlay;\nvar Content = DialogContent;\nvar Title = DialogTitle;\nvar Description = DialogDescription;\nvar Close = DialogClose;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-dialog/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Branch: () => (/* binding */ Branch),\n/* harmony export */   DismissableLayer: () => (/* binding */ DismissableLayer),\n/* harmony export */   DismissableLayerBranch: () => (/* binding */ DismissableLayerBranch),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-escape-keydown */ \"(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Branch,DismissableLayer,DismissableLayerBranch,Root auto */ // packages/react/dismissable-layer/src/dismissable-layer.tsx\n\n\n\n\n\n\n\nvar DISMISSABLE_LAYER_NAME = \"DismissableLayer\";\nvar CONTEXT_UPDATE = \"dismissableLayer.update\";\nvar POINTER_DOWN_OUTSIDE = \"dismissableLayer.pointerDownOutside\";\nvar FOCUS_OUTSIDE = \"dismissableLayer.focusOutside\";\nvar originalBodyPointerEvents;\nvar DismissableLayerContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext({\n    layers: /* @__PURE__ */ new Set(),\n    layersWithOutsidePointerEventsDisabled: /* @__PURE__ */ new Set(),\n    branches: /* @__PURE__ */ new Set()\n});\nvar DismissableLayer = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { disableOutsidePointerEvents = false, onEscapeKeyDown, onPointerDownOutside, onFocusOutside, onInteractOutside, onDismiss, ...layerProps } = props;\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const ownerDocument = node?.ownerDocument ?? globalThis?.document;\n    const [, force] = react__WEBPACK_IMPORTED_MODULE_0__.useState({});\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, (node2)=>setNode(node2));\n    const layers = Array.from(context.layers);\n    const [highestLayerWithOutsidePointerEventsDisabled] = [\n        ...context.layersWithOutsidePointerEventsDisabled\n    ].slice(-1);\n    const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled);\n    const index = node ? layers.indexOf(node) : -1;\n    const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n    const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n    const pointerDownOutside = usePointerDownOutside((event)=>{\n        const target = event.target;\n        const isPointerDownOnBranch = [\n            ...context.branches\n        ].some((branch)=>branch.contains(target));\n        if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n        onPointerDownOutside?.(event);\n        onInteractOutside?.(event);\n        if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    const focusOutside = useFocusOutside((event)=>{\n        const target = event.target;\n        const isFocusInBranch = [\n            ...context.branches\n        ].some((branch)=>branch.contains(target));\n        if (isFocusInBranch) return;\n        onFocusOutside?.(event);\n        onInteractOutside?.(event);\n        if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    (0,_radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__.useEscapeKeydown)((event)=>{\n        const isHighestLayer = index === context.layers.size - 1;\n        if (!isHighestLayer) return;\n        onEscapeKeyDown?.(event);\n        if (!event.defaultPrevented && onDismiss) {\n            event.preventDefault();\n            onDismiss();\n        }\n    }, ownerDocument);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (!node) return;\n        if (disableOutsidePointerEvents) {\n            if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n                originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;\n                ownerDocument.body.style.pointerEvents = \"none\";\n            }\n            context.layersWithOutsidePointerEventsDisabled.add(node);\n        }\n        context.layers.add(node);\n        dispatchUpdate();\n        return ()=>{\n            if (disableOutsidePointerEvents && context.layersWithOutsidePointerEventsDisabled.size === 1) {\n                ownerDocument.body.style.pointerEvents = originalBodyPointerEvents;\n            }\n        };\n    }, [\n        node,\n        ownerDocument,\n        disableOutsidePointerEvents,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>{\n            if (!node) return;\n            context.layers.delete(node);\n            context.layersWithOutsidePointerEventsDisabled.delete(node);\n            dispatchUpdate();\n        };\n    }, [\n        node,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleUpdate = ()=>force({});\n        document.addEventListener(CONTEXT_UPDATE, handleUpdate);\n        return ()=>document.removeEventListener(CONTEXT_UPDATE, handleUpdate);\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...layerProps,\n        ref: composedRefs,\n        style: {\n            pointerEvents: isBodyPointerEventsDisabled ? isPointerEventsEnabled ? \"auto\" : \"none\" : void 0,\n            ...props.style\n        },\n        onFocusCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onFocusCapture, focusOutside.onFocusCapture),\n        onBlurCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onBlurCapture, focusOutside.onBlurCapture),\n        onPointerDownCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onPointerDownCapture, pointerDownOutside.onPointerDownCapture)\n    });\n});\nDismissableLayer.displayName = DISMISSABLE_LAYER_NAME;\nvar BRANCH_NAME = \"DismissableLayerBranch\";\nvar DismissableLayerBranch = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const node = ref.current;\n        if (node) {\n            context.branches.add(node);\n            return ()=>{\n                context.branches.delete(node);\n            };\n        }\n    }, [\n        context.branches\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...props,\n        ref: composedRefs\n    });\n});\nDismissableLayerBranch.displayName = BRANCH_NAME;\nfunction usePointerDownOutside(onPointerDownOutside, ownerDocument = globalThis?.document) {\n    const handlePointerDownOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onPointerDownOutside);\n    const isPointerInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handleClickRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(()=>{});\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handlePointerDown = (event)=>{\n            if (event.target && !isPointerInsideReactTreeRef.current) {\n                let handleAndDispatchPointerDownOutsideEvent2 = function() {\n                    handleAndDispatchCustomEvent(POINTER_DOWN_OUTSIDE, handlePointerDownOutside, eventDetail, {\n                        discrete: true\n                    });\n                };\n                var handleAndDispatchPointerDownOutsideEvent = handleAndDispatchPointerDownOutsideEvent2;\n                const eventDetail = {\n                    originalEvent: event\n                };\n                if (event.pointerType === \"touch\") {\n                    ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n                    handleClickRef.current = handleAndDispatchPointerDownOutsideEvent2;\n                    ownerDocument.addEventListener(\"click\", handleClickRef.current, {\n                        once: true\n                    });\n                } else {\n                    handleAndDispatchPointerDownOutsideEvent2();\n                }\n            } else {\n                ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n            }\n            isPointerInsideReactTreeRef.current = false;\n        };\n        const timerId = window.setTimeout(()=>{\n            ownerDocument.addEventListener(\"pointerdown\", handlePointerDown);\n        }, 0);\n        return ()=>{\n            window.clearTimeout(timerId);\n            ownerDocument.removeEventListener(\"pointerdown\", handlePointerDown);\n            ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n        };\n    }, [\n        ownerDocument,\n        handlePointerDownOutside\n    ]);\n    return {\n        // ensures we check React component tree (not just DOM tree)\n        onPointerDownCapture: ()=>isPointerInsideReactTreeRef.current = true\n    };\n}\nfunction useFocusOutside(onFocusOutside, ownerDocument = globalThis?.document) {\n    const handleFocusOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onFocusOutside);\n    const isFocusInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleFocus = (event)=>{\n            if (event.target && !isFocusInsideReactTreeRef.current) {\n                const eventDetail = {\n                    originalEvent: event\n                };\n                handleAndDispatchCustomEvent(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n                    discrete: false\n                });\n            }\n        };\n        ownerDocument.addEventListener(\"focusin\", handleFocus);\n        return ()=>ownerDocument.removeEventListener(\"focusin\", handleFocus);\n    }, [\n        ownerDocument,\n        handleFocusOutside\n    ]);\n    return {\n        onFocusCapture: ()=>isFocusInsideReactTreeRef.current = true,\n        onBlurCapture: ()=>isFocusInsideReactTreeRef.current = false\n    };\n}\nfunction dispatchUpdate() {\n    const event = new CustomEvent(CONTEXT_UPDATE);\n    document.dispatchEvent(event);\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n    const target = detail.originalEvent.target;\n    const event = new CustomEvent(name, {\n        bubbles: false,\n        cancelable: true,\n        detail\n    });\n    if (handler) target.addEventListener(name, handler, {\n        once: true\n    });\n    if (discrete) {\n        (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.dispatchDiscreteCustomEvent)(target, event);\n    } else {\n        target.dispatchEvent(event);\n    }\n}\nvar Root = DismissableLayer;\nvar Branch = DismissableLayerBranch;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-focus-guards/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-focus-guards/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusGuards: () => (/* binding */ FocusGuards),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   useFocusGuards: () => (/* binding */ useFocusGuards)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ FocusGuards,Root,useFocusGuards auto */ // packages/react/focus-guards/src/FocusGuards.tsx\n\nvar count = 0;\nfunction FocusGuards(props) {\n    useFocusGuards();\n    return props.children;\n}\nfunction useFocusGuards() {\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const edgeGuards = document.querySelectorAll(\"[data-radix-focus-guard]\");\n        document.body.insertAdjacentElement(\"afterbegin\", edgeGuards[0] ?? createFocusGuard());\n        document.body.insertAdjacentElement(\"beforeend\", edgeGuards[1] ?? createFocusGuard());\n        count++;\n        return ()=>{\n            if (count === 1) {\n                document.querySelectorAll(\"[data-radix-focus-guard]\").forEach((node)=>node.remove());\n            }\n            count--;\n        };\n    }, []);\n}\nfunction createFocusGuard() {\n    const element = document.createElement(\"span\");\n    element.setAttribute(\"data-radix-focus-guard\", \"\");\n    element.tabIndex = 0;\n    element.style.outline = \"none\";\n    element.style.opacity = \"0\";\n    element.style.position = \"fixed\";\n    element.style.pointerEvents = \"none\";\n    return element;\n}\nvar Root = FocusGuards;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-focus-guards/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-focus-scope/dist/index.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/@radix-ui/react-focus-scope/dist/index.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusScope: () => (/* binding */ FocusScope),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ FocusScope,Root auto */ // packages/react/focus-scope/src/focus-scope.tsx\n\n\n\n\n\nvar AUTOFOCUS_ON_MOUNT = \"focusScope.autoFocusOnMount\";\nvar AUTOFOCUS_ON_UNMOUNT = \"focusScope.autoFocusOnUnmount\";\nvar EVENT_OPTIONS = {\n    bubbles: false,\n    cancelable: true\n};\nvar FOCUS_SCOPE_NAME = \"FocusScope\";\nvar FocusScope = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { loop = false, trapped = false, onMountAutoFocus: onMountAutoFocusProp, onUnmountAutoFocus: onUnmountAutoFocusProp, ...scopeProps } = props;\n    const [container, setContainer] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const onMountAutoFocus = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__.useCallbackRef)(onMountAutoFocusProp);\n    const onUnmountAutoFocus = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__.useCallbackRef)(onUnmountAutoFocusProp);\n    const lastFocusedElementRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node)=>setContainer(node));\n    const focusScope = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n        paused: false,\n        pause () {\n            this.paused = true;\n        },\n        resume () {\n            this.paused = false;\n        }\n    }).current;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (trapped) {\n            let handleFocusIn2 = function(event) {\n                if (focusScope.paused || !container) return;\n                const target = event.target;\n                if (container.contains(target)) {\n                    lastFocusedElementRef.current = target;\n                } else {\n                    focus(lastFocusedElementRef.current, {\n                        select: true\n                    });\n                }\n            }, handleFocusOut2 = function(event) {\n                if (focusScope.paused || !container) return;\n                const relatedTarget = event.relatedTarget;\n                if (relatedTarget === null) return;\n                if (!container.contains(relatedTarget)) {\n                    focus(lastFocusedElementRef.current, {\n                        select: true\n                    });\n                }\n            }, handleMutations2 = function(mutations) {\n                const focusedElement = document.activeElement;\n                if (focusedElement !== document.body) return;\n                for (const mutation of mutations){\n                    if (mutation.removedNodes.length > 0) focus(container);\n                }\n            };\n            var handleFocusIn = handleFocusIn2, handleFocusOut = handleFocusOut2, handleMutations = handleMutations2;\n            document.addEventListener(\"focusin\", handleFocusIn2);\n            document.addEventListener(\"focusout\", handleFocusOut2);\n            const mutationObserver = new MutationObserver(handleMutations2);\n            if (container) mutationObserver.observe(container, {\n                childList: true,\n                subtree: true\n            });\n            return ()=>{\n                document.removeEventListener(\"focusin\", handleFocusIn2);\n                document.removeEventListener(\"focusout\", handleFocusOut2);\n                mutationObserver.disconnect();\n            };\n        }\n    }, [\n        trapped,\n        container,\n        focusScope.paused\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (container) {\n            focusScopesStack.add(focusScope);\n            const previouslyFocusedElement = document.activeElement;\n            const hasFocusedCandidate = container.contains(previouslyFocusedElement);\n            if (!hasFocusedCandidate) {\n                const mountEvent = new CustomEvent(AUTOFOCUS_ON_MOUNT, EVENT_OPTIONS);\n                container.addEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n                container.dispatchEvent(mountEvent);\n                if (!mountEvent.defaultPrevented) {\n                    focusFirst(removeLinks(getTabbableCandidates(container)), {\n                        select: true\n                    });\n                    if (document.activeElement === previouslyFocusedElement) {\n                        focus(container);\n                    }\n                }\n            }\n            return ()=>{\n                container.removeEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n                setTimeout(()=>{\n                    const unmountEvent = new CustomEvent(AUTOFOCUS_ON_UNMOUNT, EVENT_OPTIONS);\n                    container.addEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n                    container.dispatchEvent(unmountEvent);\n                    if (!unmountEvent.defaultPrevented) {\n                        focus(previouslyFocusedElement ?? document.body, {\n                            select: true\n                        });\n                    }\n                    container.removeEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n                    focusScopesStack.remove(focusScope);\n                }, 0);\n            };\n        }\n    }, [\n        container,\n        onMountAutoFocus,\n        onUnmountAutoFocus,\n        focusScope\n    ]);\n    const handleKeyDown = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>{\n        if (!loop && !trapped) return;\n        if (focusScope.paused) return;\n        const isTabKey = event.key === \"Tab\" && !event.altKey && !event.ctrlKey && !event.metaKey;\n        const focusedElement = document.activeElement;\n        if (isTabKey && focusedElement) {\n            const container2 = event.currentTarget;\n            const [first, last] = getTabbableEdges(container2);\n            const hasTabbableElementsInside = first && last;\n            if (!hasTabbableElementsInside) {\n                if (focusedElement === container2) event.preventDefault();\n            } else {\n                if (!event.shiftKey && focusedElement === last) {\n                    event.preventDefault();\n                    if (loop) focus(first, {\n                        select: true\n                    });\n                } else if (event.shiftKey && focusedElement === first) {\n                    event.preventDefault();\n                    if (loop) focus(last, {\n                        select: true\n                    });\n                }\n            }\n        }\n    }, [\n        loop,\n        trapped,\n        focusScope.paused\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        tabIndex: -1,\n        ...scopeProps,\n        ref: composedRefs,\n        onKeyDown: handleKeyDown\n    });\n});\nFocusScope.displayName = FOCUS_SCOPE_NAME;\nfunction focusFirst(candidates, { select = false } = {}) {\n    const previouslyFocusedElement = document.activeElement;\n    for (const candidate of candidates){\n        focus(candidate, {\n            select\n        });\n        if (document.activeElement !== previouslyFocusedElement) return;\n    }\n}\nfunction getTabbableEdges(container) {\n    const candidates = getTabbableCandidates(container);\n    const first = findVisible(candidates, container);\n    const last = findVisible(candidates.reverse(), container);\n    return [\n        first,\n        last\n    ];\n}\nfunction getTabbableCandidates(container) {\n    const nodes = [];\n    const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n        acceptNode: (node)=>{\n            const isHiddenInput = node.tagName === \"INPUT\" && node.type === \"hidden\";\n            if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n            return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n        }\n    });\n    while(walker.nextNode())nodes.push(walker.currentNode);\n    return nodes;\n}\nfunction findVisible(elements, container) {\n    for (const element of elements){\n        if (!isHidden(element, {\n            upTo: container\n        })) return element;\n    }\n}\nfunction isHidden(node, { upTo }) {\n    if (getComputedStyle(node).visibility === \"hidden\") return true;\n    while(node){\n        if (upTo !== void 0 && node === upTo) return false;\n        if (getComputedStyle(node).display === \"none\") return true;\n        node = node.parentElement;\n    }\n    return false;\n}\nfunction isSelectableInput(element) {\n    return element instanceof HTMLInputElement && \"select\" in element;\n}\nfunction focus(element, { select = false } = {}) {\n    if (element && element.focus) {\n        const previouslyFocusedElement = document.activeElement;\n        element.focus({\n            preventScroll: true\n        });\n        if (element !== previouslyFocusedElement && isSelectableInput(element) && select) element.select();\n    }\n}\nvar focusScopesStack = createFocusScopesStack();\nfunction createFocusScopesStack() {\n    let stack = [];\n    return {\n        add (focusScope) {\n            const activeFocusScope = stack[0];\n            if (focusScope !== activeFocusScope) {\n                activeFocusScope?.pause();\n            }\n            stack = arrayRemove(stack, focusScope);\n            stack.unshift(focusScope);\n        },\n        remove (focusScope) {\n            stack = arrayRemove(stack, focusScope);\n            stack[0]?.resume();\n        }\n    };\n}\nfunction arrayRemove(array, item) {\n    const updatedArray = [\n        ...array\n    ];\n    const index = updatedArray.indexOf(item);\n    if (index !== -1) {\n        updatedArray.splice(index, 1);\n    }\n    return updatedArray;\n}\nfunction removeLinks(items) {\n    return items.filter((item)=>item.tagName !== \"A\");\n}\nvar Root = FocusScope;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWZvY3VzLXNjb3BlL2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQXVCO0FBQ1M7QUFDTjtBQUNLO0FBd00zQjtBQXRNSixJQUFNSyxxQkFBcUI7QUFDM0IsSUFBTUMsdUJBQXVCO0FBQzdCLElBQU1DLGdCQUFnQjtJQUFFQyxTQUFTO0lBQU9DLFlBQVk7QUFBSztBQVF6RCxJQUFNQyxtQkFBbUI7QUFnQ3pCLElBQU1DLDJCQUFtQlgsNkNBQUEsQ0FBK0MsQ0FBQ2EsT0FBT0M7SUFDOUUsTUFBTSxFQUNKQyxPQUFPLE9BQ1BDLFVBQVUsT0FDVkMsa0JBQWtCQyxvQkFBQSxFQUNsQkMsb0JBQW9CQyxzQkFBQSxFQUNwQixHQUFHQyxZQUNMLEdBQUlSO0lBQ0osTUFBTSxDQUFDUyxXQUFXQyxhQUFZLEdBQVV2QiwyQ0FBQSxDQUE2QjtJQUNyRSxNQUFNaUIsbUJBQW1CZCxnRkFBY0EsQ0FBQ2U7SUFDeEMsTUFBTUMscUJBQXFCaEIsZ0ZBQWNBLENBQUNpQjtJQUMxQyxNQUFNSyx3QkFBOEJ6Qix5Q0FBQSxDQUEyQjtJQUMvRCxNQUFNMkIsZUFBZTFCLDZFQUFlQSxDQUFDYSxjQUFjLENBQUNjLE9BQVNMLGFBQWFLO0lBRTFFLE1BQU1DLGFBQW1CN0IseUNBQUEsQ0FBTztRQUM5QjhCLFFBQVE7UUFDUkM7WUFDRSxLQUFLRCxNQUFBLEdBQVM7UUFDaEI7UUFDQUU7WUFDRSxLQUFLRixNQUFBLEdBQVM7UUFDaEI7SUFDRixHQUFHRyxPQUFBO0lBR0dqQyw0Q0FBQSxDQUFVO1FBQ2QsSUFBSWdCLFNBQVM7WUFDWCxJQUFTbUIsaUJBQVQsU0FBdUJDLEtBQUE7Z0JBQ3JCLElBQUlQLFdBQVdDLE1BQUEsSUFBVSxDQUFDUixXQUFXO2dCQUNyQyxNQUFNZSxTQUFTRCxNQUFNQyxNQUFBO2dCQUNyQixJQUFJZixVQUFVZ0IsUUFBQSxDQUFTRCxTQUFTO29CQUM5Qlosc0JBQXNCUSxPQUFBLEdBQVVJO2dCQUNsQyxPQUFPO29CQUNMRSxNQUFNZCxzQkFBc0JRLE9BQUEsRUFBUzt3QkFBRU8sUUFBUTtvQkFBSztnQkFDdEQ7WUFDRixHQUVTQyxrQkFBVCxTQUF3QkwsS0FBQTtnQkFDdEIsSUFBSVAsV0FBV0MsTUFBQSxJQUFVLENBQUNSLFdBQVc7Z0JBQ3JDLE1BQU1vQixnQkFBZ0JOLE1BQU1NLGFBQUE7Z0JBWTVCLElBQUlBLGtCQUFrQixNQUFNO2dCQUk1QixJQUFJLENBQUNwQixVQUFVZ0IsUUFBQSxDQUFTSSxnQkFBZ0I7b0JBQ3RDSCxNQUFNZCxzQkFBc0JRLE9BQUEsRUFBUzt3QkFBRU8sUUFBUTtvQkFBSztnQkFDdEQ7WUFDRixHQUtTRyxtQkFBVCxTQUF5QkMsU0FBQTtnQkFDdkIsTUFBTUMsaUJBQWlCQyxTQUFTQyxhQUFBO2dCQUNoQyxJQUFJRixtQkFBbUJDLFNBQVNFLElBQUEsRUFBTTtnQkFDdEMsV0FBV0MsWUFBWUwsVUFBVztvQkFDaEMsSUFBSUssU0FBU0MsWUFBQSxDQUFhQyxNQUFBLEdBQVMsR0FBR1osTUFBTWpCO2dCQUM5QztZQUNGO1lBMUNTLElBQUFhLGdCQUFBQSxnQkFVQU0saUJBQUFBLGlCQTBCQUUsa0JBQUFBO1lBUVRHLFNBQVNNLGdCQUFBLENBQWlCLFdBQVdqQjtZQUNyQ1csU0FBU00sZ0JBQUEsQ0FBaUIsWUFBWVg7WUFDdEMsTUFBTVksbUJBQW1CLElBQUlDLGlCQUFpQlg7WUFDOUMsSUFBSXJCLFdBQVcrQixpQkFBaUJFLE9BQUEsQ0FBUWpDLFdBQVc7Z0JBQUVrQyxXQUFXO2dCQUFNQyxTQUFTO1lBQUs7WUFFcEYsT0FBTztnQkFDTFgsU0FBU1ksbUJBQUEsQ0FBb0IsV0FBV3ZCO2dCQUN4Q1csU0FBU1ksbUJBQUEsQ0FBb0IsWUFBWWpCO2dCQUN6Q1ksaUJBQWlCTSxVQUFBO1lBQ25CO1FBQ0Y7SUFDRixHQUFHO1FBQUMzQztRQUFTTTtRQUFXTyxXQUFXQyxNQUFNO0tBQUM7SUFFcEM5Qiw0Q0FBQSxDQUFVO1FBQ2QsSUFBSXNCLFdBQVc7WUFDYnNDLGlCQUFpQkMsR0FBQSxDQUFJaEM7WUFDckIsTUFBTWlDLDJCQUEyQmhCLFNBQVNDLGFBQUE7WUFDMUMsTUFBTWdCLHNCQUFzQnpDLFVBQVVnQixRQUFBLENBQVN3QjtZQUUvQyxJQUFJLENBQUNDLHFCQUFxQjtnQkFDeEIsTUFBTUMsYUFBYSxJQUFJQyxZQUFZNUQsb0JBQW9CRTtnQkFDdkRlLFVBQVU4QixnQkFBQSxDQUFpQi9DLG9CQUFvQlk7Z0JBQy9DSyxVQUFVNEMsYUFBQSxDQUFjRjtnQkFDeEIsSUFBSSxDQUFDQSxXQUFXRyxnQkFBQSxFQUFrQjtvQkFDaENDLFdBQVdDLFlBQVlDLHNCQUFzQmhELGFBQWE7d0JBQUVrQixRQUFRO29CQUFLO29CQUN6RSxJQUFJTSxTQUFTQyxhQUFBLEtBQWtCZSwwQkFBMEI7d0JBQ3ZEdkIsTUFBTWpCO29CQUNSO2dCQUNGO1lBQ0Y7WUFFQSxPQUFPO2dCQUNMQSxVQUFVb0MsbUJBQUEsQ0FBb0JyRCxvQkFBb0JZO2dCQUtsRHNELFdBQVc7b0JBQ1QsTUFBTUMsZUFBZSxJQUFJUCxZQUFZM0Qsc0JBQXNCQztvQkFDM0RlLFVBQVU4QixnQkFBQSxDQUFpQjlDLHNCQUFzQmE7b0JBQ2pERyxVQUFVNEMsYUFBQSxDQUFjTTtvQkFDeEIsSUFBSSxDQUFDQSxhQUFhTCxnQkFBQSxFQUFrQjt3QkFDbEM1QixNQUFNdUIsNEJBQTRCaEIsU0FBU0UsSUFBQSxFQUFNOzRCQUFFUixRQUFRO3dCQUFLO29CQUNsRTtvQkFFQWxCLFVBQVVvQyxtQkFBQSxDQUFvQnBELHNCQUFzQmE7b0JBRXBEeUMsaUJBQWlCYSxNQUFBLENBQU81QztnQkFDMUIsR0FBRztZQUNMO1FBQ0Y7SUFDRixHQUFHO1FBQUNQO1FBQVdMO1FBQWtCRTtRQUFvQlU7S0FBVztJQUdoRSxNQUFNNkMsZ0JBQXNCMUUsOENBQUEsQ0FDMUIsQ0FBQ29DO1FBQ0MsSUFBSSxDQUFDckIsUUFBUSxDQUFDQyxTQUFTO1FBQ3ZCLElBQUlhLFdBQVdDLE1BQUEsRUFBUTtRQUV2QixNQUFNOEMsV0FBV3hDLE1BQU15QyxHQUFBLEtBQVEsU0FBUyxDQUFDekMsTUFBTTBDLE1BQUEsSUFBVSxDQUFDMUMsTUFBTTJDLE9BQUEsSUFBVyxDQUFDM0MsTUFBTTRDLE9BQUE7UUFDbEYsTUFBTW5DLGlCQUFpQkMsU0FBU0MsYUFBQTtRQUVoQyxJQUFJNkIsWUFBWS9CLGdCQUFnQjtZQUM5QixNQUFNdkIsYUFBWWMsTUFBTTZDLGFBQUE7WUFDeEIsTUFBTSxDQUFDQyxPQUFPQyxLQUFJLEdBQUlDLGlCQUFpQjlEO1lBQ3ZDLE1BQU0rRCw0QkFBNEJILFNBQVNDO1lBRzNDLElBQUksQ0FBQ0UsMkJBQTJCO2dCQUM5QixJQUFJeEMsbUJBQW1CdkIsWUFBV2MsTUFBTWtELGNBQUE7WUFDMUMsT0FBTztnQkFDTCxJQUFJLENBQUNsRCxNQUFNbUQsUUFBQSxJQUFZMUMsbUJBQW1Cc0MsTUFBTTtvQkFDOUMvQyxNQUFNa0QsY0FBQTtvQkFDTixJQUFJdkUsTUFBTXdCLE1BQU0yQyxPQUFPO3dCQUFFMUMsUUFBUTtvQkFBSztnQkFDeEMsV0FBV0osTUFBTW1ELFFBQUEsSUFBWTFDLG1CQUFtQnFDLE9BQU87b0JBQ3JEOUMsTUFBTWtELGNBQUE7b0JBQ04sSUFBSXZFLE1BQU13QixNQUFNNEMsTUFBTTt3QkFBRTNDLFFBQVE7b0JBQUs7Z0JBQ3ZDO1lBQ0Y7UUFDRjtJQUNGLEdBQ0E7UUFBQ3pCO1FBQU1DO1FBQVNhLFdBQVdDLE1BQU07S0FBQTtJQUduQyxPQUNFLGdCQUFBMUIsc0RBQUFBLENBQUNGLGdFQUFTQSxDQUFDc0YsR0FBQSxFQUFWO1FBQWNDLFVBQVU7UUFBSyxHQUFHcEUsVUFBQTtRQUFZcUUsS0FBSy9EO1FBQWNnRSxXQUFXakI7SUFBQTtBQUUvRTtBQUVBL0QsV0FBV2lGLFdBQUEsR0FBY2xGO0FBVXpCLFNBQVMwRCxXQUFXeUIsVUFBQSxFQUEyQixFQUFFckQsU0FBUyxPQUFNLEdBQUksQ0FBQztJQUNuRSxNQUFNc0IsMkJBQTJCaEIsU0FBU0MsYUFBQTtJQUMxQyxXQUFXK0MsYUFBYUQsV0FBWTtRQUNsQ3RELE1BQU11RCxXQUFXO1lBQUV0RDtRQUFPO1FBQzFCLElBQUlNLFNBQVNDLGFBQUEsS0FBa0JlLDBCQUEwQjtJQUMzRDtBQUNGO0FBS0EsU0FBU3NCLGlCQUFpQjlELFNBQUE7SUFDeEIsTUFBTXVFLGFBQWF2QixzQkFBc0JoRDtJQUN6QyxNQUFNNEQsUUFBUWEsWUFBWUYsWUFBWXZFO0lBQ3RDLE1BQU02RCxPQUFPWSxZQUFZRixXQUFXRyxPQUFBLElBQVcxRTtJQUMvQyxPQUFPO1FBQUM0RDtRQUFPQztLQUFJO0FBQ3JCO0FBWUEsU0FBU2Isc0JBQXNCaEQsU0FBQTtJQUM3QixNQUFNMkUsUUFBdUIsRUFBQztJQUM5QixNQUFNQyxTQUFTcEQsU0FBU3FELGdCQUFBLENBQWlCN0UsV0FBVzhFLFdBQVdDLFlBQUEsRUFBYztRQUMzRUMsWUFBWSxDQUFDMUU7WUFDWCxNQUFNMkUsZ0JBQWdCM0UsS0FBSzRFLE9BQUEsS0FBWSxXQUFXNUUsS0FBSzZFLElBQUEsS0FBUztZQUNoRSxJQUFJN0UsS0FBSzhFLFFBQUEsSUFBWTlFLEtBQUsrRSxNQUFBLElBQVVKLGVBQWUsT0FBT0gsV0FBV1EsV0FBQTtZQUlyRSxPQUFPaEYsS0FBSzZELFFBQUEsSUFBWSxJQUFJVyxXQUFXUyxhQUFBLEdBQWdCVCxXQUFXUSxXQUFBO1FBQ3BFO0lBQ0Y7SUFDQSxNQUFPVixPQUFPWSxRQUFBLEdBQVliLE1BQU1jLElBQUEsQ0FBS2IsT0FBT2MsV0FBMEI7SUFHdEUsT0FBT2Y7QUFDVDtBQU1BLFNBQVNGLFlBQVlrQixRQUFBLEVBQXlCM0YsU0FBQTtJQUM1QyxXQUFXNEYsV0FBV0QsU0FBVTtRQUU5QixJQUFJLENBQUNFLFNBQVNELFNBQVM7WUFBRUUsTUFBTTlGO1FBQVUsSUFBSSxPQUFPNEY7SUFDdEQ7QUFDRjtBQUVBLFNBQVNDLFNBQVN2RixJQUFBLEVBQW1CLEVBQUV3RixJQUFBLEVBQUs7SUFDMUMsSUFBSUMsaUJBQWlCekYsTUFBTTBGLFVBQUEsS0FBZSxVQUFVLE9BQU87SUFDM0QsTUFBTzFGLEtBQU07UUFFWCxJQUFJd0YsU0FBUyxVQUFheEYsU0FBU3dGLE1BQU0sT0FBTztRQUNoRCxJQUFJQyxpQkFBaUJ6RixNQUFNMkYsT0FBQSxLQUFZLFFBQVEsT0FBTztRQUN0RDNGLE9BQU9BLEtBQUs0RixhQUFBO0lBQ2Q7SUFDQSxPQUFPO0FBQ1Q7QUFFQSxTQUFTQyxrQkFBa0JQLE9BQUE7SUFDekIsT0FBT0EsbUJBQW1CUSxvQkFBb0IsWUFBWVI7QUFDNUQ7QUFFQSxTQUFTM0UsTUFBTTJFLE9BQUEsRUFBa0MsRUFBRTFFLFNBQVMsT0FBTSxHQUFJLENBQUM7SUFFckUsSUFBSTBFLFdBQVdBLFFBQVEzRSxLQUFBLEVBQU87UUFDNUIsTUFBTXVCLDJCQUEyQmhCLFNBQVNDLGFBQUE7UUFFMUNtRSxRQUFRM0UsS0FBQSxDQUFNO1lBQUVvRixlQUFlO1FBQUs7UUFFcEMsSUFBSVQsWUFBWXBELDRCQUE0QjJELGtCQUFrQlAsWUFBWTFFLFFBQ3hFMEUsUUFBUTFFLE1BQUE7SUFDWjtBQUNGO0FBT0EsSUFBTW9CLG1CQUFtQmdFO0FBRXpCLFNBQVNBO0lBRVAsSUFBSUMsUUFBeUIsRUFBQztJQUU5QixPQUFPO1FBQ0xoRSxLQUFJaEMsVUFBQTtZQUVGLE1BQU1pRyxtQkFBbUJELEtBQUEsQ0FBTSxFQUFDO1lBQ2hDLElBQUloRyxlQUFlaUcsa0JBQWtCO2dCQUNuQ0Esa0JBQWtCL0Y7WUFDcEI7WUFFQThGLFFBQVFFLFlBQVlGLE9BQU9oRztZQUMzQmdHLE1BQU1HLE9BQUEsQ0FBUW5HO1FBQ2hCO1FBRUE0QyxRQUFPNUMsVUFBQTtZQUNMZ0csUUFBUUUsWUFBWUYsT0FBT2hHO1lBQzNCZ0csS0FBQSxDQUFNLEVBQUMsRUFBRzdGO1FBQ1o7SUFDRjtBQUNGO0FBRUEsU0FBUytGLFlBQWVFLEtBQUEsRUFBWUMsSUFBQTtJQUNsQyxNQUFNQyxlQUFlO1dBQUlGO0tBQUs7SUFDOUIsTUFBTUcsUUFBUUQsYUFBYUUsT0FBQSxDQUFRSDtJQUNuQyxJQUFJRSxVQUFVLElBQUk7UUFDaEJELGFBQWFHLE1BQUEsQ0FBT0YsT0FBTztJQUM3QjtJQUNBLE9BQU9EO0FBQ1Q7QUFFQSxTQUFTOUQsWUFBWWtFLEtBQUE7SUFDbkIsT0FBT0EsTUFBTUMsTUFBQSxDQUFPLENBQUNOLE9BQVNBLEtBQUsxQixPQUFBLEtBQVk7QUFDakQ7QUFFQSxJQUFNaUMsT0FBTzlIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW5ra29yLy4uL3NyYy9mb2N1cy1zY29wZS50c3g/YzkwZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VDb21wb3NlZFJlZnMgfSBmcm9tICdAcmFkaXgtdWkvcmVhY3QtY29tcG9zZS1yZWZzJztcbmltcG9ydCB7IFByaW1pdGl2ZSB9IGZyb20gJ0ByYWRpeC11aS9yZWFjdC1wcmltaXRpdmUnO1xuaW1wb3J0IHsgdXNlQ2FsbGJhY2tSZWYgfSBmcm9tICdAcmFkaXgtdWkvcmVhY3QtdXNlLWNhbGxiYWNrLXJlZic7XG5cbmNvbnN0IEFVVE9GT0NVU19PTl9NT1VOVCA9ICdmb2N1c1Njb3BlLmF1dG9Gb2N1c09uTW91bnQnO1xuY29uc3QgQVVUT0ZPQ1VTX09OX1VOTU9VTlQgPSAnZm9jdXNTY29wZS5hdXRvRm9jdXNPblVubW91bnQnO1xuY29uc3QgRVZFTlRfT1BUSU9OUyA9IHsgYnViYmxlczogZmFsc2UsIGNhbmNlbGFibGU6IHRydWUgfTtcblxudHlwZSBGb2N1c2FibGVUYXJnZXQgPSBIVE1MRWxlbWVudCB8IHsgZm9jdXMoKTogdm9pZCB9O1xuXG4vKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4gKiBGb2N1c1Njb3BlXG4gKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5cbmNvbnN0IEZPQ1VTX1NDT1BFX05BTUUgPSAnRm9jdXNTY29wZSc7XG5cbnR5cGUgRm9jdXNTY29wZUVsZW1lbnQgPSBSZWFjdC5FbGVtZW50UmVmPHR5cGVvZiBQcmltaXRpdmUuZGl2PjtcbnR5cGUgUHJpbWl0aXZlRGl2UHJvcHMgPSBSZWFjdC5Db21wb25lbnRQcm9wc1dpdGhvdXRSZWY8dHlwZW9mIFByaW1pdGl2ZS5kaXY+O1xuaW50ZXJmYWNlIEZvY3VzU2NvcGVQcm9wcyBleHRlbmRzIFByaW1pdGl2ZURpdlByb3BzIHtcbiAgLyoqXG4gICAqIFdoZW4gYHRydWVgLCB0YWJiaW5nIGZyb20gbGFzdCBpdGVtIHdpbGwgZm9jdXMgZmlyc3QgdGFiYmFibGVcbiAgICogYW5kIHNoaWZ0K3RhYiBmcm9tIGZpcnN0IGl0ZW0gd2lsbCBmb2N1cyBsYXN0IHRhYmFiYmxlLlxuICAgKiBAZGVmYXVsdFZhbHVlIGZhbHNlXG4gICAqL1xuICBsb29wPzogYm9vbGVhbjtcblxuICAvKipcbiAgICogV2hlbiBgdHJ1ZWAsIGZvY3VzIGNhbm5vdCBlc2NhcGUgdGhlIGZvY3VzIHNjb3BlIHZpYSBrZXlib2FyZCxcbiAgICogcG9pbnRlciwgb3IgYSBwcm9ncmFtbWF0aWMgZm9jdXMuXG4gICAqIEBkZWZhdWx0VmFsdWUgZmFsc2VcbiAgICovXG4gIHRyYXBwZWQ/OiBib29sZWFuO1xuXG4gIC8qKlxuICAgKiBFdmVudCBoYW5kbGVyIGNhbGxlZCB3aGVuIGF1dG8tZm9jdXNpbmcgb24gbW91bnQuXG4gICAqIENhbiBiZSBwcmV2ZW50ZWQuXG4gICAqL1xuICBvbk1vdW50QXV0b0ZvY3VzPzogKGV2ZW50OiBFdmVudCkgPT4gdm9pZDtcblxuICAvKipcbiAgICogRXZlbnQgaGFuZGxlciBjYWxsZWQgd2hlbiBhdXRvLWZvY3VzaW5nIG9uIHVubW91bnQuXG4gICAqIENhbiBiZSBwcmV2ZW50ZWQuXG4gICAqL1xuICBvblVubW91bnRBdXRvRm9jdXM/OiAoZXZlbnQ6IEV2ZW50KSA9PiB2b2lkO1xufVxuXG5jb25zdCBGb2N1c1Njb3BlID0gUmVhY3QuZm9yd2FyZFJlZjxGb2N1c1Njb3BlRWxlbWVudCwgRm9jdXNTY29wZVByb3BzPigocHJvcHMsIGZvcndhcmRlZFJlZikgPT4ge1xuICBjb25zdCB7XG4gICAgbG9vcCA9IGZhbHNlLFxuICAgIHRyYXBwZWQgPSBmYWxzZSxcbiAgICBvbk1vdW50QXV0b0ZvY3VzOiBvbk1vdW50QXV0b0ZvY3VzUHJvcCxcbiAgICBvblVubW91bnRBdXRvRm9jdXM6IG9uVW5tb3VudEF1dG9Gb2N1c1Byb3AsXG4gICAgLi4uc2NvcGVQcm9wc1xuICB9ID0gcHJvcHM7XG4gIGNvbnN0IFtjb250YWluZXIsIHNldENvbnRhaW5lcl0gPSBSZWFjdC51c2VTdGF0ZTxIVE1MRWxlbWVudCB8IG51bGw+KG51bGwpO1xuICBjb25zdCBvbk1vdW50QXV0b0ZvY3VzID0gdXNlQ2FsbGJhY2tSZWYob25Nb3VudEF1dG9Gb2N1c1Byb3ApO1xuICBjb25zdCBvblVubW91bnRBdXRvRm9jdXMgPSB1c2VDYWxsYmFja1JlZihvblVubW91bnRBdXRvRm9jdXNQcm9wKTtcbiAgY29uc3QgbGFzdEZvY3VzZWRFbGVtZW50UmVmID0gUmVhY3QudXNlUmVmPEhUTUxFbGVtZW50IHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IGNvbXBvc2VkUmVmcyA9IHVzZUNvbXBvc2VkUmVmcyhmb3J3YXJkZWRSZWYsIChub2RlKSA9PiBzZXRDb250YWluZXIobm9kZSkpO1xuXG4gIGNvbnN0IGZvY3VzU2NvcGUgPSBSZWFjdC51c2VSZWYoe1xuICAgIHBhdXNlZDogZmFsc2UsXG4gICAgcGF1c2UoKSB7XG4gICAgICB0aGlzLnBhdXNlZCA9IHRydWU7XG4gICAgfSxcbiAgICByZXN1bWUoKSB7XG4gICAgICB0aGlzLnBhdXNlZCA9IGZhbHNlO1xuICAgIH0sXG4gIH0pLmN1cnJlbnQ7XG5cbiAgLy8gVGFrZXMgY2FyZSBvZiB0cmFwcGluZyBmb2N1cyBpZiBmb2N1cyBpcyBtb3ZlZCBvdXRzaWRlIHByb2dyYW1tYXRpY2FsbHkgZm9yIGV4YW1wbGVcbiAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAodHJhcHBlZCkge1xuICAgICAgZnVuY3Rpb24gaGFuZGxlRm9jdXNJbihldmVudDogRm9jdXNFdmVudCkge1xuICAgICAgICBpZiAoZm9jdXNTY29wZS5wYXVzZWQgfHwgIWNvbnRhaW5lcikgcmV0dXJuO1xuICAgICAgICBjb25zdCB0YXJnZXQgPSBldmVudC50YXJnZXQgYXMgSFRNTEVsZW1lbnQgfCBudWxsO1xuICAgICAgICBpZiAoY29udGFpbmVyLmNvbnRhaW5zKHRhcmdldCkpIHtcbiAgICAgICAgICBsYXN0Rm9jdXNlZEVsZW1lbnRSZWYuY3VycmVudCA9IHRhcmdldDtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBmb2N1cyhsYXN0Rm9jdXNlZEVsZW1lbnRSZWYuY3VycmVudCwgeyBzZWxlY3Q6IHRydWUgfSk7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgZnVuY3Rpb24gaGFuZGxlRm9jdXNPdXQoZXZlbnQ6IEZvY3VzRXZlbnQpIHtcbiAgICAgICAgaWYgKGZvY3VzU2NvcGUucGF1c2VkIHx8ICFjb250YWluZXIpIHJldHVybjtcbiAgICAgICAgY29uc3QgcmVsYXRlZFRhcmdldCA9IGV2ZW50LnJlbGF0ZWRUYXJnZXQgYXMgSFRNTEVsZW1lbnQgfCBudWxsO1xuXG4gICAgICAgIC8vIEEgYGZvY3Vzb3V0YCBldmVudCB3aXRoIGEgYG51bGxgIGByZWxhdGVkVGFyZ2V0YCB3aWxsIGhhcHBlbiBpbiBhdCBsZWFzdCB0d28gY2FzZXM6XG4gICAgICAgIC8vXG4gICAgICAgIC8vIDEuIFdoZW4gdGhlIHVzZXIgc3dpdGNoZXMgYXBwL3RhYnMvd2luZG93cy90aGUgYnJvd3NlciBpdHNlbGYgbG9zZXMgZm9jdXMuXG4gICAgICAgIC8vIDIuIEluIEdvb2dsZSBDaHJvbWUsIHdoZW4gdGhlIGZvY3VzZWQgZWxlbWVudCBpcyByZW1vdmVkIGZyb20gdGhlIERPTS5cbiAgICAgICAgLy9cbiAgICAgICAgLy8gV2UgbGV0IHRoZSBicm93c2VyIGRvIGl0cyB0aGluZyBoZXJlIGJlY2F1c2U6XG4gICAgICAgIC8vXG4gICAgICAgIC8vIDEuIFRoZSBicm93c2VyIGFscmVhZHkga2VlcHMgYSBtZW1vcnkgb2Ygd2hhdCdzIGZvY3VzZWQgZm9yIHdoZW4gdGhlIHBhZ2UgZ2V0cyByZWZvY3VzZWQuXG4gICAgICAgIC8vIDIuIEluIEdvb2dsZSBDaHJvbWUsIGlmIHdlIHRyeSB0byBmb2N1cyB0aGUgZGVsZXRlZCBmb2N1c2VkIGVsZW1lbnQgKGFzIHBlciBiZWxvdyksIGl0XG4gICAgICAgIC8vICAgIHRocm93cyB0aGUgQ1BVIHRvIDEwMCUsIHNvIHdlIGF2b2lkIGRvaW5nIGFueXRoaW5nIGZvciB0aGlzIHJlYXNvbiBoZXJlIHRvby5cbiAgICAgICAgaWYgKHJlbGF0ZWRUYXJnZXQgPT09IG51bGwpIHJldHVybjtcblxuICAgICAgICAvLyBJZiB0aGUgZm9jdXMgaGFzIG1vdmVkIHRvIGFuIGFjdHVhbCBsZWdpdGltYXRlIGVsZW1lbnQgKGByZWxhdGVkVGFyZ2V0ICE9PSBudWxsYClcbiAgICAgICAgLy8gdGhhdCBpcyBvdXRzaWRlIHRoZSBjb250YWluZXIsIHdlIG1vdmUgZm9jdXMgdG8gdGhlIGxhc3QgdmFsaWQgZm9jdXNlZCBlbGVtZW50IGluc2lkZS5cbiAgICAgICAgaWYgKCFjb250YWluZXIuY29udGFpbnMocmVsYXRlZFRhcmdldCkpIHtcbiAgICAgICAgICBmb2N1cyhsYXN0Rm9jdXNlZEVsZW1lbnRSZWYuY3VycmVudCwgeyBzZWxlY3Q6IHRydWUgfSk7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8gV2hlbiB0aGUgZm9jdXNlZCBlbGVtZW50IGdldHMgcmVtb3ZlZCBmcm9tIHRoZSBET00sIGJyb3dzZXJzIG1vdmUgZm9jdXNcbiAgICAgIC8vIGJhY2sgdG8gdGhlIGRvY3VtZW50LmJvZHkuIEluIHRoaXMgY2FzZSwgd2UgbW92ZSBmb2N1cyB0byB0aGUgY29udGFpbmVyXG4gICAgICAvLyB0byBrZWVwIGZvY3VzIHRyYXBwZWQgY29ycmVjdGx5LlxuICAgICAgZnVuY3Rpb24gaGFuZGxlTXV0YXRpb25zKG11dGF0aW9uczogTXV0YXRpb25SZWNvcmRbXSkge1xuICAgICAgICBjb25zdCBmb2N1c2VkRWxlbWVudCA9IGRvY3VtZW50LmFjdGl2ZUVsZW1lbnQgYXMgSFRNTEVsZW1lbnQgfCBudWxsO1xuICAgICAgICBpZiAoZm9jdXNlZEVsZW1lbnQgIT09IGRvY3VtZW50LmJvZHkpIHJldHVybjtcbiAgICAgICAgZm9yIChjb25zdCBtdXRhdGlvbiBvZiBtdXRhdGlvbnMpIHtcbiAgICAgICAgICBpZiAobXV0YXRpb24ucmVtb3ZlZE5vZGVzLmxlbmd0aCA+IDApIGZvY3VzKGNvbnRhaW5lcik7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcignZm9jdXNpbicsIGhhbmRsZUZvY3VzSW4pO1xuICAgICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcignZm9jdXNvdXQnLCBoYW5kbGVGb2N1c091dCk7XG4gICAgICBjb25zdCBtdXRhdGlvbk9ic2VydmVyID0gbmV3IE11dGF0aW9uT2JzZXJ2ZXIoaGFuZGxlTXV0YXRpb25zKTtcbiAgICAgIGlmIChjb250YWluZXIpIG11dGF0aW9uT2JzZXJ2ZXIub2JzZXJ2ZShjb250YWluZXIsIHsgY2hpbGRMaXN0OiB0cnVlLCBzdWJ0cmVlOiB0cnVlIH0pO1xuXG4gICAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgICBkb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKCdmb2N1c2luJywgaGFuZGxlRm9jdXNJbik7XG4gICAgICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2ZvY3Vzb3V0JywgaGFuZGxlRm9jdXNPdXQpO1xuICAgICAgICBtdXRhdGlvbk9ic2VydmVyLmRpc2Nvbm5lY3QoKTtcbiAgICAgIH07XG4gICAgfVxuICB9LCBbdHJhcHBlZCwgY29udGFpbmVyLCBmb2N1c1Njb3BlLnBhdXNlZF0pO1xuXG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGNvbnRhaW5lcikge1xuICAgICAgZm9jdXNTY29wZXNTdGFjay5hZGQoZm9jdXNTY29wZSk7XG4gICAgICBjb25zdCBwcmV2aW91c2x5Rm9jdXNlZEVsZW1lbnQgPSBkb2N1bWVudC5hY3RpdmVFbGVtZW50IGFzIEhUTUxFbGVtZW50IHwgbnVsbDtcbiAgICAgIGNvbnN0IGhhc0ZvY3VzZWRDYW5kaWRhdGUgPSBjb250YWluZXIuY29udGFpbnMocHJldmlvdXNseUZvY3VzZWRFbGVtZW50KTtcblxuICAgICAgaWYgKCFoYXNGb2N1c2VkQ2FuZGlkYXRlKSB7XG4gICAgICAgIGNvbnN0IG1vdW50RXZlbnQgPSBuZXcgQ3VzdG9tRXZlbnQoQVVUT0ZPQ1VTX09OX01PVU5ULCBFVkVOVF9PUFRJT05TKTtcbiAgICAgICAgY29udGFpbmVyLmFkZEV2ZW50TGlzdGVuZXIoQVVUT0ZPQ1VTX09OX01PVU5ULCBvbk1vdW50QXV0b0ZvY3VzKTtcbiAgICAgICAgY29udGFpbmVyLmRpc3BhdGNoRXZlbnQobW91bnRFdmVudCk7XG4gICAgICAgIGlmICghbW91bnRFdmVudC5kZWZhdWx0UHJldmVudGVkKSB7XG4gICAgICAgICAgZm9jdXNGaXJzdChyZW1vdmVMaW5rcyhnZXRUYWJiYWJsZUNhbmRpZGF0ZXMoY29udGFpbmVyKSksIHsgc2VsZWN0OiB0cnVlIH0pO1xuICAgICAgICAgIGlmIChkb2N1bWVudC5hY3RpdmVFbGVtZW50ID09PSBwcmV2aW91c2x5Rm9jdXNlZEVsZW1lbnQpIHtcbiAgICAgICAgICAgIGZvY3VzKGNvbnRhaW5lcik7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAgIGNvbnRhaW5lci5yZW1vdmVFdmVudExpc3RlbmVyKEFVVE9GT0NVU19PTl9NT1VOVCwgb25Nb3VudEF1dG9Gb2N1cyk7XG5cbiAgICAgICAgLy8gV2UgaGl0IGEgcmVhY3QgYnVnIChmaXhlZCBpbiB2MTcpIHdpdGggZm9jdXNpbmcgaW4gdW5tb3VudC5cbiAgICAgICAgLy8gV2UgbmVlZCB0byBkZWxheSB0aGUgZm9jdXMgYSBsaXR0bGUgdG8gZ2V0IGFyb3VuZCBpdCBmb3Igbm93LlxuICAgICAgICAvLyBTZWU6IGh0dHBzOi8vZ2l0aHViLmNvbS9mYWNlYm9vay9yZWFjdC9pc3N1ZXMvMTc4OTRcbiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgY29uc3QgdW5tb3VudEV2ZW50ID0gbmV3IEN1c3RvbUV2ZW50KEFVVE9GT0NVU19PTl9VTk1PVU5ULCBFVkVOVF9PUFRJT05TKTtcbiAgICAgICAgICBjb250YWluZXIuYWRkRXZlbnRMaXN0ZW5lcihBVVRPRk9DVVNfT05fVU5NT1VOVCwgb25Vbm1vdW50QXV0b0ZvY3VzKTtcbiAgICAgICAgICBjb250YWluZXIuZGlzcGF0Y2hFdmVudCh1bm1vdW50RXZlbnQpO1xuICAgICAgICAgIGlmICghdW5tb3VudEV2ZW50LmRlZmF1bHRQcmV2ZW50ZWQpIHtcbiAgICAgICAgICAgIGZvY3VzKHByZXZpb3VzbHlGb2N1c2VkRWxlbWVudCA/PyBkb2N1bWVudC5ib2R5LCB7IHNlbGVjdDogdHJ1ZSB9KTtcbiAgICAgICAgICB9XG4gICAgICAgICAgLy8gd2UgbmVlZCB0byByZW1vdmUgdGhlIGxpc3RlbmVyIGFmdGVyIHdlIGBkaXNwYXRjaEV2ZW50YFxuICAgICAgICAgIGNvbnRhaW5lci5yZW1vdmVFdmVudExpc3RlbmVyKEFVVE9GT0NVU19PTl9VTk1PVU5ULCBvblVubW91bnRBdXRvRm9jdXMpO1xuXG4gICAgICAgICAgZm9jdXNTY29wZXNTdGFjay5yZW1vdmUoZm9jdXNTY29wZSk7XG4gICAgICAgIH0sIDApO1xuICAgICAgfTtcbiAgICB9XG4gIH0sIFtjb250YWluZXIsIG9uTW91bnRBdXRvRm9jdXMsIG9uVW5tb3VudEF1dG9Gb2N1cywgZm9jdXNTY29wZV0pO1xuXG4gIC8vIFRha2VzIGNhcmUgb2YgbG9vcGluZyBmb2N1cyAod2hlbiB0YWJiaW5nIHdoaWxzdCBhdCB0aGUgZWRnZXMpXG4gIGNvbnN0IGhhbmRsZUtleURvd24gPSBSZWFjdC51c2VDYWxsYmFjayhcbiAgICAoZXZlbnQ6IFJlYWN0LktleWJvYXJkRXZlbnQpID0+IHtcbiAgICAgIGlmICghbG9vcCAmJiAhdHJhcHBlZCkgcmV0dXJuO1xuICAgICAgaWYgKGZvY3VzU2NvcGUucGF1c2VkKSByZXR1cm47XG5cbiAgICAgIGNvbnN0IGlzVGFiS2V5ID0gZXZlbnQua2V5ID09PSAnVGFiJyAmJiAhZXZlbnQuYWx0S2V5ICYmICFldmVudC5jdHJsS2V5ICYmICFldmVudC5tZXRhS2V5O1xuICAgICAgY29uc3QgZm9jdXNlZEVsZW1lbnQgPSBkb2N1bWVudC5hY3RpdmVFbGVtZW50IGFzIEhUTUxFbGVtZW50IHwgbnVsbDtcblxuICAgICAgaWYgKGlzVGFiS2V5ICYmIGZvY3VzZWRFbGVtZW50KSB7XG4gICAgICAgIGNvbnN0IGNvbnRhaW5lciA9IGV2ZW50LmN1cnJlbnRUYXJnZXQgYXMgSFRNTEVsZW1lbnQ7XG4gICAgICAgIGNvbnN0IFtmaXJzdCwgbGFzdF0gPSBnZXRUYWJiYWJsZUVkZ2VzKGNvbnRhaW5lcik7XG4gICAgICAgIGNvbnN0IGhhc1RhYmJhYmxlRWxlbWVudHNJbnNpZGUgPSBmaXJzdCAmJiBsYXN0O1xuXG4gICAgICAgIC8vIHdlIGNhbiBvbmx5IHdyYXAgZm9jdXMgaWYgd2UgaGF2ZSB0YWJiYWJsZSBlZGdlc1xuICAgICAgICBpZiAoIWhhc1RhYmJhYmxlRWxlbWVudHNJbnNpZGUpIHtcbiAgICAgICAgICBpZiAoZm9jdXNlZEVsZW1lbnQgPT09IGNvbnRhaW5lcikgZXZlbnQucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBpZiAoIWV2ZW50LnNoaWZ0S2V5ICYmIGZvY3VzZWRFbGVtZW50ID09PSBsYXN0KSB7XG4gICAgICAgICAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgICAgICAgaWYgKGxvb3ApIGZvY3VzKGZpcnN0LCB7IHNlbGVjdDogdHJ1ZSB9KTtcbiAgICAgICAgICB9IGVsc2UgaWYgKGV2ZW50LnNoaWZ0S2V5ICYmIGZvY3VzZWRFbGVtZW50ID09PSBmaXJzdCkge1xuICAgICAgICAgICAgZXZlbnQucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgICAgIGlmIChsb29wKSBmb2N1cyhsYXN0LCB7IHNlbGVjdDogdHJ1ZSB9KTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9LFxuICAgIFtsb29wLCB0cmFwcGVkLCBmb2N1c1Njb3BlLnBhdXNlZF1cbiAgKTtcblxuICByZXR1cm4gKFxuICAgIDxQcmltaXRpdmUuZGl2IHRhYkluZGV4PXstMX0gey4uLnNjb3BlUHJvcHN9IHJlZj17Y29tcG9zZWRSZWZzfSBvbktleURvd249e2hhbmRsZUtleURvd259IC8+XG4gICk7XG59KTtcblxuRm9jdXNTY29wZS5kaXNwbGF5TmFtZSA9IEZPQ1VTX1NDT1BFX05BTUU7XG5cbi8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbiAqIFV0aWxzXG4gKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5cbi8qKlxuICogQXR0ZW1wdHMgZm9jdXNpbmcgdGhlIGZpcnN0IGVsZW1lbnQgaW4gYSBsaXN0IG9mIGNhbmRpZGF0ZXMuXG4gKiBTdG9wcyB3aGVuIGZvY3VzIGhhcyBhY3R1YWxseSBtb3ZlZC5cbiAqL1xuZnVuY3Rpb24gZm9jdXNGaXJzdChjYW5kaWRhdGVzOiBIVE1MRWxlbWVudFtdLCB7IHNlbGVjdCA9IGZhbHNlIH0gPSB7fSkge1xuICBjb25zdCBwcmV2aW91c2x5Rm9jdXNlZEVsZW1lbnQgPSBkb2N1bWVudC5hY3RpdmVFbGVtZW50O1xuICBmb3IgKGNvbnN0IGNhbmRpZGF0ZSBvZiBjYW5kaWRhdGVzKSB7XG4gICAgZm9jdXMoY2FuZGlkYXRlLCB7IHNlbGVjdCB9KTtcbiAgICBpZiAoZG9jdW1lbnQuYWN0aXZlRWxlbWVudCAhPT0gcHJldmlvdXNseUZvY3VzZWRFbGVtZW50KSByZXR1cm47XG4gIH1cbn1cblxuLyoqXG4gKiBSZXR1cm5zIHRoZSBmaXJzdCBhbmQgbGFzdCB0YWJiYWJsZSBlbGVtZW50cyBpbnNpZGUgYSBjb250YWluZXIuXG4gKi9cbmZ1bmN0aW9uIGdldFRhYmJhYmxlRWRnZXMoY29udGFpbmVyOiBIVE1MRWxlbWVudCkge1xuICBjb25zdCBjYW5kaWRhdGVzID0gZ2V0VGFiYmFibGVDYW5kaWRhdGVzKGNvbnRhaW5lcik7XG4gIGNvbnN0IGZpcnN0ID0gZmluZFZpc2libGUoY2FuZGlkYXRlcywgY29udGFpbmVyKTtcbiAgY29uc3QgbGFzdCA9IGZpbmRWaXNpYmxlKGNhbmRpZGF0ZXMucmV2ZXJzZSgpLCBjb250YWluZXIpO1xuICByZXR1cm4gW2ZpcnN0LCBsYXN0XSBhcyBjb25zdDtcbn1cblxuLyoqXG4gKiBSZXR1cm5zIGEgbGlzdCBvZiBwb3RlbnRpYWwgdGFiYmFibGUgY2FuZGlkYXRlcy5cbiAqXG4gKiBOT1RFOiBUaGlzIGlzIG9ubHkgYSBjbG9zZSBhcHByb3hpbWF0aW9uLiBGb3IgZXhhbXBsZSBpdCBkb2Vzbid0IHRha2UgaW50byBhY2NvdW50IGNhc2VzIGxpa2Ugd2hlblxuICogZWxlbWVudHMgYXJlIG5vdCB2aXNpYmxlLiBUaGlzIGNhbm5vdCBiZSB3b3JrZWQgb3V0IGVhc2lseSBieSBqdXN0IHJlYWRpbmcgYSBwcm9wZXJ0eSwgYnV0IHJhdGhlclxuICogbmVjZXNzaXRhdGUgcnVudGltZSBrbm93bGVkZ2UgKGNvbXB1dGVkIHN0eWxlcywgZXRjKS4gV2UgZGVhbCB3aXRoIHRoZXNlIGNhc2VzIHNlcGFyYXRlbHkuXG4gKlxuICogU2VlOiBodHRwczovL2RldmVsb3Blci5tb3ppbGxhLm9yZy9lbi1VUy9kb2NzL1dlYi9BUEkvVHJlZVdhbGtlclxuICogQ3JlZGl0OiBodHRwczovL2dpdGh1Yi5jb20vZGlzY29yZC9mb2N1cy1sYXllcnMvYmxvYi9tYXN0ZXIvc3JjL3V0aWwvd3JhcEZvY3VzLnRzeCNMMVxuICovXG5mdW5jdGlvbiBnZXRUYWJiYWJsZUNhbmRpZGF0ZXMoY29udGFpbmVyOiBIVE1MRWxlbWVudCkge1xuICBjb25zdCBub2RlczogSFRNTEVsZW1lbnRbXSA9IFtdO1xuICBjb25zdCB3YWxrZXIgPSBkb2N1bWVudC5jcmVhdGVUcmVlV2Fsa2VyKGNvbnRhaW5lciwgTm9kZUZpbHRlci5TSE9XX0VMRU1FTlQsIHtcbiAgICBhY2NlcHROb2RlOiAobm9kZTogYW55KSA9PiB7XG4gICAgICBjb25zdCBpc0hpZGRlbklucHV0ID0gbm9kZS50YWdOYW1lID09PSAnSU5QVVQnICYmIG5vZGUudHlwZSA9PT0gJ2hpZGRlbic7XG4gICAgICBpZiAobm9kZS5kaXNhYmxlZCB8fCBub2RlLmhpZGRlbiB8fCBpc0hpZGRlbklucHV0KSByZXR1cm4gTm9kZUZpbHRlci5GSUxURVJfU0tJUDtcbiAgICAgIC8vIGAudGFiSW5kZXhgIGlzIG5vdCB0aGUgc2FtZSBhcyB0aGUgYHRhYmluZGV4YCBhdHRyaWJ1dGUuIEl0IHdvcmtzIG9uIHRoZVxuICAgICAgLy8gcnVudGltZSdzIHVuZGVyc3RhbmRpbmcgb2YgdGFiYmFiaWxpdHksIHNvIHRoaXMgYXV0b21hdGljYWxseSBhY2NvdW50c1xuICAgICAgLy8gZm9yIGFueSBraW5kIG9mIGVsZW1lbnQgdGhhdCBjb3VsZCBiZSB0YWJiZWQgdG8uXG4gICAgICByZXR1cm4gbm9kZS50YWJJbmRleCA+PSAwID8gTm9kZUZpbHRlci5GSUxURVJfQUNDRVBUIDogTm9kZUZpbHRlci5GSUxURVJfU0tJUDtcbiAgICB9LFxuICB9KTtcbiAgd2hpbGUgKHdhbGtlci5uZXh0Tm9kZSgpKSBub2Rlcy5wdXNoKHdhbGtlci5jdXJyZW50Tm9kZSBhcyBIVE1MRWxlbWVudCk7XG4gIC8vIHdlIGRvIG5vdCB0YWtlIGludG8gYWNjb3VudCB0aGUgb3JkZXIgb2Ygbm9kZXMgd2l0aCBwb3NpdGl2ZSBgdGFiSW5kZXhgIGFzIGl0XG4gIC8vIGhpbmRlcnMgYWNjZXNzaWJpbGl0eSB0byBoYXZlIHRhYiBvcmRlciBkaWZmZXJlbnQgZnJvbSB2aXN1YWwgb3JkZXIuXG4gIHJldHVybiBub2Rlcztcbn1cblxuLyoqXG4gKiBSZXR1cm5zIHRoZSBmaXJzdCB2aXNpYmxlIGVsZW1lbnQgaW4gYSBsaXN0LlxuICogTk9URTogT25seSBjaGVja3MgdmlzaWJpbGl0eSB1cCB0byB0aGUgYGNvbnRhaW5lcmAuXG4gKi9cbmZ1bmN0aW9uIGZpbmRWaXNpYmxlKGVsZW1lbnRzOiBIVE1MRWxlbWVudFtdLCBjb250YWluZXI6IEhUTUxFbGVtZW50KSB7XG4gIGZvciAoY29uc3QgZWxlbWVudCBvZiBlbGVtZW50cykge1xuICAgIC8vIHdlIHN0b3AgY2hlY2tpbmcgaWYgaXQncyBoaWRkZW4gYXQgdGhlIGBjb250YWluZXJgIGxldmVsIChleGNsdWRpbmcpXG4gICAgaWYgKCFpc0hpZGRlbihlbGVtZW50LCB7IHVwVG86IGNvbnRhaW5lciB9KSkgcmV0dXJuIGVsZW1lbnQ7XG4gIH1cbn1cblxuZnVuY3Rpb24gaXNIaWRkZW4obm9kZTogSFRNTEVsZW1lbnQsIHsgdXBUbyB9OiB7IHVwVG8/OiBIVE1MRWxlbWVudCB9KSB7XG4gIGlmIChnZXRDb21wdXRlZFN0eWxlKG5vZGUpLnZpc2liaWxpdHkgPT09ICdoaWRkZW4nKSByZXR1cm4gdHJ1ZTtcbiAgd2hpbGUgKG5vZGUpIHtcbiAgICAvLyB3ZSBzdG9wIGF0IGB1cFRvYCAoZXhjbHVkaW5nIGl0KVxuICAgIGlmICh1cFRvICE9PSB1bmRlZmluZWQgJiYgbm9kZSA9PT0gdXBUbykgcmV0dXJuIGZhbHNlO1xuICAgIGlmIChnZXRDb21wdXRlZFN0eWxlKG5vZGUpLmRpc3BsYXkgPT09ICdub25lJykgcmV0dXJuIHRydWU7XG4gICAgbm9kZSA9IG5vZGUucGFyZW50RWxlbWVudCBhcyBIVE1MRWxlbWVudDtcbiAgfVxuICByZXR1cm4gZmFsc2U7XG59XG5cbmZ1bmN0aW9uIGlzU2VsZWN0YWJsZUlucHV0KGVsZW1lbnQ6IGFueSk6IGVsZW1lbnQgaXMgRm9jdXNhYmxlVGFyZ2V0ICYgeyBzZWxlY3Q6ICgpID0+IHZvaWQgfSB7XG4gIHJldHVybiBlbGVtZW50IGluc3RhbmNlb2YgSFRNTElucHV0RWxlbWVudCAmJiAnc2VsZWN0JyBpbiBlbGVtZW50O1xufVxuXG5mdW5jdGlvbiBmb2N1cyhlbGVtZW50PzogRm9jdXNhYmxlVGFyZ2V0IHwgbnVsbCwgeyBzZWxlY3QgPSBmYWxzZSB9ID0ge30pIHtcbiAgLy8gb25seSBmb2N1cyBpZiB0aGF0IGVsZW1lbnQgaXMgZm9jdXNhYmxlXG4gIGlmIChlbGVtZW50ICYmIGVsZW1lbnQuZm9jdXMpIHtcbiAgICBjb25zdCBwcmV2aW91c2x5Rm9jdXNlZEVsZW1lbnQgPSBkb2N1bWVudC5hY3RpdmVFbGVtZW50O1xuICAgIC8vIE5PVEU6IHdlIHByZXZlbnQgc2Nyb2xsaW5nIG9uIGZvY3VzLCB0byBtaW5pbWl6ZSBqYXJyaW5nIHRyYW5zaXRpb25zIGZvciB1c2Vyc1xuICAgIGVsZW1lbnQuZm9jdXMoeyBwcmV2ZW50U2Nyb2xsOiB0cnVlIH0pO1xuICAgIC8vIG9ubHkgc2VsZWN0IGlmIGl0cyBub3QgdGhlIHNhbWUgZWxlbWVudCwgaXQgc3VwcG9ydHMgc2VsZWN0aW9uIGFuZCB3ZSBuZWVkIHRvIHNlbGVjdFxuICAgIGlmIChlbGVtZW50ICE9PSBwcmV2aW91c2x5Rm9jdXNlZEVsZW1lbnQgJiYgaXNTZWxlY3RhYmxlSW5wdXQoZWxlbWVudCkgJiYgc2VsZWN0KVxuICAgICAgZWxlbWVudC5zZWxlY3QoKTtcbiAgfVxufVxuXG4vKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4gKiBGb2N1c1Njb3BlIHN0YWNrXG4gKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5cbnR5cGUgRm9jdXNTY29wZUFQSSA9IHsgcGF1c2VkOiBib29sZWFuOyBwYXVzZSgpOiB2b2lkOyByZXN1bWUoKTogdm9pZCB9O1xuY29uc3QgZm9jdXNTY29wZXNTdGFjayA9IGNyZWF0ZUZvY3VzU2NvcGVzU3RhY2soKTtcblxuZnVuY3Rpb24gY3JlYXRlRm9jdXNTY29wZXNTdGFjaygpIHtcbiAgLyoqIEEgc3RhY2sgb2YgZm9jdXMgc2NvcGVzLCB3aXRoIHRoZSBhY3RpdmUgb25lIGF0IHRoZSB0b3AgKi9cbiAgbGV0IHN0YWNrOiBGb2N1c1Njb3BlQVBJW10gPSBbXTtcblxuICByZXR1cm4ge1xuICAgIGFkZChmb2N1c1Njb3BlOiBGb2N1c1Njb3BlQVBJKSB7XG4gICAgICAvLyBwYXVzZSB0aGUgY3VycmVudGx5IGFjdGl2ZSBmb2N1cyBzY29wZSAoYXQgdGhlIHRvcCBvZiB0aGUgc3RhY2spXG4gICAgICBjb25zdCBhY3RpdmVGb2N1c1Njb3BlID0gc3RhY2tbMF07XG4gICAgICBpZiAoZm9jdXNTY29wZSAhPT0gYWN0aXZlRm9jdXNTY29wZSkge1xuICAgICAgICBhY3RpdmVGb2N1c1Njb3BlPy5wYXVzZSgpO1xuICAgICAgfVxuICAgICAgLy8gcmVtb3ZlIGluIGNhc2UgaXQgYWxyZWFkeSBleGlzdHMgKGJlY2F1c2Ugd2UnbGwgcmUtYWRkIGl0IGF0IHRoZSB0b3Agb2YgdGhlIHN0YWNrKVxuICAgICAgc3RhY2sgPSBhcnJheVJlbW92ZShzdGFjaywgZm9jdXNTY29wZSk7XG4gICAgICBzdGFjay51bnNoaWZ0KGZvY3VzU2NvcGUpO1xuICAgIH0sXG5cbiAgICByZW1vdmUoZm9jdXNTY29wZTogRm9jdXNTY29wZUFQSSkge1xuICAgICAgc3RhY2sgPSBhcnJheVJlbW92ZShzdGFjaywgZm9jdXNTY29wZSk7XG4gICAgICBzdGFja1swXT8ucmVzdW1lKCk7XG4gICAgfSxcbiAgfTtcbn1cblxuZnVuY3Rpb24gYXJyYXlSZW1vdmU8VD4oYXJyYXk6IFRbXSwgaXRlbTogVCkge1xuICBjb25zdCB1cGRhdGVkQXJyYXkgPSBbLi4uYXJyYXldO1xuICBjb25zdCBpbmRleCA9IHVwZGF0ZWRBcnJheS5pbmRleE9mKGl0ZW0pO1xuICBpZiAoaW5kZXggIT09IC0xKSB7XG4gICAgdXBkYXRlZEFycmF5LnNwbGljZShpbmRleCwgMSk7XG4gIH1cbiAgcmV0dXJuIHVwZGF0ZWRBcnJheTtcbn1cblxuZnVuY3Rpb24gcmVtb3ZlTGlua3MoaXRlbXM6IEhUTUxFbGVtZW50W10pIHtcbiAgcmV0dXJuIGl0ZW1zLmZpbHRlcigoaXRlbSkgPT4gaXRlbS50YWdOYW1lICE9PSAnQScpO1xufVxuXG5jb25zdCBSb290ID0gRm9jdXNTY29wZTtcblxuZXhwb3J0IHtcbiAgRm9jdXNTY29wZSxcbiAgLy9cbiAgUm9vdCxcbn07XG5leHBvcnQgdHlwZSB7IEZvY3VzU2NvcGVQcm9wcyB9O1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlQ29tcG9zZWRSZWZzIiwiUHJpbWl0aXZlIiwidXNlQ2FsbGJhY2tSZWYiLCJqc3giLCJBVVRPRk9DVVNfT05fTU9VTlQiLCJBVVRPRk9DVVNfT05fVU5NT1VOVCIsIkVWRU5UX09QVElPTlMiLCJidWJibGVzIiwiY2FuY2VsYWJsZSIsIkZPQ1VTX1NDT1BFX05BTUUiLCJGb2N1c1Njb3BlIiwiZm9yd2FyZFJlZiIsInByb3BzIiwiZm9yd2FyZGVkUmVmIiwibG9vcCIsInRyYXBwZWQiLCJvbk1vdW50QXV0b0ZvY3VzIiwib25Nb3VudEF1dG9Gb2N1c1Byb3AiLCJvblVubW91bnRBdXRvRm9jdXMiLCJvblVubW91bnRBdXRvRm9jdXNQcm9wIiwic2NvcGVQcm9wcyIsImNvbnRhaW5lciIsInNldENvbnRhaW5lciIsInVzZVN0YXRlIiwibGFzdEZvY3VzZWRFbGVtZW50UmVmIiwidXNlUmVmIiwiY29tcG9zZWRSZWZzIiwibm9kZSIsImZvY3VzU2NvcGUiLCJwYXVzZWQiLCJwYXVzZSIsInJlc3VtZSIsImN1cnJlbnQiLCJ1c2VFZmZlY3QiLCJoYW5kbGVGb2N1c0luIiwiZXZlbnQiLCJ0YXJnZXQiLCJjb250YWlucyIsImZvY3VzIiwic2VsZWN0IiwiaGFuZGxlRm9jdXNPdXQiLCJyZWxhdGVkVGFyZ2V0IiwiaGFuZGxlTXV0YXRpb25zIiwibXV0YXRpb25zIiwiZm9jdXNlZEVsZW1lbnQiLCJkb2N1bWVudCIsImFjdGl2ZUVsZW1lbnQiLCJib2R5IiwibXV0YXRpb24iLCJyZW1vdmVkTm9kZXMiLCJsZW5ndGgiLCJhZGRFdmVudExpc3RlbmVyIiwibXV0YXRpb25PYnNlcnZlciIsIk11dGF0aW9uT2JzZXJ2ZXIiLCJvYnNlcnZlIiwiY2hpbGRMaXN0Iiwic3VidHJlZSIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJkaXNjb25uZWN0IiwiZm9jdXNTY29wZXNTdGFjayIsImFkZCIsInByZXZpb3VzbHlGb2N1c2VkRWxlbWVudCIsImhhc0ZvY3VzZWRDYW5kaWRhdGUiLCJtb3VudEV2ZW50IiwiQ3VzdG9tRXZlbnQiLCJkaXNwYXRjaEV2ZW50IiwiZGVmYXVsdFByZXZlbnRlZCIsImZvY3VzRmlyc3QiLCJyZW1vdmVMaW5rcyIsImdldFRhYmJhYmxlQ2FuZGlkYXRlcyIsInNldFRpbWVvdXQiLCJ1bm1vdW50RXZlbnQiLCJyZW1vdmUiLCJoYW5kbGVLZXlEb3duIiwidXNlQ2FsbGJhY2siLCJpc1RhYktleSIsImtleSIsImFsdEtleSIsImN0cmxLZXkiLCJtZXRhS2V5IiwiY3VycmVudFRhcmdldCIsImZpcnN0IiwibGFzdCIsImdldFRhYmJhYmxlRWRnZXMiLCJoYXNUYWJiYWJsZUVsZW1lbnRzSW5zaWRlIiwicHJldmVudERlZmF1bHQiLCJzaGlmdEtleSIsImRpdiIsInRhYkluZGV4IiwicmVmIiwib25LZXlEb3duIiwiZGlzcGxheU5hbWUiLCJjYW5kaWRhdGVzIiwiY2FuZGlkYXRlIiwiZmluZFZpc2libGUiLCJyZXZlcnNlIiwibm9kZXMiLCJ3YWxrZXIiLCJjcmVhdGVUcmVlV2Fsa2VyIiwiTm9kZUZpbHRlciIsIlNIT1dfRUxFTUVOVCIsImFjY2VwdE5vZGUiLCJpc0hpZGRlbklucHV0IiwidGFnTmFtZSIsInR5cGUiLCJkaXNhYmxlZCIsImhpZGRlbiIsIkZJTFRFUl9TS0lQIiwiRklMVEVSX0FDQ0VQVCIsIm5leHROb2RlIiwicHVzaCIsImN1cnJlbnROb2RlIiwiZWxlbWVudHMiLCJlbGVtZW50IiwiaXNIaWRkZW4iLCJ1cFRvIiwiZ2V0Q29tcHV0ZWRTdHlsZSIsInZpc2liaWxpdHkiLCJkaXNwbGF5IiwicGFyZW50RWxlbWVudCIsImlzU2VsZWN0YWJsZUlucHV0IiwiSFRNTElucHV0RWxlbWVudCIsInByZXZlbnRTY3JvbGwiLCJjcmVhdGVGb2N1c1Njb3Blc1N0YWNrIiwic3RhY2siLCJhY3RpdmVGb2N1c1Njb3BlIiwiYXJyYXlSZW1vdmUiLCJ1bnNoaWZ0IiwiYXJyYXkiLCJpdGVtIiwidXBkYXRlZEFycmF5IiwiaW5kZXgiLCJpbmRleE9mIiwic3BsaWNlIiwiaXRlbXMiLCJmaWx0ZXIiLCJSb290Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-focus-scope/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-id/dist/index.mjs":
/*!********************************************************!*\
  !*** ./node_modules/@radix-ui/react-id/dist/index.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useId: () => (/* binding */ useId)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n// packages/react/id/src/id.tsx\n\n\nvar useReactId = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\"useId\".toString()] || (() => void 0);\nvar count = 0;\nfunction useId(deterministicId) {\n  const [id, setId] = react__WEBPACK_IMPORTED_MODULE_0__.useState(useReactId());\n  (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(() => {\n    if (!deterministicId) setId((reactId) => reactId ?? String(count++));\n  }, [deterministicId]);\n  return deterministicId || (id ? `radix-${id}` : \"\");\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWlkL2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTtBQUMrQjtBQUNxQztBQUNwRSxpQkFBaUIseUxBQUs7QUFDdEI7QUFDQTtBQUNBLHNCQUFzQiwyQ0FBYztBQUNwQyxFQUFFLGtGQUFlO0FBQ2pCO0FBQ0EsR0FBRztBQUNILDJDQUEyQyxHQUFHO0FBQzlDO0FBR0U7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL2Fua2tvci8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtaWQvZGlzdC9pbmRleC5tanM/NGMwNyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC9pZC9zcmMvaWQudHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IHVzZUxheW91dEVmZmVjdCB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtdXNlLWxheW91dC1lZmZlY3RcIjtcbnZhciB1c2VSZWFjdElkID0gUmVhY3RbXCJ1c2VJZFwiLnRvU3RyaW5nKCldIHx8ICgoKSA9PiB2b2lkIDApO1xudmFyIGNvdW50ID0gMDtcbmZ1bmN0aW9uIHVzZUlkKGRldGVybWluaXN0aWNJZCkge1xuICBjb25zdCBbaWQsIHNldElkXSA9IFJlYWN0LnVzZVN0YXRlKHVzZVJlYWN0SWQoKSk7XG4gIHVzZUxheW91dEVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCFkZXRlcm1pbmlzdGljSWQpIHNldElkKChyZWFjdElkKSA9PiByZWFjdElkID8/IFN0cmluZyhjb3VudCsrKSk7XG4gIH0sIFtkZXRlcm1pbmlzdGljSWRdKTtcbiAgcmV0dXJuIGRldGVybWluaXN0aWNJZCB8fCAoaWQgPyBgcmFkaXgtJHtpZH1gIDogXCJcIik7XG59XG5leHBvcnQge1xuICB1c2VJZFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-id/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@radix-ui/react-portal/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Portal,Root auto */ // packages/react/portal/src/portal.tsx\n\n\n\n\n\nvar PORTAL_NAME = \"Portal\";\nvar Portal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { container: containerProp, ...portalProps } = props;\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__.useLayoutEffect)(()=>setMounted(true), []);\n    const container = containerProp || mounted && globalThis?.document?.body;\n    return container ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...portalProps,\n        ref: forwardedRef\n    }), container) : null;\n});\nPortal.displayName = PORTAL_NAME;\nvar Root = Portal;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@radix-ui/react-presence/dist/index.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Presence: () => (/* binding */ Presence)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Presence auto */ // packages/react/presence/src/Presence.tsx\n\n\n\n// packages/react/presence/src/useStateMachine.tsx\n\nfunction useStateMachine(initialState, machine) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useReducer((state, event)=>{\n        const nextState = machine[state][event];\n        return nextState ?? state;\n    }, initialState);\n}\n// packages/react/presence/src/Presence.tsx\nvar Presence = (props)=>{\n    const { present, children } = props;\n    const presence = usePresence(present);\n    const child = typeof children === \"function\" ? children({\n        present: presence.isPresent\n    }) : react__WEBPACK_IMPORTED_MODULE_0__.Children.only(children);\n    const ref = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__.useComposedRefs)(presence.ref, getElementRef(child));\n    const forceMount = typeof children === \"function\";\n    return forceMount || presence.isPresent ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(child, {\n        ref\n    }) : null;\n};\nPresence.displayName = \"Presence\";\nfunction usePresence(present) {\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const stylesRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef({});\n    const prevPresentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(present);\n    const prevAnimationNameRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"none\");\n    const initialState = present ? \"mounted\" : \"unmounted\";\n    const [state, send] = useStateMachine(initialState, {\n        mounted: {\n            UNMOUNT: \"unmounted\",\n            ANIMATION_OUT: \"unmountSuspended\"\n        },\n        unmountSuspended: {\n            MOUNT: \"mounted\",\n            ANIMATION_END: \"unmounted\"\n        },\n        unmounted: {\n            MOUNT: \"mounted\"\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const currentAnimationName = getAnimationName(stylesRef.current);\n        prevAnimationNameRef.current = state === \"mounted\" ? currentAnimationName : \"none\";\n    }, [\n        state\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)(()=>{\n        const styles = stylesRef.current;\n        const wasPresent = prevPresentRef.current;\n        const hasPresentChanged = wasPresent !== present;\n        if (hasPresentChanged) {\n            const prevAnimationName = prevAnimationNameRef.current;\n            const currentAnimationName = getAnimationName(styles);\n            if (present) {\n                send(\"MOUNT\");\n            } else if (currentAnimationName === \"none\" || styles?.display === \"none\") {\n                send(\"UNMOUNT\");\n            } else {\n                const isAnimating = prevAnimationName !== currentAnimationName;\n                if (wasPresent && isAnimating) {\n                    send(\"ANIMATION_OUT\");\n                } else {\n                    send(\"UNMOUNT\");\n                }\n            }\n            prevPresentRef.current = present;\n        }\n    }, [\n        present,\n        send\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)(()=>{\n        if (node) {\n            let timeoutId;\n            const ownerWindow = node.ownerDocument.defaultView ?? window;\n            const handleAnimationEnd = (event)=>{\n                const currentAnimationName = getAnimationName(stylesRef.current);\n                const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n                if (event.target === node && isCurrentAnimation) {\n                    send(\"ANIMATION_END\");\n                    if (!prevPresentRef.current) {\n                        const currentFillMode = node.style.animationFillMode;\n                        node.style.animationFillMode = \"forwards\";\n                        timeoutId = ownerWindow.setTimeout(()=>{\n                            if (node.style.animationFillMode === \"forwards\") {\n                                node.style.animationFillMode = currentFillMode;\n                            }\n                        });\n                    }\n                }\n            };\n            const handleAnimationStart = (event)=>{\n                if (event.target === node) {\n                    prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n                }\n            };\n            node.addEventListener(\"animationstart\", handleAnimationStart);\n            node.addEventListener(\"animationcancel\", handleAnimationEnd);\n            node.addEventListener(\"animationend\", handleAnimationEnd);\n            return ()=>{\n                ownerWindow.clearTimeout(timeoutId);\n                node.removeEventListener(\"animationstart\", handleAnimationStart);\n                node.removeEventListener(\"animationcancel\", handleAnimationEnd);\n                node.removeEventListener(\"animationend\", handleAnimationEnd);\n            };\n        } else {\n            send(\"ANIMATION_END\");\n        }\n    }, [\n        node,\n        send\n    ]);\n    return {\n        isPresent: [\n            \"mounted\",\n            \"unmountSuspended\"\n        ].includes(state),\n        ref: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node2)=>{\n            if (node2) stylesRef.current = getComputedStyle(node2);\n            setNode(node2);\n        }, [])\n    };\n}\nfunction getAnimationName(styles) {\n    return styles?.animationName || \"none\";\n}\nfunction getElementRef(element) {\n    let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n    let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.ref;\n    }\n    getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n    mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.props.ref;\n    }\n    return element.props.ref || element.ref;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-primitive/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Primitive: () => (/* binding */ Primitive),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   dispatchDiscreteCustomEvent: () => (/* binding */ dispatchDiscreteCustomEvent)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/primitive/src/primitive.tsx\n\n\n\n\nvar NODES = [\n  \"a\",\n  \"button\",\n  \"div\",\n  \"form\",\n  \"h2\",\n  \"h3\",\n  \"img\",\n  \"input\",\n  \"label\",\n  \"li\",\n  \"nav\",\n  \"ol\",\n  \"p\",\n  \"span\",\n  \"svg\",\n  \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node) => {\n  const Node = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.Slot : node;\n    if (typeof window !== \"undefined\") {\n      window[Symbol.for(\"radix-ui\")] = true;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Comp, { ...primitiveProps, ref: forwardedRef });\n  });\n  Node.displayName = `Primitive.${node}`;\n  return { ...primitive, [node]: Node };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n  if (target) react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(() => target.dispatchEvent(event));\n}\nvar Root = Primitive;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@radix-ui/react-slot/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Slot: () => (/* binding */ Slot),\n/* harmony export */   Slottable: () => (/* binding */ Slottable)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/slot/src/slot.tsx\n\n\n\nvar Slot = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  const childrenArray = react__WEBPACK_IMPORTED_MODULE_0__.Children.toArray(children);\n  const slottable = childrenArray.find(isSlottable);\n  if (slottable) {\n    const newElement = slottable.props.children;\n    const newChildren = childrenArray.map((child) => {\n      if (child === slottable) {\n        if (react__WEBPACK_IMPORTED_MODULE_0__.Children.count(newElement) > 1) return react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null);\n        return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? newElement.props.children : null;\n      } else {\n        return child;\n      }\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children: react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(newElement, void 0, newChildren) : null });\n  }\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children });\n});\nSlot.displayName = \"Slot\";\nvar SlotClone = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  if (react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(children)) {\n    const childrenRef = getElementRef(children);\n    const props2 = mergeProps(slotProps, children.props);\n    if (children.type !== react__WEBPACK_IMPORTED_MODULE_0__.Fragment) {\n      props2.ref = forwardedRef ? (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.composeRefs)(forwardedRef, childrenRef) : childrenRef;\n    }\n    return react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(children, props2);\n  }\n  return react__WEBPACK_IMPORTED_MODULE_0__.Children.count(children) > 1 ? react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null) : null;\n});\nSlotClone.displayName = \"SlotClone\";\nvar Slottable = ({ children }) => {\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, { children });\n};\nfunction isSlottable(child) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(child) && child.type === Slottable;\n}\nfunction mergeProps(slotProps, childProps) {\n  const overrideProps = { ...childProps };\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args) => {\n          childPropValue(...args);\n          slotPropValue(...args);\n        };\n      } else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    } else if (propName === \"style\") {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === \"className\") {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(\" \");\n    }\n  }\n  return { ...slotProps, ...overrideProps };\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\nvar Root = Slot;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXNsb3QvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQUE7QUFDK0I7QUFDNEI7QUFDSTtBQUMvRCxXQUFXLDZDQUFnQjtBQUMzQixVQUFVLHlCQUF5QjtBQUNuQyx3QkFBd0IsMkNBQWM7QUFDdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksMkNBQWMsK0JBQStCLDJDQUFjO0FBQ3ZFLGVBQWUsaURBQW9CO0FBQ25DLFFBQVE7QUFDUjtBQUNBO0FBQ0EsS0FBSztBQUNMLDJCQUEyQixzREFBRyxjQUFjLDJDQUEyQyxpREFBb0IsZUFBZSwrQ0FBa0IsMENBQTBDO0FBQ3RMO0FBQ0EseUJBQXlCLHNEQUFHLGNBQWMsMkNBQTJDO0FBQ3JGLENBQUM7QUFDRDtBQUNBLGdCQUFnQiw2Q0FBZ0I7QUFDaEMsVUFBVSx5QkFBeUI7QUFDbkMsTUFBTSxpREFBb0I7QUFDMUI7QUFDQTtBQUNBLDBCQUEwQiwyQ0FBYztBQUN4QyxrQ0FBa0MseUVBQVc7QUFDN0M7QUFDQSxXQUFXLCtDQUFrQjtBQUM3QjtBQUNBLFNBQVMsMkNBQWMsdUJBQXVCLDJDQUFjO0FBQzVELENBQUM7QUFDRDtBQUNBLG1CQUFtQixVQUFVO0FBQzdCLHlCQUF5QixzREFBRyxDQUFDLHVEQUFTLElBQUksVUFBVTtBQUNwRDtBQUNBO0FBQ0EsU0FBUyxpREFBb0I7QUFDN0I7QUFDQTtBQUNBLDBCQUEwQjtBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0EsTUFBTTtBQUNOLGtDQUFrQztBQUNsQyxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0EsV0FBVztBQUNYO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUtFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hbmtrb3IvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXNsb3QvZGlzdC9pbmRleC5tanM/MDIxMSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC9zbG90L3NyYy9zbG90LnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyBjb21wb3NlUmVmcyB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtY29tcG9zZS1yZWZzXCI7XG5pbXBvcnQgeyBGcmFnbWVudCBhcyBGcmFnbWVudDIsIGpzeCB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xudmFyIFNsb3QgPSBSZWFjdC5mb3J3YXJkUmVmKChwcm9wcywgZm9yd2FyZGVkUmVmKSA9PiB7XG4gIGNvbnN0IHsgY2hpbGRyZW4sIC4uLnNsb3RQcm9wcyB9ID0gcHJvcHM7XG4gIGNvbnN0IGNoaWxkcmVuQXJyYXkgPSBSZWFjdC5DaGlsZHJlbi50b0FycmF5KGNoaWxkcmVuKTtcbiAgY29uc3Qgc2xvdHRhYmxlID0gY2hpbGRyZW5BcnJheS5maW5kKGlzU2xvdHRhYmxlKTtcbiAgaWYgKHNsb3R0YWJsZSkge1xuICAgIGNvbnN0IG5ld0VsZW1lbnQgPSBzbG90dGFibGUucHJvcHMuY2hpbGRyZW47XG4gICAgY29uc3QgbmV3Q2hpbGRyZW4gPSBjaGlsZHJlbkFycmF5Lm1hcCgoY2hpbGQpID0+IHtcbiAgICAgIGlmIChjaGlsZCA9PT0gc2xvdHRhYmxlKSB7XG4gICAgICAgIGlmIChSZWFjdC5DaGlsZHJlbi5jb3VudChuZXdFbGVtZW50KSA+IDEpIHJldHVybiBSZWFjdC5DaGlsZHJlbi5vbmx5KG51bGwpO1xuICAgICAgICByZXR1cm4gUmVhY3QuaXNWYWxpZEVsZW1lbnQobmV3RWxlbWVudCkgPyBuZXdFbGVtZW50LnByb3BzLmNoaWxkcmVuIDogbnVsbDtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHJldHVybiBjaGlsZDtcbiAgICAgIH1cbiAgICB9KTtcbiAgICByZXR1cm4gLyogQF9fUFVSRV9fICovIGpzeChTbG90Q2xvbmUsIHsgLi4uc2xvdFByb3BzLCByZWY6IGZvcndhcmRlZFJlZiwgY2hpbGRyZW46IFJlYWN0LmlzVmFsaWRFbGVtZW50KG5ld0VsZW1lbnQpID8gUmVhY3QuY2xvbmVFbGVtZW50KG5ld0VsZW1lbnQsIHZvaWQgMCwgbmV3Q2hpbGRyZW4pIDogbnVsbCB9KTtcbiAgfVxuICByZXR1cm4gLyogQF9fUFVSRV9fICovIGpzeChTbG90Q2xvbmUsIHsgLi4uc2xvdFByb3BzLCByZWY6IGZvcndhcmRlZFJlZiwgY2hpbGRyZW4gfSk7XG59KTtcblNsb3QuZGlzcGxheU5hbWUgPSBcIlNsb3RcIjtcbnZhciBTbG90Q2xvbmUgPSBSZWFjdC5mb3J3YXJkUmVmKChwcm9wcywgZm9yd2FyZGVkUmVmKSA9PiB7XG4gIGNvbnN0IHsgY2hpbGRyZW4sIC4uLnNsb3RQcm9wcyB9ID0gcHJvcHM7XG4gIGlmIChSZWFjdC5pc1ZhbGlkRWxlbWVudChjaGlsZHJlbikpIHtcbiAgICBjb25zdCBjaGlsZHJlblJlZiA9IGdldEVsZW1lbnRSZWYoY2hpbGRyZW4pO1xuICAgIGNvbnN0IHByb3BzMiA9IG1lcmdlUHJvcHMoc2xvdFByb3BzLCBjaGlsZHJlbi5wcm9wcyk7XG4gICAgaWYgKGNoaWxkcmVuLnR5cGUgIT09IFJlYWN0LkZyYWdtZW50KSB7XG4gICAgICBwcm9wczIucmVmID0gZm9yd2FyZGVkUmVmID8gY29tcG9zZVJlZnMoZm9yd2FyZGVkUmVmLCBjaGlsZHJlblJlZikgOiBjaGlsZHJlblJlZjtcbiAgICB9XG4gICAgcmV0dXJuIFJlYWN0LmNsb25lRWxlbWVudChjaGlsZHJlbiwgcHJvcHMyKTtcbiAgfVxuICByZXR1cm4gUmVhY3QuQ2hpbGRyZW4uY291bnQoY2hpbGRyZW4pID4gMSA/IFJlYWN0LkNoaWxkcmVuLm9ubHkobnVsbCkgOiBudWxsO1xufSk7XG5TbG90Q2xvbmUuZGlzcGxheU5hbWUgPSBcIlNsb3RDbG9uZVwiO1xudmFyIFNsb3R0YWJsZSA9ICh7IGNoaWxkcmVuIH0pID0+IHtcbiAgcmV0dXJuIC8qIEBfX1BVUkVfXyAqLyBqc3goRnJhZ21lbnQyLCB7IGNoaWxkcmVuIH0pO1xufTtcbmZ1bmN0aW9uIGlzU2xvdHRhYmxlKGNoaWxkKSB7XG4gIHJldHVybiBSZWFjdC5pc1ZhbGlkRWxlbWVudChjaGlsZCkgJiYgY2hpbGQudHlwZSA9PT0gU2xvdHRhYmxlO1xufVxuZnVuY3Rpb24gbWVyZ2VQcm9wcyhzbG90UHJvcHMsIGNoaWxkUHJvcHMpIHtcbiAgY29uc3Qgb3ZlcnJpZGVQcm9wcyA9IHsgLi4uY2hpbGRQcm9wcyB9O1xuICBmb3IgKGNvbnN0IHByb3BOYW1lIGluIGNoaWxkUHJvcHMpIHtcbiAgICBjb25zdCBzbG90UHJvcFZhbHVlID0gc2xvdFByb3BzW3Byb3BOYW1lXTtcbiAgICBjb25zdCBjaGlsZFByb3BWYWx1ZSA9IGNoaWxkUHJvcHNbcHJvcE5hbWVdO1xuICAgIGNvbnN0IGlzSGFuZGxlciA9IC9eb25bQS1aXS8udGVzdChwcm9wTmFtZSk7XG4gICAgaWYgKGlzSGFuZGxlcikge1xuICAgICAgaWYgKHNsb3RQcm9wVmFsdWUgJiYgY2hpbGRQcm9wVmFsdWUpIHtcbiAgICAgICAgb3ZlcnJpZGVQcm9wc1twcm9wTmFtZV0gPSAoLi4uYXJncykgPT4ge1xuICAgICAgICAgIGNoaWxkUHJvcFZhbHVlKC4uLmFyZ3MpO1xuICAgICAgICAgIHNsb3RQcm9wVmFsdWUoLi4uYXJncyk7XG4gICAgICAgIH07XG4gICAgICB9IGVsc2UgaWYgKHNsb3RQcm9wVmFsdWUpIHtcbiAgICAgICAgb3ZlcnJpZGVQcm9wc1twcm9wTmFtZV0gPSBzbG90UHJvcFZhbHVlO1xuICAgICAgfVxuICAgIH0gZWxzZSBpZiAocHJvcE5hbWUgPT09IFwic3R5bGVcIikge1xuICAgICAgb3ZlcnJpZGVQcm9wc1twcm9wTmFtZV0gPSB7IC4uLnNsb3RQcm9wVmFsdWUsIC4uLmNoaWxkUHJvcFZhbHVlIH07XG4gICAgfSBlbHNlIGlmIChwcm9wTmFtZSA9PT0gXCJjbGFzc05hbWVcIikge1xuICAgICAgb3ZlcnJpZGVQcm9wc1twcm9wTmFtZV0gPSBbc2xvdFByb3BWYWx1ZSwgY2hpbGRQcm9wVmFsdWVdLmZpbHRlcihCb29sZWFuKS5qb2luKFwiIFwiKTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIHsgLi4uc2xvdFByb3BzLCAuLi5vdmVycmlkZVByb3BzIH07XG59XG5mdW5jdGlvbiBnZXRFbGVtZW50UmVmKGVsZW1lbnQpIHtcbiAgbGV0IGdldHRlciA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IoZWxlbWVudC5wcm9wcywgXCJyZWZcIik/LmdldDtcbiAgbGV0IG1heVdhcm4gPSBnZXR0ZXIgJiYgXCJpc1JlYWN0V2FybmluZ1wiIGluIGdldHRlciAmJiBnZXR0ZXIuaXNSZWFjdFdhcm5pbmc7XG4gIGlmIChtYXlXYXJuKSB7XG4gICAgcmV0dXJuIGVsZW1lbnQucmVmO1xuICB9XG4gIGdldHRlciA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IoZWxlbWVudCwgXCJyZWZcIik/LmdldDtcbiAgbWF5V2FybiA9IGdldHRlciAmJiBcImlzUmVhY3RXYXJuaW5nXCIgaW4gZ2V0dGVyICYmIGdldHRlci5pc1JlYWN0V2FybmluZztcbiAgaWYgKG1heVdhcm4pIHtcbiAgICByZXR1cm4gZWxlbWVudC5wcm9wcy5yZWY7XG4gIH1cbiAgcmV0dXJuIGVsZW1lbnQucHJvcHMucmVmIHx8IGVsZW1lbnQucmVmO1xufVxudmFyIFJvb3QgPSBTbG90O1xuZXhwb3J0IHtcbiAgUm9vdCxcbiAgU2xvdCxcbiAgU2xvdHRhYmxlXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCallbackRef: () => (/* binding */ useCallbackRef)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-callback-ref/src/useCallbackRef.tsx\n\nfunction useCallbackRef(callback) {\n  const callbackRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(callback);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    callbackRef.current = callback;\n  });\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => (...args) => callbackRef.current?.(...args), []);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1jYWxsYmFjay1yZWYvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUMrQjtBQUMvQjtBQUNBLHNCQUFzQix5Q0FBWTtBQUNsQyxFQUFFLDRDQUFlO0FBQ2pCO0FBQ0EsR0FBRztBQUNILFNBQVMsMENBQWE7QUFDdEI7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW5ra29yLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC11c2UtY2FsbGJhY2stcmVmL2Rpc3QvaW5kZXgubWpzPzY3MGUiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvdXNlLWNhbGxiYWNrLXJlZi9zcmMvdXNlQ2FsbGJhY2tSZWYudHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmZ1bmN0aW9uIHVzZUNhbGxiYWNrUmVmKGNhbGxiYWNrKSB7XG4gIGNvbnN0IGNhbGxiYWNrUmVmID0gUmVhY3QudXNlUmVmKGNhbGxiYWNrKTtcbiAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICBjYWxsYmFja1JlZi5jdXJyZW50ID0gY2FsbGJhY2s7XG4gIH0pO1xuICByZXR1cm4gUmVhY3QudXNlTWVtbygoKSA9PiAoLi4uYXJncykgPT4gY2FsbGJhY2tSZWYuY3VycmVudD8uKC4uLmFyZ3MpLCBbXSk7XG59XG5leHBvcnQge1xuICB1c2VDYWxsYmFja1JlZlxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useControllableState: () => (/* binding */ useControllableState)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n// packages/react/use-controllable-state/src/useControllableState.tsx\n\n\nfunction useControllableState({\n  prop,\n  defaultProp,\n  onChange = () => {\n  }\n}) {\n  const [uncontrolledProp, setUncontrolledProp] = useUncontrolledState({ defaultProp, onChange });\n  const isControlled = prop !== void 0;\n  const value = isControlled ? prop : uncontrolledProp;\n  const handleChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__.useCallbackRef)(onChange);\n  const setValue = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n    (nextValue) => {\n      if (isControlled) {\n        const setter = nextValue;\n        const value2 = typeof nextValue === \"function\" ? setter(prop) : nextValue;\n        if (value2 !== prop) handleChange(value2);\n      } else {\n        setUncontrolledProp(nextValue);\n      }\n    },\n    [isControlled, prop, setUncontrolledProp, handleChange]\n  );\n  return [value, setValue];\n}\nfunction useUncontrolledState({\n  defaultProp,\n  onChange\n}) {\n  const uncontrolledState = react__WEBPACK_IMPORTED_MODULE_0__.useState(defaultProp);\n  const [value] = uncontrolledState;\n  const prevValueRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(value);\n  const handleChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__.useCallbackRef)(onChange);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      handleChange(value);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef, handleChange]);\n  return uncontrolledState;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1jb250cm9sbGFibGUtc3RhdGUvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7QUFDK0I7QUFDbUM7QUFDbEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRCx5RUFBeUUsdUJBQXVCO0FBQ2hHO0FBQ0E7QUFDQSx1QkFBdUIsZ0ZBQWM7QUFDckMsbUJBQW1CLDhDQUFpQjtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0QsNEJBQTRCLDJDQUFjO0FBQzFDO0FBQ0EsdUJBQXVCLHlDQUFZO0FBQ25DLHVCQUF1QixnRkFBYztBQUNyQyxFQUFFLDRDQUFlO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW5ra29yLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC11c2UtY29udHJvbGxhYmxlLXN0YXRlL2Rpc3QvaW5kZXgubWpzPzBkNTIiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvdXNlLWNvbnRyb2xsYWJsZS1zdGF0ZS9zcmMvdXNlQ29udHJvbGxhYmxlU3RhdGUudHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IHVzZUNhbGxiYWNrUmVmIH0gZnJvbSBcIkByYWRpeC11aS9yZWFjdC11c2UtY2FsbGJhY2stcmVmXCI7XG5mdW5jdGlvbiB1c2VDb250cm9sbGFibGVTdGF0ZSh7XG4gIHByb3AsXG4gIGRlZmF1bHRQcm9wLFxuICBvbkNoYW5nZSA9ICgpID0+IHtcbiAgfVxufSkge1xuICBjb25zdCBbdW5jb250cm9sbGVkUHJvcCwgc2V0VW5jb250cm9sbGVkUHJvcF0gPSB1c2VVbmNvbnRyb2xsZWRTdGF0ZSh7IGRlZmF1bHRQcm9wLCBvbkNoYW5nZSB9KTtcbiAgY29uc3QgaXNDb250cm9sbGVkID0gcHJvcCAhPT0gdm9pZCAwO1xuICBjb25zdCB2YWx1ZSA9IGlzQ29udHJvbGxlZCA/IHByb3AgOiB1bmNvbnRyb2xsZWRQcm9wO1xuICBjb25zdCBoYW5kbGVDaGFuZ2UgPSB1c2VDYWxsYmFja1JlZihvbkNoYW5nZSk7XG4gIGNvbnN0IHNldFZhbHVlID0gUmVhY3QudXNlQ2FsbGJhY2soXG4gICAgKG5leHRWYWx1ZSkgPT4ge1xuICAgICAgaWYgKGlzQ29udHJvbGxlZCkge1xuICAgICAgICBjb25zdCBzZXR0ZXIgPSBuZXh0VmFsdWU7XG4gICAgICAgIGNvbnN0IHZhbHVlMiA9IHR5cGVvZiBuZXh0VmFsdWUgPT09IFwiZnVuY3Rpb25cIiA/IHNldHRlcihwcm9wKSA6IG5leHRWYWx1ZTtcbiAgICAgICAgaWYgKHZhbHVlMiAhPT0gcHJvcCkgaGFuZGxlQ2hhbmdlKHZhbHVlMik7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBzZXRVbmNvbnRyb2xsZWRQcm9wKG5leHRWYWx1ZSk7XG4gICAgICB9XG4gICAgfSxcbiAgICBbaXNDb250cm9sbGVkLCBwcm9wLCBzZXRVbmNvbnRyb2xsZWRQcm9wLCBoYW5kbGVDaGFuZ2VdXG4gICk7XG4gIHJldHVybiBbdmFsdWUsIHNldFZhbHVlXTtcbn1cbmZ1bmN0aW9uIHVzZVVuY29udHJvbGxlZFN0YXRlKHtcbiAgZGVmYXVsdFByb3AsXG4gIG9uQ2hhbmdlXG59KSB7XG4gIGNvbnN0IHVuY29udHJvbGxlZFN0YXRlID0gUmVhY3QudXNlU3RhdGUoZGVmYXVsdFByb3ApO1xuICBjb25zdCBbdmFsdWVdID0gdW5jb250cm9sbGVkU3RhdGU7XG4gIGNvbnN0IHByZXZWYWx1ZVJlZiA9IFJlYWN0LnVzZVJlZih2YWx1ZSk7XG4gIGNvbnN0IGhhbmRsZUNoYW5nZSA9IHVzZUNhbGxiYWNrUmVmKG9uQ2hhbmdlKTtcbiAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAocHJldlZhbHVlUmVmLmN1cnJlbnQgIT09IHZhbHVlKSB7XG4gICAgICBoYW5kbGVDaGFuZ2UodmFsdWUpO1xuICAgICAgcHJldlZhbHVlUmVmLmN1cnJlbnQgPSB2YWx1ZTtcbiAgICB9XG4gIH0sIFt2YWx1ZSwgcHJldlZhbHVlUmVmLCBoYW5kbGVDaGFuZ2VdKTtcbiAgcmV0dXJuIHVuY29udHJvbGxlZFN0YXRlO1xufVxuZXhwb3J0IHtcbiAgdXNlQ29udHJvbGxhYmxlU3RhdGVcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEscapeKeydown: () => (/* binding */ useEscapeKeydown)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n// packages/react/use-escape-keydown/src/useEscapeKeydown.tsx\n\n\nfunction useEscapeKeydown(onEscapeKeyDownProp, ownerDocument = globalThis?.document) {\n  const onEscapeKeyDown = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__.useCallbackRef)(onEscapeKeyDownProp);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    const handleKeyDown = (event) => {\n      if (event.key === \"Escape\") {\n        onEscapeKeyDown(event);\n      }\n    };\n    ownerDocument.addEventListener(\"keydown\", handleKeyDown, { capture: true });\n    return () => ownerDocument.removeEventListener(\"keydown\", handleKeyDown, { capture: true });\n  }, [onEscapeKeyDown, ownerDocument]);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1lc2NhcGUta2V5ZG93bi9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUMrQjtBQUNtQztBQUNsRTtBQUNBLDBCQUEwQixnRkFBYztBQUN4QyxFQUFFLDRDQUFlO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrREFBK0QsZUFBZTtBQUM5RSwrRUFBK0UsZUFBZTtBQUM5RixHQUFHO0FBQ0g7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW5ra29yLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC11c2UtZXNjYXBlLWtleWRvd24vZGlzdC9pbmRleC5tanM/ZmQ0MyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC91c2UtZXNjYXBlLWtleWRvd24vc3JjL3VzZUVzY2FwZUtleWRvd24udHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IHVzZUNhbGxiYWNrUmVmIH0gZnJvbSBcIkByYWRpeC11aS9yZWFjdC11c2UtY2FsbGJhY2stcmVmXCI7XG5mdW5jdGlvbiB1c2VFc2NhcGVLZXlkb3duKG9uRXNjYXBlS2V5RG93blByb3AsIG93bmVyRG9jdW1lbnQgPSBnbG9iYWxUaGlzPy5kb2N1bWVudCkge1xuICBjb25zdCBvbkVzY2FwZUtleURvd24gPSB1c2VDYWxsYmFja1JlZihvbkVzY2FwZUtleURvd25Qcm9wKTtcbiAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBoYW5kbGVLZXlEb3duID0gKGV2ZW50KSA9PiB7XG4gICAgICBpZiAoZXZlbnQua2V5ID09PSBcIkVzY2FwZVwiKSB7XG4gICAgICAgIG9uRXNjYXBlS2V5RG93bihldmVudCk7XG4gICAgICB9XG4gICAgfTtcbiAgICBvd25lckRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoXCJrZXlkb3duXCIsIGhhbmRsZUtleURvd24sIHsgY2FwdHVyZTogdHJ1ZSB9KTtcbiAgICByZXR1cm4gKCkgPT4gb3duZXJEb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKFwia2V5ZG93blwiLCBoYW5kbGVLZXlEb3duLCB7IGNhcHR1cmU6IHRydWUgfSk7XG4gIH0sIFtvbkVzY2FwZUtleURvd24sIG93bmVyRG9jdW1lbnRdKTtcbn1cbmV4cG9ydCB7XG4gIHVzZUVzY2FwZUtleWRvd25cbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLayoutEffect: () => (/* binding */ useLayoutEffect2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-layout-effect/src/useLayoutEffect.tsx\n\nvar useLayoutEffect2 = Boolean(globalThis?.document) ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : () => {\n};\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0L2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDK0I7QUFDL0IsdURBQXVELGtEQUFxQjtBQUM1RTtBQUdFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hbmtrb3IvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0L2Rpc3QvaW5kZXgubWpzPzBiMGMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvdXNlLWxheW91dC1lZmZlY3Qvc3JjL3VzZUxheW91dEVmZmVjdC50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xudmFyIHVzZUxheW91dEVmZmVjdDIgPSBCb29sZWFuKGdsb2JhbFRoaXM/LmRvY3VtZW50KSA/IFJlYWN0LnVzZUxheW91dEVmZmVjdCA6ICgpID0+IHtcbn07XG5leHBvcnQge1xuICB1c2VMYXlvdXRFZmZlY3QyIGFzIHVzZUxheW91dEVmZmVjdFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\n");

/***/ })

};
;