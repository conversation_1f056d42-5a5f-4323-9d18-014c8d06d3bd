"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/graphql/route";
exports.ids = ["app/api/graphql/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgraphql%2Froute&page=%2Fapi%2Fgraphql%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgraphql%2Froute.ts&appDir=E%3A%5Cankkorwoo%5Cankkor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cankkorwoo%5Cankkor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgraphql%2Froute&page=%2Fapi%2Fgraphql%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgraphql%2Froute.ts&appDir=E%3A%5Cankkorwoo%5Cankkor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cankkorwoo%5Cankkor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var E_ankkorwoo_ankkor_src_app_api_graphql_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/graphql/route.ts */ \"(rsc)/./src/app/api/graphql/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/graphql/route\",\n        pathname: \"/api/graphql\",\n        filename: \"route\",\n        bundlePath: \"app/api/graphql/route\"\n    },\n    resolvedPagePath: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\api\\\\graphql\\\\route.ts\",\n    nextConfigOutput,\n    userland: E_ankkorwoo_ankkor_src_app_api_graphql_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/graphql/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgraphql%2Froute&page=%2Fapi%2Fgraphql%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgraphql%2Froute.ts&appDir=E%3A%5Cankkorwoo%5Cankkor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cankkorwoo%5Cankkor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/graphql/route.ts":
/*!**************************************!*\
  !*** ./src/app/api/graphql/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n/**\n * GraphQL Proxy to handle CORS issues when connecting to WooCommerce\n * This endpoint forwards GraphQL requests to the WordPress site and handles CORS\n */ async function POST(request) {\n    try {\n        // Get the GraphQL query from the request body\n        const body = await request.json();\n        // WordPress/WooCommerce GraphQL endpoint from env variables\n        const graphqlEndpoint = process.env.WOOCOMMERCE_GRAPHQL_URL || \"https://lightpink-eagle-376738.hostingersite.com/graphql\";\n        // Get the origin for CORS\n        const origin = request.headers.get(\"origin\") || \"\";\n        // Prepare headers for the request to WooCommerce\n        const headers = {\n            \"Content-Type\": \"application/json\",\n            \"Accept\": \"application/json\"\n        };\n        // Forward session token if present in the request\n        const sessionHeader = request.headers.get(\"woocommerce-session\");\n        if (sessionHeader) {\n            headers[\"woocommerce-session\"] = sessionHeader;\n        }\n        // Forward cookies if present\n        const cookie = request.headers.get(\"cookie\");\n        if (cookie) {\n            headers[\"cookie\"] = cookie;\n        }\n        // Forward the request to WordPress GraphQL\n        const response = await fetch(graphqlEndpoint, {\n            method: \"POST\",\n            headers,\n            body: JSON.stringify(body),\n            credentials: \"include\"\n        });\n        // Get the response data\n        const data = await response.json();\n        // Prepare response headers - use actual origin instead of wildcard for credentials support\n        const responseHeaders = {\n            \"Access-Control-Allow-Origin\": origin,\n            \"Access-Control-Allow-Methods\": \"POST, OPTIONS\",\n            \"Access-Control-Allow-Headers\": \"Content-Type, Authorization, woocommerce-session, cookie\",\n            \"Access-Control-Allow-Credentials\": \"true\",\n            \"Vary\": \"Origin\"\n        };\n        // Forward session token from WooCommerce response if present\n        const responseSessionHeader = response.headers.get(\"woocommerce-session\");\n        if (responseSessionHeader) {\n            responseHeaders[\"woocommerce-session\"] = responseSessionHeader;\n        }\n        // Forward any cookies from the WordPress response\n        const setCookieHeader = response.headers.get(\"set-cookie\");\n        if (setCookieHeader) {\n            responseHeaders[\"set-cookie\"] = setCookieHeader;\n        }\n        // Return the response with CORS headers\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data, {\n            status: response.status,\n            headers: responseHeaders\n        });\n    } catch (error) {\n        console.error(\"GraphQL proxy error:\", error);\n        // Get the origin for error response\n        const origin = request.headers.get(\"origin\") || \"\";\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            errors: [\n                {\n                    message: error instanceof Error ? error.message : \"Unknown error occurred\"\n                }\n            ]\n        }, {\n            status: 500,\n            headers: {\n                \"Access-Control-Allow-Origin\": origin,\n                \"Access-Control-Allow-Methods\": \"POST, OPTIONS\",\n                \"Access-Control-Allow-Headers\": \"Content-Type, Authorization, woocommerce-session, cookie\",\n                \"Access-Control-Allow-Credentials\": \"true\",\n                \"Vary\": \"Origin\"\n            }\n        });\n    }\n}\n/**\n * Handle OPTIONS requests for CORS preflight\n */ async function OPTIONS(request) {\n    // Get the origin for CORS\n    const origin = request.headers.get(\"origin\") || \"\";\n    return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n        status: 204,\n        headers: {\n            \"Access-Control-Allow-Origin\": origin,\n            \"Access-Control-Allow-Methods\": \"POST, OPTIONS\",\n            \"Access-Control-Allow-Headers\": \"Content-Type, Authorization, woocommerce-session, cookie\",\n            \"Access-Control-Allow-Credentials\": \"true\",\n            \"Access-Control-Max-Age\": \"86400\",\n            \"Vary\": \"Origin\"\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/graphql/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgraphql%2Froute&page=%2Fapi%2Fgraphql%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgraphql%2Froute.ts&appDir=E%3A%5Cankkorwoo%5Cankkor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cankkorwoo%5Cankkor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();