(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9160],{81667:function(e,s,a){Promise.resolve().then(a.bind(a,75292))},75292:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return i}});var n=a(57437);a(2265);var d=a(30166);let c=()=>(0,n.jsx)("div",{className:"animate-pulse flex space-x-4",children:(0,n.jsxs)("div",{className:"flex-1 space-y-6 py-1",children:[(0,n.jsx)("div",{className:"h-2 bg-[#e5e2d9] rounded"}),(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,n.jsx)("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-2"}),(0,n.jsx)("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-1"})]}),(0,n.jsx)("div",{className:"h-2 bg-[#e5e2d9] rounded"})]})]})}),l=(0,d.default)(()=>Promise.all([a.e(5540),a.e(4822),a.e(8787),a.e(8261),a.e(1539),a.e(2188),a.e(6003),a.e(7231),a.e(2323),a.e(9960),a.e(7696),a.e(8002),a.e(7111),a.e(8989),a.e(5717),a.e(8496),a.e(9429),a.e(8966),a.e(3903),a.e(6076),a.e(4596),a.e(62),a.e(6271),a.e(8726),a.e(8133),a.e(8049),a.e(2870),a.e(632),a.e(6613),a.e(9965),a.e(5270),a.e(2647),a.e(9971),a.e(9290),a.e(6459),a.e(7800),a.e(4818),a.e(1729),a.e(5526),a.e(4615),a.e(7406),a.e(1225),a.e(2068),a.e(6518),a.e(4595),a.e(3280),a.e(8790),a.e(1104),a.e(7158),a.e(7044),a.e(9482),a.e(986),a.e(4754),a.e(7909)]).then(a.bind(a,27909)),{loadableGenerated:{webpack:()=>[27909]},ssr:!1,loading:()=>(0,n.jsx)(c,{})});function i(){return(0,n.jsx)("div",{className:"container mx-auto py-20",children:(0,n.jsx)(l,{})})}}},function(e){e.O(0,[5540,4822,8787,8261,1539,2188,6003,7231,2323,9960,7696,8002,7111,8989,5717,8496,9429,8966,3903,6076,4596,62,6271,8726,8133,8049,2870,1744],function(){return e(e.s=81667)}),_N_E=e.O()}]);